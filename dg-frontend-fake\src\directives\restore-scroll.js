let scrollTop = 0, ctx, target
let storeScroll = (e) => {
  scrollTop = e.target.scrollTop
}
let restoreScroll = () => {
  // TODO: 改进恢复时机
  setTimeout(() => {
    target.scrollTop = scrollTop
  }, 100)
}

// 路由切换保存滚动位置（目前只适用el-table）
export default {
  bind(el, binding, vnode) {
    const selector = binding.value
    if (selector && el.querySelector(selector)) {
      el = el.querySelector(selector)
    }
    target = el
    target.addEventListener('scroll', storeScroll)
    ctx = vnode.componentInstance || vnode.context
    let activated = ctx.$options['activated']
    if (activated && Array.isArray(activated)) {
      activated.push(restoreScroll)
    } else {
      ctx.$options['activated'] = [restoreScroll]
    }
  },
  unbind(el, binding, vnode) {
    target.removeEventListener('scroll', storeScroll)
  }
}