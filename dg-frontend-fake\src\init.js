import 'core-js/stable'
import 'regenerator-runtime/runtime'
const path = require('path')

import Vue from 'vue'
import './plugins/element.js'
import './plugins/echarts.js'
import './plugins/contextmenu.js'
import './http/axios'
import './icons'
import './assets/style/common/index.scss'
import './router/permission.js'


// components
import BaseCard from './components/Card'
import EmptyWrapper from './components/EmptyWrapper'
import Pagination from './components/Pagination'
import OverflowText from './components/OverflowText'
Vue.component('BaseCard', BaseCard)
Vue.component('EmptyWrapper', EmptyWrapper)
Vue.component('Pagination', Pagination)
Vue.component('OverflowText', OverflowText)

// directives
const directiveModules = require.context('./directives', false, /\.js$/)
const directives = directiveModules.keys().map((key) => {
  const module = directiveModules(key)
  module.name = path.basename(key, '.js')
  return module
})
directives.forEach(directive => Vue.directive(directive.name, directive.default))