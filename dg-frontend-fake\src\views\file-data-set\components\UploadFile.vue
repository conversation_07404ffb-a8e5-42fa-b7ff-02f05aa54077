<template>
  <el-upload
    class="uploader"
    ref="upload"
    :on-change="handleChange"
    drag
    action=""
    :file-list="fileList"
    :auto-upload="false"
    multiple>
    <i class="el-icon-upload"></i>
    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
  </el-upload>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      fileList: [],
    }
  },
  computed: {},
  methods: {
    handleChange(file, fileList) {
      this.$emit('change', fileList)
    },
  },
}
</script>

<style lang="scss" scoped>
.uploader ::v-deep {
  width: 100%;
  .el-upload {
    width: 100%;
    .el-upload-dragger {
      width: 100%;
    }
  }
}
</style>
