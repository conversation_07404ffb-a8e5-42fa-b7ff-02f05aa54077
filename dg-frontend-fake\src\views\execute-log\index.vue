<template>
  <base-card
    id="execute-log"
    class="y-page">
    <div class="y-bar header">
      <h2 class="y-title">{{ overrideTitle || 'ETL任务执行记录' }}</h2>
      <el-button
        v-if="formData.etlResult === '0'"
        class="mini"
        type="primary"
        plain
        @click.native="exportList">
        <svg-icon icon="exit"></svg-icon>
        数据导出
      </el-button>
    </div>
    <div class="y-bar search-bar">
      <span>数据生成时间</span>
      <el-date-picker
        v-model="dateRange"
        type="datetimerange"
        size="small"
        value-format="yyyy-MM-dd HH:mm:ss"
        @change="dateChange"
        :picker-options="pickerOptions"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :default-time="['00:00:00', '23:59:59']"
        style="width: 340px">
      </el-date-picker>
      <span>作业状态</span>
      <el-select
        v-model="formData.etlStatus"
        placeholder="请选择作业状态"
        clearable
        size="small"
        style="width: 160px">
        <el-option
          label="已完成"
          value="1"></el-option>
        <el-option
          label="执行中"
          value="0"></el-option>
        <el-option
          label="全部"
          value=""></el-option>
      </el-select>
      <span>作业结果</span>
      <el-select
        v-model="formData.etlResult"
        placeholder="请选择作业结果"
        clearable
        size="small"
        style="width: 160px">
        <el-option
          label="成功"
          value="1"></el-option>
        <el-option
          label="失败"
          value="0"></el-option>
        <el-option
          label="全部"
          value=""></el-option>
      </el-select>
      <el-input
        v-model="formData.key"
        v-trim
        placeholder="请输入关键词进行搜索"
        size="small"
        clearable
        style="width: 220px"></el-input>
      <el-button
        class="mini"
        type="primary"
        size="small"
        plain
        @click.native="resetSearch">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
    </div>
    <div class="y-container--tight">
      <el-table
        :data="orderList"
        v-loading="tableLoading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <el-table-column
          type="index"
          label="序号"
          width="50">
        </el-table-column>
        <!-- <el-table-column
          label="中转ID"
          prop="TRANSFER_ID">
        </el-table-column> -->
        <el-table-column
          label="作业开始时间"
          prop="BEGIN_TIME"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="作业结束时间"
          prop="END_TIME"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="数据生成时间"
          prop="ROW_DATE"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="数据集"
          prop="SRC_DS_NAME"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="目标源"
          prop="TAG_DS_NAME"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="数据抽取方式"
          prop="SRC_DS_TYPE"
          show-overflow-tooltip>
          <template v-slot="scope">
            <span>{{ scope.row.SRC_DS_TYPE === 'ds' ? '数据库连接方式' : 'EXCEL文件方式' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="作业日志"
          prop="SYNC_FAIL_DESC"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="作业状态"
          prop="ETL_STATUS">
          <template v-slot="scope">
            <el-tag
              size="small"
              :type="scope.row.ETL_STATUS == 0 ? 'primary' : 'success'"
              >{{ scope.row.ETL_STATUS == 0 ? '执行中' : '已完成' }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          label="作业结果"
          prop="ETL_RESULT">
          <template v-slot="scope">
            <el-tag
              size="small"
              :type="getResult(scope.row.ETL_RESULT, 'type')"
              >{{ getResult(scope.row.ETL_RESULT, 'text') }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="50"
          fixed="right">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :underline="false"
              @click.native="openDetail(scope.row)"
              >详情</el-link
            >
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
    <el-drawer
      title="详细信息"
      :visible.sync="detailVisible"
      direction="rtl"
      :size="1200">
      <detail-table
        ref="detailTable"
        :data="detailData"
        @close-detail="closeDetail"></detail-table>
    </el-drawer>
  </base-card>
</template>

<script>
import { getExecuteList, exportList } from '@/api/etl-log'
import DetailTable from './components/DetailTable'

export default {
  name: 'ExecuteLog',
  components: {
    DetailTable,
  },
  props: {},
  data() {
    return {
      overrideTitle: '',
      dateRange: [],
      total: 0,
      tableLoading: false,
      formData: {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
        key: '',
        logId: '',
        startTime: '',
        endTime: '',
        etlResult: '',
        etlStatus: '',
      },
      orderList: [],
      pickerOptions: {
        // 禁止选中今日之后的日期
        disabledDate(time) {
          const now = new Date()
          const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
          return time.getTime() > todayEnd.getTime()
        },
      },
      detailVisible: false,
      detailData: null,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.handleRouteData()
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.tableLoading = true
      const [err, res] = await getExecuteList(this.formData)
      if (res) {
        this.orderList = res.data
        this.total = res.totalRow
        this.$nextTick(() => {
          this.$refs.table.doLayout()
        })
      }
      this.tableLoading = false
    },
    handleRouteData() {
      let { etlResult = '' } = this.$route.query
      let { logId, overrideTitle } = this.$route.params
      Object.assign(this.formData, { logId, etlResult })
      this.overrideTitle = overrideTitle
    },
    async exportList() {
      const payload = { ...this.formData }
      delete payload.pageIndex
      delete payload.pageSize
      delete payload.pageType
      const [err, res] = await exportList(payload)
      if (!err) {
        this.$message({
          message: '正在导出中，请移步系统管理-导出记录进行查看',
          type: 'success',
          duration: 800,
          onClose: () => {},
        })
      }
    },
    dateChange() {
      if (!this.dateRange || this.dateRange?.length === 0) {
        this.formData.startTime = ''
        this.formData.endTime = ''
        return false
      }
      const [begin, end] = this.dateRange
      this.formData.startTime = begin
      this.formData.endTime = end
    },
    getResult(result, key) {
      const map = {
        text: {
          0: '失败',
          1: '成功',
          2: '部分成功',
        },
        type: {
          0: 'danger',
          1: 'success',
          2: 'primary',
        },
      }
      return map[key][result]
    },
    resetSearch() {
      this.dateRange = []
      this.formData = {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
        key: '',
        logId: this.formData.logId,
        startTime: '',
        endTime: '',
        etlResult: '',
        etlStatus: '',
      }
      this.fetchData()
    },
    openDetail(data) {
      this.detailData = data
      this.detailVisible = true
    },
    closeDetail() {
      this.detailVisible = false
    },
  },
}
</script>

<style lang="scss">
#execute-log {
  .header {
    border-bottom: 1px solid $borderColor;
  }

  .search-bar {
    padding-top: 16px;
    justify-content: flex-start;
  }

  > .y-container--tight {
    padding-bottom: 0;
    border-bottom: 1px solid $borderColor;

    .el-table::before {
      content: none;
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }
}
</style>
