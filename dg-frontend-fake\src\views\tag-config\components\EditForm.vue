<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="90px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="标签名称"
            prop="name">
            <el-input
              v-model="formData.name"
              v-trim
              clearable
              placeholder="请输入标签名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <h4
        class="y-title--secondary"
        style="margin-bottom: 16px">
        规则配置
      </h4>
      <section class="rule-config">
        <div
          class="rule-config_item"
          v-for="(item, idx) in formData.rules">
          <div class="header">
            <span>条件</span>
            <span>关系</span>
            <span>条件</span>
            <span>关系</span>
            <el-link
              v-if="idx !== 0"
              type="danger"
              :underline="false"
              @click.native="removeRule(idx)"
              >删除</el-link
            >
          </div>
          <div class="body">
            <input-tag v-model="item.tags1"></input-tag>
            <el-select
              v-model="item.relation1"
              placeholder="请选择关系">
              <el-option
                v-for="(item, idx) in relations"
                :label="item"
                :value="idx"></el-option>
            </el-select>
            <el-select
              v-model="item.relation2"
              placeholder="请选择关系">
              <el-option
                v-for="(item, idx) in relations"
                :label="item"
                :value="idx"></el-option>
            </el-select>
            <input-tag v-model="item.tags2"></input-tag>
            <el-select
              v-model="item.relation3"
              placeholder="请选择关系">
              <el-option
                v-for="(item, idx) in relations"
                :label="item"
                :value="idx"></el-option>
            </el-select>
          </div>
        </div>
        <div
          class="rule-config_add"
          @click="addRule">
          <i class="el-icon-plus"></i><span>添加规则配置</span>
        </div>
      </section>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { getTreeNode, addUser, updateUser } from '@/api/auth.js'

export default {
  components: {
    'input-tag': () => import('@/components/InputTag'),
  },
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      formData: {
        name: '',
        rules: [
          {
            tags1: [],
            relation1: null,
            relation2: null,
            tags2: [],
            relation3: null,
          },
        ],
      },
      rules: {
        name: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
      },
      relations: ['无', '且', '或', '但不属于'],
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      // this.$refs?.editForm?.resetFields()
      // if (this.data && JSON.stringify(this.data) !== '{}') {
      //   this.formData = { ...this.data }
      //   this.formData.classifyId = this.data.parentList
      //   delete this.formData.parentList
      // } else {
      //   this.formData = {}
      // }
      // if (this.$refs?.editForm) {
      //   this.$refs.editForm.clearValidate()
      // }
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        // let submitAction = addUser
        // if (this.data) {
        //   submitAction = updateUser
        // }

        // const [err, res] = await submitAction(this.formData)
        // if (!err) {
        //   this.$message({
        //     message: '操作成功！',
        //     type: 'success',
        //     duration: 800,
        //     onClose: () => {
        //       this.$emit('close-edit')
        //       this.$emit('update-data')
        //     },
        //   })
        // }
      })
    },
    addRule() {
      this.formData.rules.push({
        tags1: [],
        relation1: null,
        relation2: null,
        tags2: [],
        relation3: null,
      })
    },
    removeRule(idx) {
      this.formData.rules.splice(idx, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;

  .rule-config {
    width: 100%;
  }

  .rule-config_item {
    border-radius: 4px;
    border: 1px solid $borderColor;
    overflow: hidden;

    .header,
    .body {
      display: grid;
      grid-auto-rows: auto;
    }

    .header {
      position: relative;
      grid-template-columns: 2fr 2fr 2fr 1fr;
      align-items: center;
      padding: 8px 16px;
      background-color: transparentize($themeColor, 0.95);

      span {
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        color: $txtColor-light;
      }

      .el-link {
        position: absolute;
        right: 16px;
      }
    }

    .body {
      padding: 16px;
      gap: 0 8px;
      grid-template-columns: 2fr 1fr 1fr 2fr 1fr;

      > div {
        width: 100%;
      }
    }

    + .rule-config_item {
      margin-top: 16px;
    }
  }

  .rule-config_add {
    @include flex-center;
    margin-top: 16px;
    width: 100%;
    height: 56px;
    color: $themeColor;
    border-radius: 4px;
    background-color: transparentize($themeColor, 0.95);
    border: 1px dashed $themeColor;
    cursor: pointer;

    i {
      margin-left: 4px;
    }
  }

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
