<template>
  <div class="config-card">
    <div class="info-wrapper">
      <slot name="left"></slot>
    </div>
    <div class="config-wrapper">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConfigCard',
  components: {},
  props: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
}
</script>

<style lang="scss">
.config-card {
  @include flex-row;
  align-items: flex-start;
  padding: 16px 24px;
  padding-bottom: 24px;
  width: 100%;
  min-width: 802px;
  height: 156px;
  border-radius: 4px;
  background: url('@/assets/images/config-card-bg.svg') $bgColor no-repeat left/cover;
  overflow: hidden;

  > .info-wrapper {
    @include flex-col;
    align-items: flex-start;
    flex: 1;
    height: 100%;
  }

  > .config-wrapper {
    @include flex-col;
    align-items: flex-end;
    flex: 0 0 auto;
    height: 100%;
  }

  .title {
    @include text-overflow(1);
    font-size: 20px;
    font-weight: bold;
    line-height: 30px;
    color: $txtColor;
  }

  .desc {
    @include text-overflow(1);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: $txtColor;
  }

  .tags {
    @include flex-row;
    justify-content: flex-start;

    .tag {
      padding: 8px 16px;
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      color: $txtColor-light;
      border-radius: 4px;
      background-color: transparentize($themeColor, 0.95);

      + .tag {
        margin-left: 8px;
      }
    }
  }

  .btn-set {
    @include flex-row;
    justify-content: flex-end;
  }

  .prefix {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: $txtColor-light;
  }
}
</style>
