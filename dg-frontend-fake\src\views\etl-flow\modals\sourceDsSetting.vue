<template>
  <el-drawer
    :size="1000"
    v-bind="$attrs"
    v-on="$listeners"
    :title="title">
    <div class="y-container no-padding">
      <div class="y-container--tight no-padding">
        <el-form
          :disabled="!editable"
          class="y-container--tight"
          style="flex: 0 0 max-content; padding-top: 24px"
          ref="elForm"
          :model="formData"
          size="medium"
          label-width="100px">
          <template v-if="node.dsType == 'excel'">
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="选择Excel"
                  prop="dsId">
                  <el-select
                    filterable
                    v-model="formData.dsId"
                    @change="onDsIdChange"
                    placeholder="请选择Excel"
                    :style="{ width: '100%' }">
                    <el-option
                      v-for="(item, index) in showDsList"
                      :key="index"
                      :label="item.dsName"
                      :value="item.dsId"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template v-else-if="node.dsType === 'ds'">
            <el-row v-if="node.dsTypeFlag === 'file'">
              <el-col :span="24">
                <el-form-item
                  label="文件数据源"
                  prop="dataset">
                  <el-select
                    filterable
                    @change="onDatasetChange"
                    v-model="formData.dataset"
                    placeholder="请选择"
                    multiple
                    :style="{ width: '100%' }">
                    <el-option
                      v-for="(item, index) in fileDatasetDict"
                      :key="index"
                      :label="item.name"
                      :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <template v-if="!node.dsTypeFlag">
              <el-row>
                <el-col :span="24">
                  <el-form-item
                    label="关系数据源"
                    prop="dsId">
                    <el-select
                      filterable
                      @change="onDsIdChange"
                      v-model="formData.dsId"
                      placeholder="请选择"
                      :style="{ width: '100%' }">
                      <el-option
                        v-for="(item, index) in showDsList"
                        :key="index"
                        :label="item.dsName"
                        :value="item.dsId"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item
                    label="数据源来源"
                    prop="dataResource">
                    <el-radio-group
                      @change="onDataResourceChange"
                      v-model="formData.dataResource"
                      size="medium">
                      <el-radio-button
                        key="1"
                        :value="1"
                        :label="1"
                        >SQL</el-radio-button
                      >
                      <el-radio-button
                        key="2"
                        :value="2"
                        :label="2"
                        >表</el-radio-button
                      >
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="formData.dataResource == 1">
                <el-col :span="24">
                  <el-form-item
                    label="SQL"
                    prop="sql">
                    <el-input
                      type="textarea"
                      v-model="formData.dataResourceObj"
                      clearable
                      :style="{ width: '100%' }"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-else>
                <el-col :span="24">
                  <el-form-item
                    label="选择表"
                    prop="table">
                    <el-select
                      @change="onDsTabelChange"
                      filterable
                      v-model="formData.dataResourceObj"
                      placeholder="请选择表"
                      clearable
                      :style="{ width: '100%' }">
                      <el-option
                        v-for="(item, index) in tableList"
                        :key="index"
                        :label="item.tableName"
                        :value="item.tableName"
                        >{{ item.tableName }}</el-option
                      >
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="数据抽取模式"
                  prop="extractMode">
                  <el-radio
                    v-model="formData.extractMode"
                    label="0"
                    >全量</el-radio
                  >
                  <el-radio
                    v-model="formData.extractMode"
                    label="1"
                    >增量</el-radio
                  >
                </el-form-item>
              </el-col>
            </el-row>
            <el-button
              v-if="!node.dsTypeFlag && formData.dataResource == 1"
              type="primary"
              size="small"
              plain
              @click="onSqlChange"
              style="align-self: flex-end"
              >查询</el-button
            >
          </template>
        </el-form>
        <div class="y-container--tight no-padding">
          <el-tabs
            type="border-card"
            class="tabs-full no-padding">
            <el-tab-pane label="数据预览">
              <el-table
                :data="tableData"
                border
                stripe
                height="100%"
                fit
                style="width: 100%">
                <el-table-column
                  v-for="{ columnLabel, column } in showMultipleSelection"
                  :key="column"
                  :prop="column"
                  align="center"
                  min-width="132"
                  show-overflow-tooltip>
                  <template
                    slot="header"
                    slot-scope="scope">
                    <overflow-text
                      :max="8"
                      :content="columnLabel || column"></overflow-text>
                  </template>
                  <template slot-scope="scope">
                    <span>{{ scope.row[column] || '-' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="字段展示">
              <el-form
                :disabled="!editable"
                style="width: 100%; height: 100%">
                <el-table
                  ref="multipleTable"
                  :data="formData.columns"
                  height="100%"
                  fit
                  style="width: 100%; border: 1px solid #e8e8e8"
                  @selection-change="handleSelectionChange">
                  <el-table-column
                    prop="checked"
                    type="selection"
                    width="50">
                    <template slot-scope="scope">
                      <el-checkbox v-model="scope.row.show"></el-checkbox>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="序号"
                    type="index"
                    width="50">
                  </el-table-column>
                  <el-table-column
                    prop="column"
                    label="字段名称"
                    :width="node.dsType == 'excel' ? 100 : 150"
                    show-overflow-tooltip>
                  </el-table-column>
                  <el-table-column
                    prop="columnLabel"
                    label="字段说明"
                    width="220">
                    <template slot-scope="scope">
                      <el-input
                        v-model="scope.row.columnLabel"
                        placeholder="请输入内容"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-if="node.dsType == 'excel'"
                    prop="mapColumn"
                    label="字段映射"
                    width="120">
                    <template slot-scope="scope">
                      <el-input
                        v-model="scope.row.mapColumn"
                        placeholder="请输入内容"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="columnType"
                    label="字段类型">
                    <template slot-scope="scope">
                      {{ VALUE_TYPES[scope.row.columnType] }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-if="node.dsType !== 'excel'"
                    prop="orderColumn"
                    label="排序">
                    <template slot-scope="scope">
                      <el-radio
                        class="no-label"
                        :value="scope.row.orderColumn"
                        @change="onColumnRadioChange(scope.$index, 'orderColumn')"
                        :label="true"></el-radio>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column
                    prop="syncColumn"
                    label="增量同步字段">
                    <template slot-scope="scope">
                      <el-radio
                        class="no-label"
                        :value="scope.row.syncColumn"
                        @change="onColumnRadioChange(scope.$index, 'syncColumn')"
                        :label="true"></el-radio>
                    </template>
                  </el-table-column> -->
                  <el-table-column
                    prop="defaultValue"
                    label="缺省值">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.defaultValue"></el-input>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="footer y-bar">
        <el-button
          type="primary"
          plain
          size="small"
          @click.native="close"
          >取消</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="handelConfirm"
          >保存</el-button
        >
      </div>
    </div>
  </el-drawer>
</template>
<script>
import {
  dsDict,
  excelDsDict,
  fileDsDict,
  getSysDsTableList,
  getExcelDsHeadConfig,
  getDsSrcTableConfig,
  getSysDsHeadConfig,
  getExcelDsPreviewDatas,
  getSysDsPreviewDatas,
} from '@/api/etl-flow'
import { getList } from '@/api/file-data-set'
import { VALUE_TYPES } from '@/utils/constant'

export default {
  inheritAttrs: false,
  components: {},
  props: ['node', 'data', 'editable'],
  data() {
    return {
      formData: {
        dsId: undefined,
        dsType: this.node.dsType,
        dsTypeFlag: this.node.dsTypeFlag,
        dataset: [],
        dataResource: 1,
        dataResourceObj: undefined,
        extractMode: '0',
        columns: [],
      },
      preDataResourceObj: undefined, //预存上次dataResourceObj
      // 预览数据
      tableData: [],
      // 已选列数据
      dsDict: [], //关系数据源字典
      excelDsDict: [], //excel数据源字典
      fileDsDict: [], //文件数据源字典
      fileDatasetDict: [], //文件数据集字典
      dataList: [],
      tableList: [],
      VALUE_TYPES,
    }
  },
  computed: {
    title() {
      return this.node.dsType === 'excel'
        ? 'Excel数据源-节点配置'
        : this.node.dsType === 'ds'
        ? this.node.dsTypeFlag === 'file'
          ? '文件数据源-节点配置'
          : '关系数据源-节点配置'
        : ''
    },
    showDsList() {
      if (this.node.dsType === 'ds') {
        return this.dsDict
      } else if (this.node.dsType === 'excel') {
        return this.excelDsDict
      } else {
        return []
      }
    },
    showMultipleSelection() {
      return this.formData.columns.filter((item) => {
        return item.show == true
      })
    },
  },
  watch: {},
  created() {
    this.init()
  },
  methods: {
    init() {
      this.node.dsType === 'excel'
        ? this.getExcelDsDict()
        : this.node.dsType === 'ds'
        ? this.node.dsTypeFlag === 'file'
          ? this.getFileDsDict() && this.getFileDatasetDict()
          : this.getDsDict()
        : null

      if (this.data) {
        this.formData = JSON.parse(JSON.stringify(this.data))
        if (this.formData.dsId) {
          this.getDsTable(this.formData.dsId)
        }

        this.getPreview2()
      }
    },
    getPreview2() {
      if (this.formData.dsId) {
        if (this.node.dsType == 'excel') {
          this.previewDatas({ dsId: this.formData.dsId }, 'excel')
        } else {
          const data = {
            dsId: this.formData.dsId,
            resourceType: this.formData.dataResource == 1 ? 'sql' : 'table',
            resourceObj: this.formData.dataResourceObj,
          }
          this.previewDatas(data)
        }
      }
    },
    getDsDict() {
      dsDict().then(([err, res]) => {
        let list = res.data || []
        this.dsDict = list
      })
    },
    async getExcelDsDict() {
      const [err, res] = await excelDsDict()
      if (res) {
        let list = res.data || []
        this.excelDsDict = list
      }
    },
    async getFileDsDict() {
      const payload = {
        pageIndex: 1,
        pageSize: 999,
        pageType: 3,
      }
      const [err, res] = await fileDsDict(payload)
      if (res) {
        this.fileDsDict = res.data || []
        if (this.node.dsType === 'ds' && this.node.dsTypeFlag === 'file') {
          this.formData.dsId = this.fileDsDict[0]?.DS_ID
        }
      }
    },
    async getFileDatasetDict() {
      const payload = {
        pageIndex: 1,
        pageSize: 999,
        pageType: 3,
      }
      const [err, res] = await getList(payload)
      if (res) {
        let list =
          res.data.map((item) => ({
            ...item,
            id: item.ID,
            name: item.NAME,
          })) || []
        this.fileDatasetDict = list
      }
    },
    onColumnRadioChange(index, prop) {
      for (var i = 0; i < this.formData.columns.length; i++) {
        this.$set(this.formData.columns[i], prop, i === index)
      }
    },
    getDsTable(dsId) {
      if (typeof dsId === 'object') {
        return false
      }
      let data = { dsId: dsId, dsType: 'src' }
      getSysDsTableList(data).then(([err, res]) => {
        this.tableList = res.data
      })
    },
    onDsIdChange(dsId) {
      console.log('onDsIdChange', dsId)
      if (dsId) {
        this.$set(this.formData, 'dataResourceObj', undefined)
        this.$set(this.formData, 'columns', [])
        this.tableData = []
        if (this.node.dsType === 'excel') {
          this.getExcelRow(dsId)
        } else if (this.node.dsType === 'ds') {
          this.getDsTable(dsId)
        }
      }
    },
    onDatasetChange() {
      if (this.formData.dataset.length === 0) {
        return false
      }
      this.formData.dataResourceObj = `SELECT * FROM dg_file_data_item where FILE_DATA_SET_ID in (${this.formData.dataset.map(i=>`'${i}'`).join()})`
      this.onSqlChange()
    },
    onDataResourceChange(dataResource) {
      this.formData.dataResource = dataResource
      if (dataResource) {
        ;[this.preDataResourceObj, this.formData.dataResourceObj] = [this.formData.dataResourceObj, this.preDataResourceObj]

        if (this.formData.dataResourceObj) {
          if (this.formData.dataResource == 1) {
            this.onSqlChange()
          } else {
            this.onDsTabelChange()
          }
        } else {
          this.$set(this.formData, 'columns', [])
          this.tableData = []
        }
      }
    },
    getExcelRow(dsId) {
      getExcelDsHeadConfig({ dsId }).then(([err, res]) => {
        if (res?.data?.headConfig) {
          this.$set(this.formData, 'columns', res.data.headConfig)
        }
      })
      this.previewDatas({ dsId: dsId }, 'excel')
    },
    onDsTabelChange() {
      let data = {
        dsId: this.formData.dsId,
        tableName: this.formData.dataResourceObj,
        resourceObj: this.formData.dataResourceObj,
        resourceType: 'table',
      }
      getDsSrcTableConfig(data).then(([err, res]) => {
        if (res.data.length > 0) {
          this.$set(this.formData, 'columns', res.data)
          this.tableData = []
        }
      })
      this.previewDatas(data)
    },
    onSqlChange() {
      if (!this.formData.dataResourceObj) return
      let data = { dsId: this.formData.dsId, resourceType: 'sql', resourceObj: this.formData.dataResourceObj }
      getSysDsHeadConfig(data).then(([err, res]) => {
        if (res.data.headConfig) {
          this.$set(this.formData, 'columns', res.data.headConfig)
        }
      })
      this.previewDatas(data)
    },
    close() {
      this.$emit('update:visible', false)
    },
    handelConfirm() {
      console.log('handelConfirm', this.formData)
      // 选中行校验
      let selection = this.formData.columns.filter((item) => item.show)
      if (selection.length <= 0) {
        this.$message.warning('请选中字段进行保存')
        return false
      }
      if (this.node.dsType === 'ds' && selection.every((item) => !item.orderColumn)) {
        this.$message.warning('选中字段中需勾选排序')
        return false
      }
      if (this.node.dsType === 'excel' && selection.some((item) => !item.mapColumn)) {
        this.$message.warning('选中字段需输入字段映射')
        return false
      }

      this.$emit('update', this.node, this.formData)
      this.close()
    },
    handleSelectionChange(selection) {
      console.log('选中', selection)
      this.formData.columns.forEach((item) => {
        if (selection.includes(item)) {
          item.show = true
        } else {
          item.show = false
        }
      })
    },
    toggleSelection(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row)
        })
      } else {
        this.$refs.multipleTable.clearSelection()
      }
    },
    previewDatas(data, type) {
      if (this.node.dsType == 'excel' || type == 'excel') {
        getExcelDsPreviewDatas(data).then(([err, res]) => {
          this.tableData = res.data || []
        })
      } else {
        getSysDsPreviewDatas(data).then(([err, res]) => {
          this.tableData = res.data || []
        })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.tabs-full ::v-deep {
  .el-tabs__content {
    padding: 0;
  }
}

.el-radio::v-deep {
  &.no-label {
    .el-radio__label {
      display: none;
    }
  }
}
</style>
