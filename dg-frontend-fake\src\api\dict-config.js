import { get, post } from '@/http/request'
import { dictConfig } from '@/api/PATH'

export function getDictList(formData) {
  return post(dictConfig.dictList, formData)
}

export function getDictDetail(formData) {
  return get(dictConfig.dictDetail, formData)
}

export function getDictItemList(formData) {
  return post(dictConfig.dictItemList, formData)
}

export function getDictItemDetail(formData) {
  return get(dictConfig.dictItemDetail, formData)
}

export function addDict(formData) {
  return get(dictConfig.addDict, { data: JSON.stringify(formData) })
}

export function updateDict(formData) {
  return get(dictConfig.updateDict, { data: JSON.stringify(formData) })
}

export function deleteDict(id) {
  return get(dictConfig.deleteDict, { data: JSON.stringify({ ID: id }) })
}

export function addDictItem(formData) {
  return get(dictConfig.addDictItem, { data: JSON.stringify(formData) })
}

export function updateDictItem(formData) {
  return get(dictConfig.updateDictItem, { data: JSON.stringify(formData) })
}

export function deleteDictItem(formData) {
  return get(dictConfig.deleteDictItem, { data: JSON.stringify(formData) })
}

export function syncRedis() {
  return post(dictConfig.syncRedis)
}