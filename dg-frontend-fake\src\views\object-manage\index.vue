<template>
  <base-card
    id="object-manage"
    class="y-page">
    <aside-bar
      ref="asideBar"
      @set-current="setCurrent($event)"></aside-bar>
    <div class="y-container no-padding">
      <div class="y-bar">
        <h2 class="y-title">诉求对象列表</h2>
        <span>关键字</span>
        <el-input
          v-model="keyword"
          v-trim
          size="small"
          style="width: 220px"
          clearable
          placeholder="请输入关键字进行搜索"></el-input>
        <el-button
          v-debounce="fetchData"
          class="mini"
          type="primary">
          <i class="el-icon-search"></i>
          搜索
        </el-button>
        <el-button
          class="mini"
          type="primary"
          @click="downloadTemplate">
          <i class="el-icon-download"></i>
          下载模板
        </el-button>
        <el-button
          class="mini"
          type="primary"
          @click.native="openEdit(null)">
          <svg-icon icon="add"></svg-icon>
          新增对象
        </el-button>
        <el-upload
          ref="upload"
          action=""
          accept=".xlsx, .xls"
          :auto-upload="false"
          :show-file-list="false"
          :multiple="false"
          :on-change="importObject">
          <el-button
            class="mini"
            type="primary"
            plain
            style="margin-left: 8px">
            <svg-icon icon="enter"></svg-icon>
            批量导入
          </el-button>
        </el-upload>
        <!-- <el-button
          class="mini"
          type="primary">
          <svg-icon icon="robot-plus"></svg-icon>
          智能生成
        </el-button> -->
      </div>
      <empty-wrapper
        class="y-container--tight"
        :toggle="!objectList || objectList?.length === 0"
        v-loading="loading">
        <div
          class="object-item-wrapper y-card-wrapper y-container--tight no-padding"
          v-reset-scroll>
          <object-card
            v-for="item in objectList"
            :key="item.APPEAL_INFO_ID"
            :data="item"
            :active-menu="activeMenu"
            @edit-object="openEdit(item)"
            @delete-object="deleteObject(item)"></object-card>
        </div>
      </empty-wrapper>
      <div class="footer">
        <pagination
          :current-page.sync="formData.pageIndex"
          :page-size.sync="formData.pageSize"
          :total="total"
          @page="fetchData"></pagination>
      </div>
    </div>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        :active-menu="activeMenu"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getObjectList, importObject, deleteObject } from '@/api/object-manage'
import AsideBar from './components/AsideBar.vue'
import ObjectCard from './components/ObjectCard.vue'
import EditForm from './components/EditForm.vue'
import { downloadFile } from '@/utils'

export default {
  name: 'ObjectManage',
  components: {
    AsideBar,
    ObjectCard,
    EditForm,
  },
  props: {},
  data() {
    return {
      loading: false,
      activeMenu: null,
      keyword: '',
      total: 0,
      formData: {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
      },
      objectList: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {},
  watch: {
    activeMenu: {
      handler(menu) {
        if (menu || menu === 0) {
          this.formData = {
            pageSize: 15,
            pageIndex: 1,
            pageType: 3,
          }
          this.fetchData()
        }
      },
      deep: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true

      const payload = {
        appealName: this.keyword,
        appealType: this.activeMenu[0],
      }
      Object.assign(payload, this.formData)
      const [err, res] = await getObjectList(payload)
      if (res) {
        this.objectList = res.data
        this.total = res.totalRow
      }

      this.loading = false
    },
    async importObject(file, fileList) {
      if (
        file.raw.type !== 'application/vnd.ms-excel' &&
        file.raw.type !== 'application/x-excel' &&
        file.raw.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ) {
        this.$message({
          message: '文件类型不正确，请重新上传',
          type: 'error',
          duration: 800,
        })
        return false
      }

      const payload = {
        appealType: this.activeMenu?.[0],
        file: file.raw,
      }

      const [err, res] = await importObject(payload)
      if (!err && res?.msg.includes('成功')) {
        this.$message({
          message: res.msg,
          type: 'success',
          duration: 3000,
          onClose: () => {
            this.fetchData()
          },
        })
      } else if (!err) {
        this.$message({
          message: res.msg,
          type: 'error',
          duration: 3000,
          onClose: () => {},
        })
      }
    },
    async deleteObject(data) {
      try {
        await this.$confirm('此操作将永久删除选中诉求对象, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const payload = {
        appealId: data.APPEAL_INFO_ID,
        contentBefore: data,
      }
      const [err, res] = await deleteObject(payload)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    openEdit(data) {
      if (data) {
        this.editDrawerTitle = '编辑诉求对象'
        this.editDrawerData = data
      } else {
        this.editDrawerTitle = '新增诉求对象'
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    downloadTemplate() {
      // downloadFile('/dg-portal/template/template_appeal_info.xlsx')
      downloadFile('/dg-portal/servlet/appeal?action=ExportTemplate')
    },
    setCurrent(menu) {
      this.activeMenu = menu
    },
  },
}
</script>

<style lang="scss">
#object-manage {
  @include flex-row;
  background-color: transparent;

  > .aside-bar {
    margin-right: 16px;
    border-radius: 4px;
    background-color: $bgColor;
  }

  > .y-container {
    width: calc(100% - 304px);
    min-width: 685px;

    .y-bar {
      justify-content: flex-start;
      background-color: $bgColor;
      border-radius: 4px;
    }
  }

  .object-item-wrapper {
    margin-top: 16px;
    padding-bottom: 16px;
    grid-auto-rows: 186px;
    grid-template-columns: repeat(auto-fill, minmax(416px, 1fr));
  }

  .footer {
    width: 100%;
    background-color: $bgColor;
    border-radius: 4px;

    .base-pagination {
      float: right;
      padding: 16px 8px;
    }
  }
}
</style>
