<template>
  <el-drawer
    :size="800"
    v-bind="$attrs"
    v-on="$listeners"
    title="空值处理节点配置">
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight no-padding"
        :disabled="!editable">
        <el-table
          :data="tableData"
          style="width: 100%"
          :height="'100%'">
          <el-table-column
            label="序号"
            type="index"
            width="50">
          </el-table-column>

          <el-table-column label="字段名称">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.column"
                placeholder="请选择">
                <el-option
                  v-for="(item, key) in currentCols"
                  :key="key"
                  :value="item.column">
                  {{ item.column }} ({{ item.columnLabel }})
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="NULL值处理">
            <template slot-scope="scope">
              <el-radio
                v-model="scope.row.rule"
                label="1"
                >设定内容为空字符串</el-radio
              >
              <el-radio
                v-model="scope.row.rule"
                label="2"
                >指定值</el-radio
              >
            </template>
          </el-table-column>

          <el-table-column label="">
            <template slot-scope="scope">
              <el-input
                v-show="scope.row.rule == 2"
                v-model="scope.row.value"
                placeholder="请输入内容"></el-input>
            </template>
          </el-table-column>

          <el-table-column
            width="80"
            align="center">
            <template slot="header">
              <el-button
                @click="addRow"
                type="primary"
                icon="el-icon-plus"
                style="padding: 8px 12px"></el-button>
            </template>
            <template slot-scope="scope">
              <el-link
                type="danger"
                :underline="false"
                :disabled="!editable"
                @click.native="handleDelete(scope.$index, scope.row)"
                >删除</el-link
              >
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="footer y-bar">
        <el-button
          type="primary"
          plain
          size="small"
          @click.native="close"
          >取消</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="handelConfirm"
          >保存</el-button
        >
      </div>
    </div>
  </el-drawer>
</template>
<script>
export default {
  inheritAttrs: false,
  components: {},
  props: ['node', 'data', 'currentCols', 'editable'],

  data() {
    return {
      tableData: [],
    }
  },
  computed: {},
  watch: {},
  created() {
    if (this.data) {
      this.tableData = JSON.parse(JSON.stringify(this.data))
    }
  },
  methods: {
    addRow() {
      this.tableData.push({
        column: '',
        rule: '1',
        value: '',
      })
    },
    close() {
      this.$emit('update:visible', false)
    },
    handelConfirm() {
      // 校验
      if (this.tableData.length <= 0) {
        this.$message.warning( '请添加至少一个字段进行保存' )
        return false
      }
      if (this.tableData.some((item) => !item.column)) {
        this.$message.warning( '添加字段需选择字段名称' )
        return false
      }
      if (this.tableData.some((item) => item.rule == 2 && !item.value.trim())) {
        this.$message.warning( '请为指定值输入内容' )
        return false
      }

      this.$emit('update', this.node, this.tableData)
      this.close()
    },
    handleDelete(index, row) {
      if (!this.editable) {
        return false
      }
      this.tableData.splice(index, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
