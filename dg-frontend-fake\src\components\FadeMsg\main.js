import Vue from 'vue';
import component from './index.vue';
// import { PopupManager } from 'element-ui/src/utils/popup';

let FadeMsgConstructor = Vue.extend(component);

let instance;
let instances = [];
let seed = 1;

const FadeMsg = function (options) {
  let id = 'fadeMsg_' + seed++;

  let userOnClose = options.onClose;

  options.onClose = function () {
    FadeMsg.close(id, userOnClose);
  };


  instance = new FadeMsgConstructor({
    data: options
  });

  instance.id = id;

  instance.$mount();
  document.body.appendChild(instance.$el);
  let verticalOffset = options.offset || 20;
  instances.forEach(item => {
    verticalOffset += item.$el.offsetHeight + 16;
  });
  instance.verticalOffset = verticalOffset;
  instance.visible = true;
  // instance.$el.style.zIndex = PopupManager.nextZIndex();
  instances.push(instance);
  return instance;
};

['success', 'warning', 'info', 'error'].forEach(type => {
  FadeMsg[type] = (options) => {
    if (typeof options === 'string') {
      return FadeMsg({
        type,
        message: options
      });
    } else {
      return FadeMsg({
        ...options,
        type
      });
    }
  };
});

FadeMsg.close = function (id, userOnClose) {
  let len = instances.length;
  let index = -1;
  let removedHeight;
  for (let i = 0; i < len; i++) {
    if (id === instances[i].id) {
      removedHeight = instances[i].$el.offsetHeight;
      index = i;
      if (typeof userOnClose === 'function') {
        userOnClose(instances[i]);
      }
      instances.splice(i, 1);
      break;
    }
  }
  if (len <= 1 || index === -1 || index > instances.length - 1) return;
  for (let i = index; i < len - 1; i++) {
    let dom = instances[i].$el;
    dom.style['top'] =
      parseInt(dom.style['top'], 10) - removedHeight - 16 + 'px';
  }
};

FadeMsg.closeAll = function () {
  for (let i = instances.length - 1; i >= 0; i--) {
    instances[i].close();
  }
};
export default FadeMsg;
