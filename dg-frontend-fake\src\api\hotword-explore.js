import { get, post } from '@/http/request'
// 新发热词

export function getList(formData) {
  return post('/dg-portal/webcall?action=hotWords.newlist', { data: JSON.stringify(formData) })
}

export function checkHotword(formData) {
  return post('/dg-portal/servlet/hotWords?action=newBatchAudit', { data: JSON.stringify(formData) })
}

export function updateHotword(formData) {
  return post('/dg-portal/servlet/hotWords?action=newUpdate', { data: JSON.stringify(formData) })
}

export function deleteHotword(formData) {
  return post('/dg-portal/servlet/hotWords?action=newBatchDel', { data: JSON.stringify(formData) })
}

export function getTypeDict(formData) {
  return post('/dg-portal/webcall?action=hotWords.newHotWordTypeDict', { data: JSON.stringify(formData) })
}