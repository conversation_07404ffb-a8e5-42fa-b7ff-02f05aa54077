import Vue from 'vue'
import axios from 'axios'
import { handleRequestHeader, handleAuth, handleNetworkError, handleUnauthorized, handleAuthError, handleGeneralError, handleErrMsg, handleTimeout } from './handlers.js'
// import store from '@/store'
// import { getToken } from '@/utils/auth'

// Full config:  https://github.com/axios/axios#request-config

const config = {
  baseURL: process.env.VUE_APP_BASE_API || '',
  timeout: 20 * 1000, // Timeout
}

// 高封装
// 使用方式如下
// const [err,res] = await api(params)
// 或者
// api(params).then(([err,res])=>{})
const service = axios.create(config)

service.interceptors.request.use(
  (config) => {
    // config = handleRequestHeader(config)
    // config = handleAuth(config)
    // console.log(config)
    return config
  },
  (error) => {
    // console.log(error)
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  (response) => {
    // console.log('axios-response', response)
    if (response.status !== 200) return Promise.reject(response)
    if (response.data === 'Request action not exsit.') {
      handleErrMsg(response.data)
      return Promise.reject(response)
    }
    // if (response?.headers?.['content-type'] === 'text/html') {
    // }
    const flag = handleGeneralError(response.data?.state, response.data?.msg)
    if (!flag) return Promise.reject(response)
    return response.data
  },
  (error) => {
    // console.log('axios-response-error', error)
    if (error?.code === 'ERR_NETWORK' || error?.response?.status === 401) {
      handleAuthError(error?.response)
    } else if (error?.code === 'ECONNABORTED' && error?.message.includes('timeout')) {
      handleTimeout()
    } else {
      handleNetworkError(error?.response?.status)
    }
    return Promise.reject(error)
  }
)


// 简单封装，直接用调$axios即可
const _axios = axios.create(config)

_axios.interceptors.request.use(
  (config) => {
    // config = handleRequestHeader(config)
    // config = handleAuth(config)
    // console.log(config)
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)


Object.defineProperties(Vue.prototype, {
  $api: {
    get() {
      return service
    }
  },
  $axios: {
    get() {
      return _axios
    }
  },
})

export default service