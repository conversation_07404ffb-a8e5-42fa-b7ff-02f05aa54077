<template>
  <div
    v-if="!data || data?.length === 0"
    :key="0"
    class="y-container--tight no-padding">
    <el-empty
      :image="require('@/assets/images/no-data.png')"
      description="暂无数据"></el-empty>
  </div>
  <div
    v-else
    :key="1"
    class="relation-chart y-container no-padding"
    ref="chart-container"></div>
</template>

<script>
import chartMixins from '@/mixins/chartMixins.js'

export default {
  components: {},
  mixins: [chartMixins],
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {}
  },
  computed: {
    labelList() {
      return this.data.map((item) => item.DG_APPEAL_MOTIF)
    },
    dataList() {
      return this.data.map((item) => item.APPEAL_MOTIF_TOTAL)
    },
    option() {
      return {
        grid: {
          top: '15%',
          bottom: '13%',
          left: '2%',
          right: '0%',
          containLabel: true,
        },
        legend: {
          orient: 'horizontal',
          top: 'top',
          left: 'right',
          icon: 'roundRect',
          itemGap: this.nowSize(20),
          itemWidth: this.nowSize(24),
          itemHeight: this.nowSize(24),
          textStyle: {
            fontSize: this.nowSize(20),
          },
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#e8e8e8',
              },
            },
            axisLabel: {
              margin: this.nowSize(10),
              color: '#868686',
              fontSize: this.nowSize(16),
            },
            axisTick: {
              show: false,
            },
            data: this.labelList,
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '单数',
            nameLocation: 'end',
            nameGap: this.nowSize(20),
            nameTextStyle: {
              fontSize: this.nowSize(16),
              color: '#868686',
              padding: [0, this.nowSize(50), 0, 0],
            },
            min: 0,
            splitNumber: 6,
            splitLine: {
              show: true,
              lineStyle: {
                color: '#e8e8e8',
                type: 'dashed',
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: this.nowSize(10),
              color: '#868686',
              fontSize: this.nowSize(16),
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: '单数',
            type: 'line',
            symbol: 'circle',
            showAllSymbol: true,
            symbolSize: this.nowSize(12),
            lineStyle: {
              normal: {
                color: '#0555CE',
              },
            },
            label: {
              show: true,
              position: 'top',
              textStyle: {
                fontSize: this.nowSize(18),
                fontWeight: 700,
                color: '#0555CE',
              },
            },
            itemStyle: {
              normal: {
                color: '#0555CE',
              },
            },
            tooltip: {
              show: true,
            },
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(5, 85, 206, 0.25)',
                    },
                    {
                      offset: 0.4,
                      color: 'rgba(5, 85, 206, 0.25)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(216, 216, 216, 0)',
                    },
                  ],
                  false
                ),
              },
            },
            data: this.dataList,
          },
        ],
        tooltip: {
          confine: true,
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#0555CE',
            },
          },
          textStyle: {
            fontSize: this.nowSize(16),
          },
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0], //表示x轴折叠
            height: this.nowSize(14),
            bottom: '5%',
            borderRadius: this.nowSize(7),
            borderColor: 'none',
            fillerColor: '#E8E8E8',
            backgroundColor: '#F2F4F7',
            moveHandleSize: 0,
          },
        ],
      }
    },
  },
  methods: {},
}
</script>

<style lang="scss">
.relation-chart {
}
</style>
