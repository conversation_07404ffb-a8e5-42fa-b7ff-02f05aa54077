<template>
  <div class="aside-bar">
    <div class="y-bar header">
      <h2 class="y-title">组织架构树</h2>
      <svg-icon
        icon="add"
        @click.native="openDialog(null)"></svg-icon>
    </div>
    <div class="y-container--tight no-padding">
      <el-tree
        ref="tree"
        node-key="id"
        :data="treeData"
        v-loading="tableLoading"
        :indent="0"
        :props="treeProps"
        default-expand-all
        @current-change="handleCurrentChange">
        <div
          slot-scope="{ node, data }"
          class="node-wrapper">
          <div class="node-label">
            <svg-icon
              v-show="getNodeIcon(node, 'icon')"
              :class="['node-icon', getNodeIcon(node, 'icon') ? 'fill' : '']"
              :icon="getNodeIcon(node, 'icon')"></svg-icon>
            <span class="node-name">{{ node.label }}</span>
          </div>
          <el-dropdown
            trigger="click"
            placement="top">
            <div class="node-more">
              <svg-icon
                icon="more"
                v-show="data.id === activeMenu"></svg-icon>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                icon="el-icon-edit"
                @click.native="openDialog(data)"
                >编辑</el-dropdown-item
              >
              <el-dropdown-item
                icon="el-icon-delete"
                @click.native="deleteClass(data)"
                >删除</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-tree>
    </div>
    <!-- <div class="y-bar footer">
      <div
        class="btn-delete">
        <i class="el-icon-delete"></i>
        <span>回收站</span>
      </div>
    </div> -->
    <el-dialog
      :title="dialogTitle"
      width="500px"
      :visible.sync="dialogShow">
      <el-form
        ref="editForm"
        class="clearfix"
        :model="editForm"
        :rules="rules"
        label-position="right"
        label-width="80px">
        <el-col :span="24">
          <el-form-item
            label="单位名称"
            prop="orgName">
            <el-input
              v-model="editForm.orgName"
              v-trim
              maxlength="30"
              show-word-limit
              clearable
              placeholder="请输入单位名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="上级单位"
            prop="pOrgId">
            <el-cascader
              ref="cascader"
              v-model="editForm.pOrgId"
              :options="treeData"
              placeholder="请选择上级单位"
              style="width: 100%"
              :show-all-levels="false"
              :props="cascaderProps"
              @change="handleChange"></el-cascader>
          </el-form-item>
        </el-col>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button
          v-debounce="submitForm"
          type="primary"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getClassTree, getClassList, addClass, updateClass, deleteClass } from '@/api/csrc-info'

export default {
  components: {},
  data() {
    return {
      activeMenu: null,
      keyword: '',
      tableLoading: false,
      treeProps: {
        label: 'name',
      },
      treeData: [],
      dialogTitle: '',
      dialogType: '',
      dialogShow: false,
      editForm: { orgName: null, pOrgId: null },
      contentBefore: null,
      rules: {
        orgName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
        pOrgId: [{ required: true, message: '请选择上级单位', trigger: 'change' }],
      },
      cascaderProps: {
        value: 'id',
        label: 'name',
        emitPath: false,
        checkStrictly: true,
      },
    }
  },
  computed: {},
  watch: {
    activeMenu(menu) {
      if (menu || menu === 0) {
        this.$emit('set-current', menu)
      }
    },
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData(id) {
      this.tableLoading = true
      const [err, res] = await getClassTree(id || '')
      if (res) {
        this.treeData = [res.data]
        this.activeMenu = res.data?.id
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.activeMenu)
        })
      }
      this.tableLoading = false
    },
    async deleteClass(data) {
      try {
        await this.$confirm('此操作将永久删除当前组织, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }
      const payload = {
        orgId: this.activeMenu,
        contentBefore: data,
      }
      const [err, res] = await deleteClass(payload)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const payload = this.editForm

        let submitAction = addClass
        if (this.dialogType === 'edit') {
          submitAction = updateClass
          payload.contentBefore = this.contentBefore
        }

        const [err, res] = await submitAction(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.fetchData()
              this.dialogShow = false
            },
          })
        }
      })
    },
    initDialog(data) {
      this.$refs?.editForm?.resetFields()
      if (data) {
        this.dialogTitle = '编辑单位'
        this.dialogType = 'edit'
        this.editForm = { orgName: data.name, pOrgId: data.P_ORG_ID, orgId: data.id }
        this.contentBefore = data
      } else {
        this.dialogTitle = '新增单位'
        this.dialogType = 'add'
        this.editForm = { orgName: null, pOrgId: this.activeMenu }
        this.contentBefore = null
      }
    },
    openDialog(data) {
      this.initDialog(data)
      this.dialogShow = true
    },
    closeDialog() {
      this.dialogShow = false
    },
    handleCurrentChange(data, node) {
      this.activeMenu = data.id
    },
    handleChange() {
      this.$refs.cascader.dropDownVisible = false
    },
    getNodeIcon(node, type) {
      const map = {
        icon: ['minus-round', 'plus-round'],
      }
      if (node.childNodes.length > 0) {
        if (node.expanded) {
          return map[type][0]
        } else {
          return map[type][1]
        }
      } else {
        return ''
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.aside-bar::v-deep {
  @include flex-col;
  flex-shrink: 0;
  width: 240px;
  height: 100%;

  .y-title {
    line-height: 24px;
  }

  > .header {
    padding: 16px 24px;
    padding-bottom: 8px;
    border-bottom: 1px solid $borderColor;

    .svg-icon {
      font-size: 16px;
      color: $txtColor-light;
      cursor: pointer;

      &:hover {
        color: $themeColor;
      }
    }
  }

  > .y-container--tight {
    padding: 12px 16px;
  }

  .el-tree {
    min-width: 100%;

    .el-tree-node {
      position: relative;
      // padding-left: 16px; // 缩进量

      // 竖线
      &::before {
        content: '';
        height: 100%;
        width: 1px;
        position: absolute;
        left: -9px;
        top: -26px;
        border-width: 1px;
        border-left: 1px dashed $txtColor-slight;
      }

      // 当前层最后一个节点的竖线高度固定
      &:last-child::before {
        height: 49px; // 可以自己调节到合适数值
      }

      // 横线
      &::after {
        content: '';
        width: 17px;
        height: 20px;
        position: absolute;
        left: -9px;
        top: 22px;
        border-width: 1px;
        border-top: 1px dashed $txtColor-slight;
      }
    }

    .el-tree-node__children {
      padding-left: 24px; // 缩进量
      overflow: visible;
    }

    // 去掉最顶层的虚线
    & > .el-tree-node::after {
      border-top: none;
    }

    & > .el-tree-node::before {
      border-left: none;
    }

    .el-tree-node__expand-icon {
      // 展开图标不要
      display: none;
    }

    // 自定义节点样式
    .node-wrapper {
      @include flex-row;
      padding: 0 8px;
      width: 100%;
      height: 48px;

      .node-label {
        @include flex-row;
      }

      .node-icon {
        position: relative;
        font-size: 16px;
        color: $txtColor-light;
        cursor: pointer;
        z-index: 2;

        &.fill {
          background-color: $bgColor;
        }
      }

      .node-name {
        @include text-overflow(1);
        display: inline-block;
        margin-left: 8px;
        max-width: 100px;
        font-size: 16px;
        font-weight: normal;
        line-height: 24px;
        color: $txtColor;
      }

      .node-more {
        .svg-icon {
          font-size: 16px;
          color: $themeColor;
        }
      }
    }

    // 自定义节点样式
    .el-tree-node {
      .el-tree-node__content {
        width: 100%;
        height: max-content;
        border-radius: 4px;
      }

      &.is-current > .el-tree-node__content,
      .el-tree-node__content:hover {
        color: $txtColor;
        background: transparentize($themeColor, 0.95);

        .node-name {
          color: $themeColor;
        }

        .node-icon {
          color: $themeColor;
          background-color: #f2f6fd;
        }
      }
    }
  }

  // > .footer {
  //   border-top: 1px solid $borderColor;

  //   .btn-delete {
  //     font-size: 14px;
  //     font-weight: normal;
  //     line-height: 22px;
  //     color: $txtColor;
  //     cursor: pointer;

  //     i {
  //       margin-right: 4px;
  //       font-size: 16px;
  //       color: $txtColor-light;
  //     }

  //     &:hover {
  //       color: $themeColor;

  //       i {
  //         color: $themeColor;
  //       }
  //     }
  //   }
  // }
}
</style>
