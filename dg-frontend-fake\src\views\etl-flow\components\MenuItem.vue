<template>
  <div>
    <el-menu-item
      v-for="(item, idx) in menu.module"
      :index="pidx + '-m' + idx"
      draggable
      @dragstart.native="$emit('drag-item', item)">
      <i
        v-if="item.icon"
        :class="item.icon"></i>
      <img
        v-else
        :src="require(`@/assets/images/i${classify}.png`)"
        height="20"
        style="margin-right: 10px" />
      <span
        slot="title"
        style="color: #868686"
        >{{ item.name }}</span
      >
    </el-menu-item>
    <el-submenu
      v-for="(item, idx) in menu.children"
      class="sub-menu"
      :index="pidx + '-c' + idx">
      <template slot="title">
        <i class="el-icon-minus"></i>
        <span>{{ item.name }}</span>
      </template>
      <menu-item
        :menu="item"
        :classify="classify"
        :pidx="pidx + '-c' + idx"
        v-on="$listeners"></menu-item>
    </el-submenu>
  </div>
</template>

<script>
export default {
  name: 'MenuItem',
  props: {
    menu: Object,
    classify: [String, Number],
    pidx: String,
  },
  created() {},
  mounted() {},
  components: {},
  data() {
    return {}
  },
  computed: {},
  methods: {},
}
</script>

<style lang="scss" scoped></style>
