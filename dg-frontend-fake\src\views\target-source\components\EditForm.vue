<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="原数据源名称"
            prop="TAG_DS_NAME">
            <el-input
              v-model="formData.TAG_DS_NAME"
              v-trim
              placeholder="请输入原数据源名称"
              maxlength="30"
              clearable
              show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="系统数据源名称"
            prop="SYS_DS_NAME">
            <el-select
              v-model="formData.SYS_DS_NAME"
              placeholder="请选择系统数据源"
              @change="handleDsChange">
              <el-option
                v-for="item in systemList"
                :value="item"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="24">
          <el-form-item
            label="数据类型"
            prop="TAG_DS_TYPE">
            <el-select
              v-model="formData.TAG_DS_TYPE"
              placeholder="请选择数据类型">
              <el-option
                label="数据库"
                value="ds"></el-option>
              <el-option
                label="EXCEL"
                value="excel"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="启用状态"
            prop="TAG_DS_STATE">
            <el-switch
              v-model="formData.TAG_DS_STATE"
              active-color="#52C41A"
              active-value="1"
              inactive-value="0">
            </el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="数据源表"
            prop="tableList">
            <el-select
              placeholder="请选择数据源表"
              value=""
              multiple>
              <el-option
                class="table-option"
                v-for="item in formData.tableList"
                :key="item.tableName"
                :value="item.tableName">
                <span>{{ item.tableName }}</span>
                <el-checkbox v-model="item.checkBox"></el-checkbox>
              </el-option>
            </el-select>
            <div
              class="tag-wrapper"
              v-if="checkedTables.length > 0">
              <el-tag
                class="tag-item"
                v-for="item in checkedTables"
                :key="item.tableName"
                closable
                type="primary"
                @close="removeCheck(item)">
                {{ item.tableName }}
              </el-tag>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { addSource, updateSource, getSystemList, getTableList } from '@/api/source'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    const validateCheckedTables = (rule, value, callback) => {
      if (this.checkedTables.length === 0) {
        callback('请选择数据源表')
      } else {
        callback()
      }
    }

    return {
      formData: {
        // TAG_DS_TYPE: null,
        SYS_DS_NAME: null,
        TAG_DS_NAME: null,
        TAG_DS_STATE: '0',
        TAG_VISIBLE_TABLE: null,
        tableList: [],
      },
      rules: {
        TAG_DS_NAME: [{ required: true, message: '请输入原数据源名称', trigger: 'blur' }],
        SYS_DS_NAME: [
          { required: true, message: '请选择系统数据源名称', trigger: 'change' },
          // { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '数据名称不合法，只能含有字母、数字或下划线且不以数字开头！', trigger: 'blur' },
        ],
        // TAG_DS_TYPE: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
        TAG_DS_STATE: [{ required: true, message: '请选择是否启用', trigger: 'change' }],
        tableList: [{ required: true, validator: validateCheckedTables, trigger: 'blur' }],
      },
      systemList: [],
    }
  },
  computed: {
    checkedTables() {
      return this.formData.tableList.filter((item) => item.checkBox)
    },
  },
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data && JSON.stringify(this.data) != '{}') {
        Object.assign(this.formData, this.data)
        this.getTableList()
      } else {
        this.formData = {
          // TAG_DS_TYPE: null,
          TAG_DS_NAME: null,
          SYS_DS_NAME: null,
          TAG_DS_STATE: '0',
          TAG_VISIBLE_TABLE: null,
          tableList: [],
        }
      }
      this.getSystemList()
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        let submitAction = addSource
        const payload = { ...this.formData }
        payload.TAG_VISIBLE_TABLE = this.checkedTables.map((item) => item.tableName).join(',')
        if (this.data) {
          payload.id = this.data.TAG_DS_ID
          submitAction = updateSource
        }
        delete payload.tableList

        const [err, res] = await submitAction(payload, 'tag')
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
    async getTableList() {
      if (!this.formData.SYS_DS_NAME) {
        return false
      }

      // 新增用sysDsName，编辑回显用dsId，编辑修改了sysDsName则用sysDsName
      let flag = this.data?.SYS_DS_NAME === this.formData.SYS_DS_NAME && this.data?.TAG_DS_ID

      const payload = {
        [flag ? 'dsId' : 'sysDsName']: flag ? this.data?.TAG_DS_ID : this.formData.SYS_DS_NAME,
        dsType: 'tag',
      }
      const [err, res] = await getTableList(payload)
      if (!err) {
        this.$set(this.formData, 'tableList', res.data)
      }
    },
    async getSystemList() {
      const [err, res] = await getSystemList()
      if (!err) {
        this.systemList = res.data
      }
    },
    removeCheck(item) {
      let find = this.formData.tableList.find((table) => table.tableName === item.tableName)
      if (find) {
        find.checkBox = false
      }
    },
    handleDsChange() {
      this.getTableList()
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form::v-deep {
  position: relative;

  // .el-form-item.modify {
  //   &.is-success {
  //     .el-input::after {
  //       content: '\e6da';
  //       position: absolute;
  //       right: 16px;
  //       font-family: element-icons;
  //       font-size: 16px;
  //       color: $color-success;
  //     }

  //     .el-input__inner {
  //       border-color: #eef9e9;
  //       background: #eef9e9;

  //       &:focus {
  //         border-color: $color-success;
  //         box-shadow: 0px 0px 8px 0px transparentize($color-success, 0.4);
  //       }

  //       &:hover {
  //         border-color: $color-success;
  //       }
  //     }
  //   }
  // }

  .tag-wrapper {
    @include flex-col;
    margin-top: 8px;
    padding: 16px;
    border-radius: 4px;
    background: $bgColor-dark;

    .tag-item {
      @include flex-row;
      width: 100%;

      + .tag-item {
        margin-top: 8px;
      }
    }
  }

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}

.table-option {
  @include flex-row;
  padding: 12px 24px;

  &:hover {
    border-left: 3px solid $themeColor;
    background: transparentize($themeColor, 0.95);
  }
}
</style>
