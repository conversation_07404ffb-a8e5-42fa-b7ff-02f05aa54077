<template>
  <div class="aside-bar">
    <div
      class="y-bar tab-bar"
      :style="!fixType ? { paddingBottom: 0 } : { paddingBottom: '10px' }">
      <multi-switch
        v-if="!fixType"
        v-model="type"
        :options="['目标源', '调度']"
        mode="tab"></multi-switch>
      <h2
        v-else-if="fixType === '0'"
        class="y-title">
        目标源
      </h2>
      <h2
        v-else-if="fixType === '1'"
        class="y-title">
        调度
      </h2>
    </div>
    <div class="y-bar search-bar">
      <el-input
        v-model="keyword"
        v-trim
        placeholder="请输入关键字"
        size="small"
        clearable
        suffix-icon="el-icon-search"></el-input>
    </div>
    <class-menu
      v-if="type === '1'"
      v-loading="loading"
      :list="filteredList"
      :active-menu="activeMenu"
      id-prop="0"
      @set-current="setCurrent($event[0])">
      <template v-slot="{ item }">
        <span>{{ item[1] }}</span>
      </template>
    </class-menu>
    <div
      class="y-container--tight"
      v-else>
      <el-tree
        ref="tree"
        node-key="id"
        :data="targetList"
        v-loading="loading"
        :props="treeProps"
        default-expand-all
        :filter-node-method="filterNode"
        style="width: 100%">
        <span
          slot-scope="{ node, data }"
          :class="['tree-node', data.id === activeMenu ? 'is-active' : '']"
          @click="setCurrent(data.id)">
          {{ node.label }}</span
        >
      </el-tree>
    </div>
  </div>
</template>

<script>
import { getTargetList, getTaskList } from '@/api/data-query'
import ClassMenu from '@/components/ClassMenu'
import MultiSwitch from '@/components/MultiSwitch'

export default {
  components: {
    ClassMenu,
    MultiSwitch,
  },
  props: {
    fixType: {
      type: String,
    },
  },
  data() {
    return {
      type: this.fixType || '0',
      activeMenu: '',
      keyword: '',
      targetList: [],
      taskList: [],
      treeProps: {
        label: 'name',
      },
      loading: false,
    }
  },
  computed: {
    filteredList() {
      if (this.keyword.trim()) {
        return this.taskList.filter(([key, value]) => key.indexOf(this.keyword) > -1 || value.indexOf(this.keyword) > -1)
      }
      return this.taskList
    },
  },
  watch: {
    type: {
      handler(type) {
        let activeMenu
        if (type === '0') {
          activeMenu = this.getTreeFirstNode(this.targetList)
          this.$nextTick(() => {
            this.$refs.tree.filter(this.keyword)
          })
        } else {
          activeMenu = this.filteredList[0]?.[0]
        }
        this.setCurrent(activeMenu)
        this.$emit('change-type', type)
      },
      immediate: true,
    },
    keyword(val) {
      if (this.type === '0') {
        this.$refs.tree.filter(val)
      }
    },
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      this.getTargetList()
      this.getTaskList()
      this.loading = false
    },
    async getTargetList() {
      const [err, res] = await getTargetList()
      if (res) {
        this.targetList = this.formatTargetList(res.data)
        this.setCurrent(this.getTreeFirstNode(this.targetList))
      }
    },
    async getTaskList() {
      const [err, res] = await getTaskList()
      if (res) {
        this.taskList = Object.entries(res.data)
      }
    },
    setCurrent(id) {
      this.activeMenu = id
      this.$emit('set-current', id)
    },
    formatTargetList(list) {
      return list.map((item) => {
        const node = {
          id: item.TAG_DS_ID,
          name: item.TAG_DS_NAME,
          ds: item.SYS_DS_NAME,
          children: [],
        }

        for (const key in item.child) {
          node.children.push({
            id: item.TAG_DS_ID + '|' + key,
            name: item.child[key],
          })
        }

        return node
      })
    },
    getTreeFirstNode(list) {
      let firstParent = list.find((parent) => parent.children?.length > 0)
      if (firstParent) {
        return firstParent.children[0].id
      } else {
        return ''
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
  },
}
</script>

<style lang="scss" scoped>
.aside-bar::v-deep {
  @include flex-col;
  flex-shrink: 0;
  width: 288px;
  height: 100%;

  > .tab-bar {
    padding: 12px 24px;
    border-bottom: 1px solid $borderColor;

    .multi-switch {
      margin: 0;
      line-height: 35px;

      .multi-switch_item {
        margin-bottom: 14px;
        font-size: 16px;
        line-height: 28px;
        color: $txtColor-light;

        &.is-active {
          color: $themeColor;

          &::after {
            bottom: -14px;
          }
        }

        + .multi-switch_item {
          margin-left: 48px;
        }
      }
    }
  }

  > .search-bar {
    padding: 16px 24px;

    > * {
      margin: 0;
    }
  }

  .el-tree {
    .el-tree-node {
      &.is-current > .el-tree-node__content,
      .el-tree-node__content:hover {
        background: transparent;
      }
    }

    .tree-node {
      @include full;
      padding: 10px 8px;
      line-height: 16px;
      border-radius: 4px;

      &.is-active {
        background: transparentize($themeColor, 0.95);
      }
    }
  }
}
</style>
