<template>
  <base-card :class="['user-card', type]">
    <div class="header">
      <el-image
        :src="require(`@/assets/images/user-card-icon_${type}.svg`)"
        style="width: 56px; height: 56px"></el-image>
      <div class="info-wrapper">
        <h5 class="name">{{ data.USERNAME }}</h5>
        <p>
          <span class="number">编号：{{ data.USER_ID }}</span>
        </p>
      </div>
    </div>
    <div class="body">
      <span class="prefix">登录账户：</span><span>{{ data.USER_ACCT }}</span> <span class="prefix">角色：</span>
      <div
        v-if="data.ROLES && data.ROLES.length > 0"
        class="role-wrapper">
        <span
          class="role"
          v-for="role in data.ROLES.split(',')"
          v-show="role"
          >{{ role }}</span
        >
      </div>
    </div>
    <div class="btn-set">
      <el-button
        class="mini"
        type="danger"
        plain
        size="small"
        @click="$emit('delete-user')"
        >删除</el-button
      >
      <el-button
        class="mini"
        type="primary"
        size="small"
        @click="$emit('edit-user')"
        >编辑</el-button
      >
    </div>
  </base-card>
</template>

<script>
export default {
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {}
  },
  computed: {
    type() {
      // admin | ordinary
      return 'ordinary'
    },
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
.user-card {
  @include full;
  @include flex-col;
  padding: 24px;
  padding-bottom: 16px;

  &.ordinary {
    background: url('@/assets/images/user-card-bg_ordinary.svg') no-repeat right top/120px 120px, $bgColor;
  }

  &.admin {
    background: url('@/assets/images/user-card-bg_admin.svg') no-repeat right top/120px 120px, $bgColor;
  }

  .header {
    @include flex-row;
    align-self: flex-start;

    .info-wrapper {
      margin-left: 16px;
    }

    .name {
      margin-bottom: 8px;
      font-size: 18px;
      font-weight: 700;
      line-height: 22px;
      color: $txtColor;
    }

    .number {
      font-size: 14px;
      font-weight: normal;
      line-height: 16px;
      color: $txtColor-light;
    }
  }

  .body {
    align-self: flex-start;
    display: grid;
    gap: 4px;
    grid-auto-rows: auto;
    grid-template-columns: max-content 1fr;

    span {
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      color: $txtColor;
    }

    .prefix {
      color: $txtColor-light;
    }

    .role-wrapper {
      display: block;
      max-height: 54px;
      overflow: hidden;
    }

    .role {
      padding: 2px 8px;
      font-size: 12px;
      font-weight: normal;
      line-height: 18px;
      color: $themeColor;
      border-radius: 2px;
      background-color: transparentize($themeColor, 0.95);

      + .role {
        margin-left: 4px;
      }
    }
  }

  .btn-set {
    align-self: flex-end;
  }
}
</style>
