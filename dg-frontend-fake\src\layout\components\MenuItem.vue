<template>
  <div v-if="!item.hidden">
    <template v-if="hasOneShowingChild(item) && (!onlyOneChild.children || onlyOneChild.noShowingChildren) && !item.alwaysShow">
      <el-menu-item
        :index="resolvePath(onlyOneChild.path)"
        :class="{ 'submenu-title-noDropdown': !isNest }"
        @click="handleMenuItemClick(onlyOneChild)"
      >
        <item
          :icon="onlyOneChild?.meta?.icon || (item?.meta && item?.meta?.icon) || (defaultIcon ? 'el-icon-menu' : '')"
          :title="onlyOneChild?.meta?.title" />
      </el-menu-item>
    </template>
    <el-submenu
      v-else
      ref="subMenu"
      :index="resolvePath(item.path)"
      popper-append-to-body>
      <template slot="title">
        <item
          v-if="item.meta"
          :icon="(item.meta && item.meta.icon) || (defaultIcon ? 'el-icon-menu' : '')"
          :title="item.meta.title" />
      </template>
      
      <MenuItem
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu">
      </MenuItem>
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'

export default {
  name: 'MenuItem',
  components: {
    Item: () => import('./Item.vue'),
  },
  props: {
    item: {
      type: Object,
      required: true,
    },
    isNest: {
      type: Boolean,
      default: false,
    },
    basePath: {
      type: String,
      default: '',
    },
    defaultIcon: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.onlyOneChild = null
    return {}
  },
  computed: {},
  methods: {
    hasOneShowingChild(parent) {
      if (parent?.meta?.notPromote) {
        return false
      }

      // if (!parent?.meta?.notPromote && parent?.meta?._blanck) {
      //   window.open('http://*************:18888/apps')
      //   return true
      // }

      if (!parent.children) {
        console.log('parent: ', parent);
        
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }
      const showingChildren = parent.children.filter((item) => {
        if (item.hidden) {
          return false
        } else {
          this.onlyOneChild = item
          return true
        }
      })
      if (showingChildren.length === 1) {
        return true
      }
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }
      return false
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    },
    handleMenuItemClick(menuItem) {
      // 检查是否满足新窗口打开的条件
      if (!menuItem.children && menuItem?.meta?.blanck) {
        // 阻止默认的路由跳转行为
        event.preventDefault()
        event.stopPropagation()
        
        // 在新窗口中打开指定URL
        if (menuItem?.meta?.url) {
          window.open(menuItem.meta.url || 'http://*************:18888/apps')
        }
        return false
      }
      // 对于其他菜单项，让默认的路由行为继续执行
    },
  },
}
</script>

<style lang="scss"></style>
