import { get, post } from '@/http/request'
import { objectManage } from '@/api/PATH'


// 分类
export function getClassList(formData) {
  return post(objectManage.classList, formData)
}
export function addClass(formData) {
  return post(objectManage.addClass, { data: JSON.stringify(formData) })
}

export function updateClass(formData) {
  return post(objectManage.updateClass, { data: JSON.stringify(formData) })
}

export function deleteClass(formData) {
  return post(objectManage.deleteClass, { data: JSON.stringify(formData) })
}

// 诉求对象
export function getObjectList(formData) {
  return post(objectManage.objectList, formData)
}

export function getObjectDetail(id) {
  return post(objectManage.objectDetail, { appealId: id })
}

export function importObject(formData) {
  return post(objectManage.importObject, formData, null, { 'Content-Type': 'multipart/form-data' })
}

export function addObject (formData) {
  return post(objectManage.addObject, { data: JSON.stringify(formData) })
}

export function updateObject(formData) {
  return post(objectManage.updateObject, { data: JSON.stringify(formData) })
}

export function deleteObject(formData) {
  return post(objectManage.deleteObject, { data: JSON.stringify(formData) })
}