<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="90px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="句式描述"
            prop="template">
            <el-input
              v-model="formData.template"
              v-trim
              type="textarea"
              placeholder="请输入句式描述"
              maxlength="200"
              show-word-limit
              clearable
              :autosize="{ minRows: 3, maxRows: 10 }"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <p class="tip">
        <span>配置说明：</span><br />
        <span
          >指用于获取投资者沟通和报告数据的标准化句式结构，以确保信息的清晰、一致和易于识别。这些句式模板提供了一种框架，辅助系统获取工单数据中的投资者信息。</span
        >
      </p>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="是否启用"
            prop="isUsed">
            <el-switch
              v-model="formData.isUsed"
              active-color="#52C41A"
              active-value="1"
              inactive-value="0">
            </el-switch>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { addPattern, updatePattern } from '@/api/pattern-template'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      formData: {
        template: null,
        isUsed: '0',
      },
      rules: {
        template: [{ required: true, message: '请输入句式描述', trigger: 'blur' }],
        isUsed: [{ required: true, trigger: 'blur' }],
      },
    }
  },
  computed: {},
  watch: {
    data: {
      handler(data) {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data && JSON.stringify(this.data) != '{}') {
        this.$set(this.formData, 'template', this.data?.TEMPLATE)
        this.$set(this.formData, 'isUsed', this.data?.IS_USED)
      } else {
        this.formData = {
          template: null,
          isUsed: '0',
        }
      }
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const payload = {
          ...this.formData,
        }

        let submitAction = addPattern
        // 有id为更新，无id为新增
        if (this.data?.INVESTOR_ID) {
          payload.investorId = this.data.INVESTOR_ID
          payload.contentBefore = { ...this.data }
          submitAction = updatePattern
        }

        const [err, res] = await submitAction(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;

  .tip {
    padding-left: 90px;
    padding-bottom: 32px;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    color: $txtColor-light;
  }

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
