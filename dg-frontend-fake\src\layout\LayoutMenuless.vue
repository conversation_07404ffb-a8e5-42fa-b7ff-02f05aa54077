<template>
  <div
    id="y-layout"
    class="clearfix">
    <top-bar></top-bar>
    <div
      class="y-layout_main"
      style="margin: 0; padding: 0;">
      <transition
        appear
        name="fade-transform"
        mode="out-in">
        <keep-alive :include="cachedViews">
          <router-view :key="routeKey"></router-view>
          <slot></slot>
        </keep-alive>
      </transition>
    </div>
  </div>
</template>

<script>
import { debounce } from '@/utils'

export default {
  name: 'layout',
  components: {
    // 框架组件
    'top-bar': () => import('./components/TopBar.vue'),
  },
  data() {
    return {}
  },
  computed: {
    cachedViews() {
      return this.$store.state.tagBar.cachedViews
    },
    routeKey() {
      return this.$route.path
    },
  },
  created() {
    // this.verifySession = debounce(
    //   () => {
    //     this.$store.dispatch('user/getUserInfo')
    //   },
    //   800,
    //   true
    // )
    // document.addEventListener('visibilitychange', this.verifySession)
  },
  beforeDestroy() {
    // document.removeEventListener('visibilitychange', this.verifySession)
  },
  methods: {},
}
</script>

<style lang="scss">
#y-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: $bgColor-dark;

  .y-layout_main {
    flex: 1;
    height: calc(100vh - 64px);
    overflow: auto;
  }

  .flex-col {
    display: flex;
    flex-direction: column;
  }

  .flex-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
}
</style>
