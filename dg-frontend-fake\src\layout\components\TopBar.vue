<template>
  <div class="y-layout_bar">
    <!-- Logo区域 -->
    <div class="y-layout_logo">
      <router-link to="/">
        <h1 id="logo">数据分析工具</h1>
      </router-link>
    </div>
    
    <!-- 水平导航菜单 -->
    <horizontal-menu class="desktop-menu"></horizontal-menu>
    
    <!-- 移动端菜单按钮 -->
    <!-- <div class="mobile-menu-trigger" @click="toggleMobileMenu">
      <svg-icon icon="menu" class="menu-icon"></svg-icon>
    </div> -->
    
    <!-- 右侧工具栏 - 恢复用户信息 -->
    <div class="y-layout_toolbar">
      <el-dropdown trigger="click">
        <div class="y-layout_user flex-row">
          <el-avatar :src="require('@/assets/images/user-card-icon_ordinary.svg')"></el-avatar>
            <div>
              <span class="username">{{ userInfo.USERNAME || '未知用户' }}</span>
              <i class="el-icon-arrow-down"></i>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                icon="el-icon-key"
                @click.native="openConfig('updatePwdDialog')"
                >修改密码</el-dropdown-item
              >
              <el-dropdown-item
                icon="el-icon-switch-button"
                @click.native="logout"
                >退出登陆</el-dropdown-item
              >
            </el-dropdown-menu>
        </div>
      </el-dropdown>
    </div>
    
    <!-- 移动端下拉菜单 -->
    <transition name="mobile-menu">
      <div v-if="mobileMenuVisible" class="mobile-menu-dropdown">
        <div class="mobile-menu-content">
          <el-menu
            :default-active="activeMenu"
            mode="vertical"
            :router="false"
            @select="handleMobileMenuSelect"
            class="mobile-menu">
            <template v-for="route in visibleRoutes">
              <el-submenu
                v-if="route.children && route.children.length > 0"
                :key="route.path"
                :index="route.path">
                <template slot="title">
                  <svg-icon 
                    v-if="route.meta && route.meta.icon" 
                    :icon="route.meta.icon">
                  </svg-icon>
                  <span>{{ route.meta ? route.meta.title : route.name }}</span>
                </template>
                <el-menu-item
                  v-for="child in getVisibleChildren(route.children)"
                  :key="child.path"
                  :index="resolvePath(route.path, child.path)"
                  @click="handleMobileMenuClick(resolvePath(route.path, child.path))">
                  <span>{{ child.meta ? child.meta.title : child.name }}</span>
                </el-menu-item>
              </el-submenu>
              <el-menu-item
                v-else
                :key="route.path"
                :index="route.path"
                @click="handleMobileMenuClick(route.path)">
                <svg-icon 
                  v-if="route.meta && route.meta.icon" 
                  :icon="route.meta.icon">
                </svg-icon>
                <span>{{ route.meta ? route.meta.title : route.name }}</span>
              </el-menu-item>
            </template>
          </el-menu>
        </div>
      </div>
    </transition>

    <config-form ref="configForm"></config-form>
  </div>
</template>

<script>
import { logout } from '@/api/auth.js'
import { mapGetters } from 'vuex'

import HorizontalMenu from './HorizontalMenu.vue'
import scrollHorizontal from '@/directives/scroll-horizontal'

export default {
  name: 'TopBar',
  components: {
    HorizontalMenu,
    'config-form': () => import('./ConfigForm'),
  },
  directives: {
    scrollHorizontal
  },
  data() {
    return {
      mobileMenuVisible: false
    }
  },
  computed: {
    ...mapGetters(['permissionRoutes']),
    userInfo() {
      return this.$store.state.user.userInfo || {}
    },
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    visibleRoutes() {
      return this.permissionRoutes.filter(route => !route.hidden)
    }
  },
  methods: {
    toggleMobileMenu() {
      this.mobileMenuVisible = !this.mobileMenuVisible
    },
    handleMobileMenuSelect(index) {
      // 移动端菜单选择
    },
    handleMobileMenuClick(path) {
      if (path !== this.$route.path) {
        this.$router.push(path)
      }
      this.mobileMenuVisible = false
    },
    getVisibleChildren(children) {
      return children.filter(child => !child.hidden)
    },
    resolvePath(basePath, routePath) {
      if (routePath.charAt(0) === '/') {
        return routePath
      }
      return `${basePath}/${routePath}`.replace(/\/+/g, '/')
    },
    openConfig(type) {
      // TODO: 实现修改密码功能
      console.log('打开配置:', type)
      this.$refs.configForm[type] = true
    },
    async logout() {
      const [err, res] = await logout()

      if (!err) {
        this.$message({
          message: '登出成功',
          type: 'success',
          duration: 800,
          onClose: () => {
            window?.opener?.top?.postMessage({ msg: 'logout' }, '/')
            let close = window.close()
            if (!close) {
              window.location.replace(res?.url || '/')
            }
          },
        })
      }

      // await this.$store.dispatch('user/logout')
      // this.$router.push(`/login?logout=true`)
    },
  },
  mounted() {
    // 点击外部关闭移动端菜单
    document.addEventListener('click', (e) => {
      if (!this.$el.contains(e.target)) {
        this.mobileMenuVisible = false
      }
    })
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/style/common/variables.scss';

.y-layout_bar {
  height: 64px;
  background: $primary-gradient;
  display: flex;
  align-items: center;
  padding: 0 16px;
  position: relative;
  z-index: 1000;
  overflow: visible; // 修改为visible，允许子菜单弹出
  
  .y-layout_logo {
    flex: 0 0 auto;
    margin-right: 24px;
    
    #logo {
      color: white;
      font-size: 20px;
      font-weight: 600;
      margin: 0;
      text-decoration: none;
      white-space: nowrap;
    }
    
    a {
      text-decoration: none;
    }
  }
  
  .desktop-menu {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    display: flex; // 确保flex布局
  }
  
  .mobile-menu-trigger {
    display: none;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
    flex-shrink: 0;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
    
    .menu-icon {
      font-size: 20px;
    }
  }
  
  .y-layout_toolbar {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    margin-left: 16px;
    
    .y-layout_user {
      display: flex;
      align-items: center;
      color: white;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 6px;
      transition: background-color 0.3s ease;
      white-space: nowrap;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
      
      .el-avatar {
        width: 32px;
        height: 32px;
        margin-right: 8px;
        flex-shrink: 0;
      }
      
      .username {
        font-size: 14px;
        font-weight: 500;
        margin-right: 4px;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #fff;
      }
      
      .el-icon-arrow-down {
        font-size: 12px;
        transition: transform 0.3s ease;
      }
      
      &:hover .el-icon-arrow-down {
        transform: rotate(180deg);
      }
    }
  }
  
  .mobile-menu-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e4e7ed;
    border-top: none;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 999;
    
    .mobile-menu-content {
      max-height: 400px;
      overflow-y: auto;
      
      .mobile-menu {
        border: none;
        
        .el-menu-item,
        .el-submenu__title {
          height: 48px;
          line-height: 48px;
          padding-left: 20px;
        }
      }
    }
  }
}

// 移动端菜单动画
.mobile-menu-enter-active,
.mobile-menu-leave-active {
  transition: all 0.3s ease;
}

.mobile-menu-enter,
.mobile-menu-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

// 响应式处理
@media (max-width: 768px) {
  .y-layout_bar {
    .desktop-menu {
      display: none;
    }
    
    .mobile-menu-trigger {
      display: block;
      margin-left: auto;
    }
    
    .y-layout_toolbar {
      margin-left: 8px;
      
      .y-layout_user {
        .username {
          display: none; // 移动端隐藏用户名
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .y-layout_bar {
    .y-layout_logo {
      margin-right: 16px;
      
      #logo {
        font-size: 18px;
      }
    }
    
    .y-layout_toolbar {
      .y-layout_user {
        .username {
          max-width: 80px;
        }
      }
    }
  }
}
</style>
