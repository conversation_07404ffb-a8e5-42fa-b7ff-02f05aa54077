<template>
  <base-card class="training-card">
    <div class="header">
      <el-image
        :src="require('@/assets/images/training-card-icon.svg')"
        style="width: 72px; height: 72px"></el-image>
      <div class="header-wrapper">
        <div>
          <h5 class="title">{{ data.TRAIN_NAME }}</h5>
          <!-- <span class="version">{{ `Ver. ${data.VER || '-'}` }}</span> -->
        </div>
        <span class="date">批次日期：{{ data.CREATE_TIME }}</span>
      </div>
    </div>
    <div class="info-wrapper">
      <span class="prefix">自助ETL名称：</span>
      <span>{{ data.ETL_NAME }}</span>
      <span class="prefix">当前版本：</span>
      <span>{{ data.ETL_VERSION }}</span>
      <span class="prefix">记录数：</span>
      <span>{{ data.RECORD_COUNT }}</span>
      <span class="prefix">第一次数据处理时间：</span>
      <span>{{ data.FIRST_CREATE_TIME }}</span>
      <span class="prefix">最后一次数据处理时间：</span>
      <span>{{ data.LAST_CREATE_TIME }}</span>
      <span class="prefix">备注：</span>
      <span>{{ data.TRAIN_MEMO }}</span>
      <!-- <span class="prefix">第一次准确率：</span>
      <el-tag
        v-if="data.FIRST_SUCC_RATE"
        :type="getAccuracyType(Number(data.FIRST_SUCC_RATE))"
        >{{ getPercent(data.FIRST_SUCC_RATE) }}</el-tag
      >
      <span v-else></span>
      <span class="prefix">最后一次准确率：</span>
      <el-tag
        v-if="data.LAST_SUCC_RATE"
        :type="getAccuracyType(Number(data.LAST_SUCC_RATE))"
        >{{ getPercent(data.LAST_SUCC_RATE) }}</el-tag
      >
      <span v-else></span> -->
    </div>
    <!-- <etl-version-selector
      :etl-id="data.ETL_ID"
      style="align-self: flex-end"></etl-version-selector> -->
    <div class="btn-set">
      <el-button
        class="mini"
        type="primary"
        plain
        key="0"
        @click.native="
          $router.push({
            name: 'TrainingMark',
            params: { etlId: data.ETL_ID, trainDatasetId: data.TRAIN_DATASET_ID, overrideTitle: data.ETL_NAME + '标注' },
            query: { trainBatchId: '' },
          })
        "
        >标注</el-button
      >
      <!-- <el-button
        class="mini"
        type="primary"
        plain
        @click.native="
          $router.push({
            name: 'TrainingHistory',
            params: { trainDatasetId: data.TRAIN_DATASET_ID, overrideTitle: data.ETL_NAME + '训练历史' },
          })
        "
        >历史</el-button
      > -->
      <el-button
        v-if="data.TRAIN_STATE !== '3'"
        v-debounce="{ evtHandler: () => train(data) }"
        class="mini"
        type="primary"
        key="1"
        >启动</el-button
      >
      <template v-else>
        <el-button
          v-debounce="{ evtHandler: () => pauseTrain(data) }"
          class="mini"
          type="danger"
          plain
          key="2"
          >暂停</el-button
        >
        <el-button
          v-debounce="{ evtHandler: () => completeTrain(data) }"
          class="mini"
          type="primary"
          key="3"
          >完成</el-button
        >
      </template>
    </div>
    <corner-tag :type="getStateTag(data.TRAIN_STATE, 'type')">{{ getStateTag(data.TRAIN_STATE, 'txt') }}</corner-tag>
  </base-card>
</template>

<script>
import { train, pauseTrain, completeTrain } from '@/api/data-training'

export default {
  components: {
    'corner-tag': () => import('@/components/CornerTag'),
    // 'etl-version-selector': () => import('@/components/EtlVersionSelector'),
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {}
  },
  computed: {},
  methods: {
    async train() {
      const payload = {
        etlId: this.data.ETL_ID,
        trainDatasetId: this.data.TRAIN_DATASET_ID,
      }
      const [err, res] = await train(payload)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.$emit('update-data')
          },
        })
      }
    },
    async pauseTrain() {
      const [err, res] = await pauseTrain(this.data.TRAIN_DATASET_ID)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.$emit('update-data')
          },
        })
      }
    },
    async completeTrain() {
      const [err, res] = await completeTrain(this.data.TRAIN_DATASET_ID)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.$emit('update-data')
          },
        })
      }
    },
    getStateTag(state, flag) {
      const map = {
        txt: {
          1: '已完成',
          2: '待执行',
          3: '执行中',
          4: '已暂停',
        },
        type: {
          1: 'success',
          2: 'delay',
          3: 'primary',
          4: 'info',
        },
      }
      return map[flag][state]
    },
    getAccuracyType(accuracy) {
      if (accuracy >= 0.85) {
        return 'success'
      } else if (accuracy >= 0.6) {
        return 'warning'
      } else {
        return 'danger'
      }
    },
    getPercent(num) {
      return (Number(num) * 100).toFixed(1) + '%'
    },
  },
}
</script>

<style lang="scss" scoped>
.training-card {
  @include full;
  @include flex-col;
  padding: 24px;

  .header {
    @include flex-row;
    justify-content: flex-start;
    width: 100%;

    .header-wrapper {
      margin-left: 24px;

      .title {
        font-size: 20px;
        font-weight: bold;
        line-height: 30px;
        color: $txtColor;
      }

      .version {
      }

      .date {
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        color: $txtColor-light;
      }
    }
  }

  .info-wrapper {
    display: grid;
    gap: 8px;
    grid-auto-rows: 22px;
    grid-template-columns: 1fr 1fr;
    padding: 16px;
    width: 100%;
    border-radius: 4px;
    background-color: transparentize($themeColor, 0.95);

    span {
      line-height: 22px;
    }

    .prefix {
      color: $txtColor-light;
    }

    .el-tag {
      justify-self: flex-start;
      height: 22px;
    }
  }

  .btn-set {
    align-self: flex-end;
  }
}
</style>
