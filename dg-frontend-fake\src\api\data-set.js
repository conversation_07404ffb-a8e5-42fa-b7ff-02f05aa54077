import { get, post } from '@/http/request'
import { dataSet } from '@/api/PATH'

// Excel数据集
export function getList(formData) {
  return post(dataSet.list, { data: JSON.stringify(formData) })
}

export function getDetail(formData) {
  return post(dataSet.detail, { data: JSON.stringify(formData) })
}

export function getLog(formData) {
  return post(dataSet.log, { data: JSON.stringify(formData) })
}

export function importExcel(formData) {
  return post(dataSet.import, formData, null, { 'Content-Type': 'multipart/form-data' }, { timeout: 3 * 60 * 1000 })
}

export function confirmImport(id) {
  return post(dataSet.confirmImport, { data: JSON.stringify({ fileId: id }) }, null, null, { timeout: 3 * 60 * 1000 })
}

export function cancelImport(id) {
  return post(dataSet.cancelImport, { data: JSON.stringify({ fileId: id }) })
}

export function deleteExcel(id) {
  return post(dataSet.delete, { data: JSON.stringify({ fileId: id }) })
}