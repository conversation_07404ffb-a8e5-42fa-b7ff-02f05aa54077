<template>
  <base-card
    id="self-service-etl"
    class="y-page">
    <div class="y-bar">
      <h2 class="y-title">服务调度</h2>
      <span>关键字</span>
      <el-input
        v-model="formData.key"
        v-trim
        size="small"
        style="width: 220px"
        clearable
        placeholder="请输入关键字进行搜索"></el-input>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
      <el-button
        class="mini"
        type="primary"
        @click.native="openEdit(null)">
        <svg-icon icon="add"></svg-icon>
        新建服务调度
      </el-button>
    </div>
    <empty-wrapper
      class="y-container--tight"
      :toggle="!etlList || etlList?.length === 0"
      v-loading="loading">
      <div
        class="etl-wrapper y-card-wrapper y-container--tight no-padding"
        v-reset-scroll>
        <config-card
          v-for="item in etlList"
          :key="item.etlId"
          :data="item">
          <template #left>
            <div class="title-wrapper">
              <h5 class="title">
                {{ item.etlName }}
              </h5>
              <span
                class="taggy info"
                style="margin-left: 4px"
                >{{ `Ver. ${item.ver || '-'}` }}</span
              >
              <span
                :class="['taggy', getTag(item.status, 'class')]"
                style="margin-left: 8px"
                >{{ getTag(item.status, 'label') }}</span
              >
            </div>
            <p class="desc">
              <span class="prefix">数据源类型：</span><span>{{ item.srcDatasetJson.dsTypeFlag || item.srcDatasetJson.dsType }}</span>
              <span class="prefix"> 数据源：</span>
              <el-tooltip
                v-if="item.srcDatasetJson.dataResource == 1 && !item.srcDsName"
                effect="dark"
                :content="item.srcDatasetJson.dataResourceObj"
                placement="top">
                <span>{{ item.srcDatasetJson.dataResourceObj.slice(0, 15) + '...' }}</span>
              </el-tooltip>
              <span v-else>{{ item.srcDsName || item.srcDatasetJson.dataResourceObj }}</span>
              <span class="prefix"> 目标源：</span><span>{{ item.tarDsName || item.tagDatasetJson.dataResourceObj }}</span>
              <span class="prefix"> 最后执行时间：</span><span>{{ item.lastRuntime || '--' }}</span> <span class="prefix"> 调度：</span
              ><span>{{ getSchedule(item.scheduleJson) }}</span>
            </p>
            <div class="tags">
              <div class="tag">
                最后一次获取记录数：<span class="primary">{{ getNumber(item.srcDataCount) }}</span>
              </div>
              <div class="tag">
                成功记录数：<span class="success">{{ getNumber(item.syncSuccCount) }}</span>
              </div>
              <div class="tag">
                失败记录数：<span class="danger">{{ getNumber(item.syncFailCount) }}</span>
              </div>
            </div>
          </template>
          <template #right>
            <div class="switch">
              <span class="prefix">启用状态：</span>
              <el-switch
                v-model="item.eltState"
                active-color="#52C41A"
                :active-value="1"
                :inactive-value="0"
                @change="handleSwitch(item)">
              </el-switch>
            </div>
            <etl-version-selector :etl-id="item.etlId"></etl-version-selector>
            <div class="btn-set">
              <el-button
                class="mini"
                type="danger"
                plain
                size="small"
                @click.native="deleteEtl(item)"
                >删除</el-button
              >
              <el-button
                class="mini"
                type="primary"
                size="small"
                @click.native="openEdit(item)"
                >配置</el-button
              >
            </div>
          </template>
        </config-card>
      </div>
    </empty-wrapper>
    <div class="footer">
      <pagination
        :current-page.sync="formData.pageIndex"
        :page-size.sync="formData.pageSize"
        :total="total"
        @page="fetchData"></pagination>
    </div>
  </base-card>
</template>

<script>
import { getEtlList, updateEtl, deleteEtl } from '@/api/etl'
import ConfigCard from '@/components/ConfigCard'
import EtlVersionSelector from '@/components/EtlVersionSelector'

export default {
  name: 'SelfServiceEtl',
  components: {
    ConfigCard,
    EtlVersionSelector,
  },
  props: {},
  data() {
    return {
      total: 0,
      loading: false,
      formData: {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
        key: '',
      },
      etlList: [],
    }
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true

      const [err, res] = await getEtlList(this.formData)
      if (res) {
        this.etlList = res.data
        this.total = res.totalRow
      }

      this.loading = false
    },
    async handleSwitch(data) {
      const payload = {
        etlId: data.etlId,
        status: data.eltState,
      }

      const [err, res] = await updateEtl(payload)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {},
        })
      } else {
        this.fetchData()
      }
    },
    async deleteEtl(item) {
      try {
        await this.$confirm('此操作将永久删除该流程, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }
      const [err, res] = await deleteEtl(item.etlId)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    getTag(status, flag) {
      const map = {
        class: {
          0: 'primary',
          1: 'success',
        },
        label: {
          0: '未执行',
          1: '执行中',
        },
      }

      return map[flag][status]
    },
    getNumber(number) {
      if (number || number === 0) {
        return number
      } else {
        return '--'
      }
    },
    getSchedule(scheduleJson) {
      if (!scheduleJson) {
        return '--'
      }
      let runTypeMap = {
        1: '固定周期',
        2: '按天执行',
      }
      return runTypeMap[scheduleJson.runType] || '--'
    },
    openEdit(item) {
      let baseUrl = process.env.VUE_APP_INDEX_PATH + '#/etl-flow'
      if (item) {
        window.open(baseUrl + '?etlId=' + item.etlId)
      } else {
        window.open(baseUrl)
      }
    },
  },
}
</script>

<style lang="scss">
#self-service-etl {
  @import '@/assets/style/modules/config-card-page-with-footer.scss';
  background-color: transparent;
}
</style>
