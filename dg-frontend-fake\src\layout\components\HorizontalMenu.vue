<template>
  <div class="horizontal-menu-wrapper">
    <el-menu
      v-scroll-horizontal
      :default-active="activeMenu"
      mode="horizontal"
      :router="false"
      @select="handleMenuSelect"
      class="horizontal-menu"
      background-color="transparent"
      text-color="rgba(255, 255, 255, 0.8)"
      active-text-color="#ffffff"
      :popper-class="'horizontal-submenu-popper'">
      
      <template v-for="route in visibleRoutes">
        <el-submenu
          v-if="route.children && route.children.length > 0"
          :key="route.path"
          :index="route.path"
          class="horizontal-submenu"
          :popper-class="'horizontal-submenu-popper'">
          
          <template slot="title">
            <svg-icon 
              v-if="route.meta && route.meta.icon" 
              :icon="route.meta.icon"
              class="menu-icon">
            </svg-icon>
            <span>{{ route.meta ? route.meta.title : route.name }}</span>
          </template>
          
          <el-menu-item
            v-for="child in getVisibleChildren(route.children)"
            :key="child.path"
            :index="resolvePath(route.path, child.path)"
            @click="handleMenuClick(resolvePath(route.path, child.path))">
            <svg-icon 
              v-if="child.meta && child.meta.icon" 
              :icon="child.meta.icon"
              class="submenu-icon">
            </svg-icon>
            <span>{{ child.meta ? child.meta.title : child.name }}</span>
          </el-menu-item>
        </el-submenu>
        
        <el-menu-item
          v-else
          :key="route.path"
          :index="route.path"
          @click="handleMenuClick(route.path)">
          <svg-icon 
            v-if="route.meta && route.meta.icon" 
            :icon="route.meta.icon"
            class="menu-icon">
          </svg-icon>
          <span>{{ route.meta ? route.meta.title : route.name }}</span>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import path from 'path'
import { isExternal } from '@/utils/validate'

export default {
  name: 'HorizontalMenu',
  computed: {
    ...mapGetters(['permissionRoutes']),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    visibleRoutes() {
      return this.permissionRoutes.filter(route => !route.hidden)
    }
  },
  methods: {
    handleMenuSelect(index) {
      // Element UI 菜单选择事件
    },
    handleMenuClick(path) {
      // 检查是否是当前路由，避免重复导航
      if (this.$route.path === path) {
        return
      }

      // 查找对应的路由对象
      const route = this.findRouteByPath(path)

      // 检查是否需要在新窗口中打开（保留原有功能）
      if (route && route.meta && route.meta.blanck) {
        // 在新窗口中打开指定URL
        window.open(route.meta.url || 'http://172.16.86.101:18888/apps')
        return
      }

      // 检查是否需要在iframe中打开
      if (route && route.meta && route.meta.iframe) {
        // 显示iframe并添加到标签页
        this.$store.dispatch('iframe/showIframe', {
          url: route.meta.url,
          route: route
        })

        // 添加到访问过的视图中
        const iframeView = {
          path: route.path,
          name: route.name,
          meta: { ...route.meta, isIframe: true },
          title: route.meta.title
        }
        this.$store.dispatch('tagBar/addView', iframeView)

        // 更新当前路由（不实际跳转，只是为了标签页状态）
        // this.$router.push(path).catch(err => {
        //   if (err.name !== 'NavigationDuplicated') {
        //     console.error('路由导航错误:', err)
        //   }
        // })
        return
      }

      // 如果当前显示的是iframe，需要隐藏它
      if (this.$store.getters['iframe/showIframe']) {
        this.$store.dispatch('iframe/hideIframe')
      }

      // 正常进行路由跳转
      this.$router.push(path).catch(err => {
        // 忽略导航重复错误
        if (err.name !== 'NavigationDuplicated') {
          console.error('路由导航错误:', err)
        }
      })
    },
    findRouteByPath(targetPath) {
      // 递归查找路由（与AsideBar.vue保持一致）
      const findRoute = (routes, basePath = '/') => {
        for (const route of routes) {
          const fullPath = this.resolvePath(basePath, route.path)
          if (fullPath === targetPath) {
            return route
          }
          if (route.children) {
            const found = findRoute(route.children, fullPath)
            if (found) return found
          }
        }
        return null
      }
      return findRoute(this.permissionRoutes)
    },
    getVisibleChildren(children) {
      return children.filter(child => !child.hidden)
    },
    resolvePath(basePath, routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(basePath)) {
        return basePath
      }
      return path.resolve(basePath, routePath)
    }
  }
}
</script>

<style lang="scss">
// 全局样式，不使用scoped，确保能覆盖Element UI
.horizontal-submenu-popper {
  background-color: #ffffff !important;
  border: 1px solid #e4e7ed !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15) !important;
  margin-top: 2px !important;
  
  .el-menu {
    background-color: #ffffff !important;
    border: none !important;
    border-radius: 6px !important;
    
    .el-menu-item {
      color: #303133 !important;
      background-color: #ffffff !important;
      height: 44px !important;
      line-height: 44px !important;
      padding: 0 20px !important;
      
      &:hover {
        color: $themeColor !important;
        background-color: transparentize($themeColor, 0.92) !important;
      }
      
      &.is-active {
        color: $themeColor !important;
        background-color: transparentize($themeColor, 0.92) !important;
        font-weight: 500 !important;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.horizontal-menu-wrapper {
  flex: 1;
  overflow: hidden;
  position: relative;
  
  .horizontal-menu {
    border: none;
    background: transparent !important;
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    display: inline-flex !important; // 使用inline-flex确保不换行
    flex-wrap: nowrap !important;
    
    // 隐藏滚动条但保持滚动功能
    // &::-webkit-scrollbar {
    //   height: 0;
    //   background: transparent;
    // }
    
    // Firefox滚动条隐藏
    scrollbar-width: none;
    
    // IE滚动条隐藏
    -ms-overflow-style: none;
    
    .el-menu-item {
      color: rgba(255, 255, 255, 0.8);
      border-bottom: 2px solid transparent;
      transition: all 0.3s ease;
      height: 64px;
      line-height: 64px;
      padding: 0 16px;
      flex-shrink: 0 !important; // 防止压缩
      white-space: nowrap;
      display: inline-flex;
      align-items: center;
      
      .menu-icon {
        margin-right: 8px;
        font-size: 16px;
      }
      
      &:hover {
        color: #ffffff;
        background-color: rgba(255, 255, 255, 0.1) !important;
        border-bottom-color: rgba(255, 255, 255, 0.3);
      }
      
      &.is-active {
        color: #ffffff !important;
        background-color: rgba(255, 255, 255, 0.15) !important;
        border-bottom-color: #ffffff;
      }
    }
    
    .horizontal-submenu {
      flex-shrink: 0 !important; // 防止压缩
      display: inline-flex;
      
      .el-submenu__title {
        color: rgba(255, 255, 255, 0.8);
        border-bottom: 2px solid transparent;
        transition: all 0.3s ease;
        height: 64px;
        line-height: 64px;
        padding: 0 16px;
        white-space: nowrap;
        display: inline-flex;
        align-items: center;
        
        .menu-icon {
          margin-right: 8px;
          font-size: 16px;
        }
        
        &:hover {
          color: #ffffff;
          background-color: rgba(255, 255, 255, 0.1) !important;
          border-bottom-color: rgba(255, 255, 255, 0.3);
        }
      }
      
      &.is-active .el-submenu__title {
        color: #ffffff !important;
        background-color: rgba(255, 255, 255, 0.15) !important;
        border-bottom-color: #ffffff;
      }
    }
  }
}

@media (max-width: 768px) {
  .horizontal-menu-wrapper {
    display: none;
  }
}
</style>





