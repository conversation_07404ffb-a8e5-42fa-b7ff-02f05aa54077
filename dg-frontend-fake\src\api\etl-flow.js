import { get, post } from '@/http/request'

/*excel数据源*/
export function excelUpload(data) {//EXCEL数据源 - 1:导入EXCEL数据源
	return post('/dg-etl-mgr/servlet/excelSource?action=excelUpload', data)
}
export function DoUpload(data) {//EXCEL数据源 - 2:确认上传后导入数据库
	return post('/dg-etl-mgr/servlet/excelSource?action=DoUpload&fileId=' + data)
}
export function delExcelSource(data) {//EXCEL数据源 - 3:删除数据源
	return post('/dg-etl-mgr/servlet/excelSource?action=delExcelSource&fileId=' + data)
}
export function getExcelSourceList(data) {//EXCEL数据源 - 4:获取excel数据源列表
	return post('/dg-etl-mgr/webcall?action=excelSource.getExcelSourceList', data)
}
export function excelSourceDetail(data) {//etl流程分页查询
	return post('/dg-etl-mgr/webcall?action=excelSource.excelSourceDetail', data)
}
/*etl流程*/
export function etlFlowPage(data) {//etl流程分页查询
	return post('/dg-etl-mgr/webcall?action=etlFlow.etlFlowPage', data)
}
export function etlFlowInfo(data) {//etl流程详情
	return post('/dg-etl-mgr/webcall?action=etlFlow.etlFlowInfo', data)
}
export function getEtlFlowByVer(data) {//etl流程详情(历史版本)
	return post('/dg-etl-mgr/webcall?action=etlFlow.getEtlFlowByVer', data)
}
export function addFlow(data) {//新增流程
	return post('/dg-etl-mgr/servlet/etlFlow?action=addFlow', data, null, { 'Content-Type': 'application/json;charset=UTF-8' })
}
export function updateFlow(data) {//变更流程
	return post('/dg-etl-mgr/servlet/etlFlow?action=updateFlow', data, null, { 'Content-Type': 'application/json;charset=UTF-8' })
}
export function deleteFlow(data) {//删除流程
	return post('/dg-etl-mgr/servlet?action=etlFlow.deleteFlow&etlId=' + data)
}
export function editFlowStatus(data) {//修改流程状态
	return post('/dg-etl-mgr/servlet?action=etlFlow.editFlowStatus', data)
}
export function editFlowJob(data) {//editFlowJob
	return post('/dg-etl-mgr/servlet?action=etlFlow.editFlowJob', data)
}
export function excueteFlow(data) {//执行流程
	return post('/dg-etl-mgr/servlet/etlFlow?action=excueteFlow', data, null, { 'Content-Type': 'application/json;charset=UTF-8' })
}
export function interuptFlow(id) {//中断执行
	return post('/dg-etl-mgr/servlet/etlFlow?action=InteruptFlow&etlId=' + id)
}

/*原数据源*/
export function dsDict(data) {//原数据源 - 1:获取关系数据源数据字典
	var defData = {
		data: {
			controls: ['common.dsDict']
		}
	}
	return post('/dg-etl-mgr/webcall?action=etlDs.dsDict', JSON.stringify(defData))
}
export function getDsSrcTableConfig(data) {//原数据源 - 2:原数据源表信息数据字典
	return post('/dg-etl-mgr/webcall?action=etlDs.getDsSrcTableConfig', data)
}
export function getSysDsHeadConfig(data) {//原数据源 - 3:获取系统数据源预览数据表头配置
	var defData = {
		data: {
			controls: ['etlDs.getSysDsHeadConfig'],
			params: data
		}
	}
	return post('/dg-etl-mgr/webcall?action=etlDs.getSysDsHeadConfig', data)
}
export function getSysDsPreviewDatas(data) {//原数据源 - 4:获取系统数据源预览数据表头配置
	return post('/dg-etl-mgr/webcall?action=etlDs.getSysDsPreviewDatas', data)
}
export function excelDsDict() {//原数据源 - 5:原数据源表信息数据字典
	return post('/dg-etl-mgr/webcall?action=etlDs.excelDsDict')
}
export function fileDsDict() {//原数据源 - 文件数据集字典
	return post('/dg-etl-mgr/webcall?action=fileDataSet.dataSys')
}
export function getExcelDsHeadConfig(data) {//原数据源 - 6:原数据源表信息数据字典
	return post('/dg-etl-mgr/webcall?action=etlDs.getExcelDsHeadConfig', data)
}
export function getExcelDsPreviewDatas(data) {//原数据源 - 7:原数据源表信息数据字典
	return post('/dg-etl-mgr/webcall?action=etlDs.getExcelDsPreviewDatas', data)
}

/*流程*/
export function getAssemblyDict(data) {//原数据源 - 3:获取系统数据源预览数据表头配置
	return post('/dg-etl-mgr/webcall?action=common.unitModuleTree')
}
// 未分类的旧接口
// export function getAssemblyDict (data) {//原数据源 - 3:获取系统数据源预览数据表头配置
// 	var defData = {
// 		data: {
// 			controls: ['common.getAssemblyDict'],
// 			params: data
// 		}
// 	}
// 	return post('/dg-etl-mgr/webcall?action=common.getAssemblyDict', JSON.stringify(defData))
// } 

/*目标数据源*/
export function getTargetDsDict(data) {//1:获取目标数据源数据字典
	var defData = {
		data: {
			controls: ['etlDs.getTargetDsDict'],
			params: data
		}
	}
	return post('/dg-etl-mgr/webcall?action=etlDs.getTargetDsDict', JSON.stringify(defData))
}
export function getDsTagTableConfig(data) {//目标数据源 - 2:目标数据源表信息数据字典
	// var defData = {
	// 	data:{
	// 	    controls:['etlDs.getDsTagTableConfig'],
	// 	    params:data
	// 	}
	// }
	return post('/dg-etl-mgr/webcall?action=etlDs.getDsTagTableConfig', data)
}
export function getDsTagPreviewDatas(data) {//目标数据源 - 3:获取目标数据源预览数据
	var defData = {
		data: {
			controls: ['etlDs.getDsTagPreviewDatas'],
			params: data
		}
	}
	return post('/dg-etl-mgr/webcall', JSON.stringify(defData))
}
/*	export function getDsTagTableConfig:function(data,exConfig){//目标数据源 - 2:目标数据源表信息数据字典
		 // var defData = {
		 // 	data:{
		 // 	    controls:['etlDs.getDsTagTableConfig'],
		 // 	    params:data
		 // 	}
		 // }
		 return post('/dg-etl-mgr/webcall?action=etlDs.getDsTagTableConfig',data,exConfig)
	 } 
*/
//dsId,dsType
export function getSysDsTableList(data) {//原数据源- 8:数据源表数据字典
	return post('/dg-etl-mgr/webcall?action=etlDs.getSysDsTableList', data)
}
export function getFlowRunningInfo(data) {//8:获取流程运行信息
	return post('/dg-etl-mgr/servlet/etlFlow?action=getFlowRunningInfo', data)
}
export function getFlowRunningLog(data) {//8:获取流程运行信息
	return post('/dg-etl-mgr/servlet/etlFlow?action=getFlowRunningLog', data)
}
export function getFlowNodeData(data) {//8:获取流程运行信息
	return post('/dg-etl-mgr/servlet/etlFlow?action=getFlowNodeData', data)
}
