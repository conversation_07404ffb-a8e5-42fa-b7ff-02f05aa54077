<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="90px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="数据集名称"
            prop="name">
            <el-input
              v-model="formData.name"
              :readonly="!!data"
              v-trim
              clearable
              placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="说明"
            prop="desc">
            <el-input
              v-model="formData.desc"
              v-trim
              type="textarea"
              placeholder="请输入"
              maxlength="100"
              show-word-limit
              clearable
              :autosize="{ minRows: 3, maxRows: 10 }"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <UploadFile
        v-if="!data"
        ref="uploader"
        @change="files = $event"
        style="padding-left: 90px"></UploadFile>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        type="primary"
        size="small"
        :icon="loading ? 'el-icon-loading' : ''"
        @click.native="submitForm"
        >确定</el-button
      >
    </div>
  </div>
</template>

<script>
import { addDataset, updateDataset } from '@/api/file-data-set'
import UploadFile from './UploadFile.vue'

export default {
  components: {
    UploadFile,
  },
  props: {
    data: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      tableLoading: false,
      loading: false,
      formData: {
        name: '',
        desc: '',
      },
      files: [],
      rules: {
        name: [{ required: true, message: '请输入数据集名称', trigger: 'blur' }],
      },
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data) {
        this.formData = {
          name: this.data.NAME,
          desc: this.data.DESC,
        }
      } else {
        this.formData = {
          name: '',
          desc: '',
        }
      }
      this.$refs.uploader?.$refs?.upload?.clearFiles?.()
      this.files = []
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        let payload
        let submitAction

        // 有id为更新，无id为新增
        if (this.data?.ID) {
          submitAction = updateDataset
          payload = {
            ...this.formData,
            id: this.data.ID,
          }
        } else {
          submitAction = addDataset
          payload = new FormData()
          for (const key in this.formData) {
            payload.append(key, this.formData[key])
          }
          this.files.forEach((file) => payload.append('files', file.raw))
        }

        this.loading = true
        const [err, res] = await submitAction(payload)
        this.loading = false
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.uploader?.$refs?.upload?.clearFiles?.()
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }
}
</style>
