<template>
  <div class="config-form y-container--tight no-padding">
    <div class="y-container--tight">
      <el-tree
        ref="tree"
        :data="treeData"
        v-loading="tableLoading"
        show-checkbox
        default-expand-all
        node-key="resId"
        :props="treeProp">
      </el-tree>
    </div>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { getResTree, updateResTree } from '@/api/role'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      treeData: [],
      tableLoading: false,
      treeProp: {
        label: 'name',
      },
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      if (this.data?.ROLE_ID) {
        this.getResTree(this.data.ROLE_ID)
      }
    },
    async getResTree(id) {
      this.tableLoading = true
      const [err, res] = await getResTree(id)
      if (!err) {
        this.treeData = [res.root]
        this.$nextTick(() => {
          this.$refs.tree.setCheckedKeys(this.getCheckedKeys(this.treeData, 'resId'))
        })
      }
      this.tableLoading = false
    },
    getCheckedKeys(tree, id = 'id', checkedKeys = []) {
      for (let i = 0, len = tree.length; i < len; i++) {
        const children = tree[i]['children']
        if (children && children.length !== 0) {
          checkedKeys = this.getCheckedKeys(children, id, checkedKeys)
        } else {
          if (tree[i]?.checked === 'true') {
            checkedKeys.push(tree[i][id])
          }
        }
      }
      return checkedKeys
    },
    async submitForm() {
      const nodes = this.$refs.tree.getCheckedNodes(false, true).map((node) => {
        return {
          resId: node.resId,
        }
      })

      const payload = {
        roleId: this.data?.ROLE_ID,
        data: JSON.stringify(nodes),
      }

      const [err, res] = await updateResTree(payload)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.$emit('close-edit')
            this.treeData = []
            this.$emit('update-data')
          },
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.config-form {
  position: relative;

  .el-tree {
    margin: 0 auto;
  }

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
