import { get, post } from '@/http/request'
import { role } from '@/api/PATH'

// 角色权限
// 角色
export function getList(formData) {
  return post(role.list, formData)
}

export function addRole(formData) {
  return post(role.add, { data: JSON.stringify(formData) })
}

export function updateRole(formData) {
  return post(role.update, { data: JSON.stringify(formData) })
}

export function deleteRole(id) {
  return post(role.delete, { data: JSON.stringify({ roleId: id }) })
}

// 授权
export function getAccreditList(formData) {
  return post(role.accreditList, formData)
}

export function getAccreditableList(formData) {
  return post(role.accreditableList, formData)
}

export function accreditUser(formData) {
  return post(role.accredit, { data: JSON.stringify(formData) })
}

export function disaccreditUser(formData) {
  return post(role.disaccredit, { data: JSON.stringify(formData) })
}

// 角色资源树
export function getResTree(id) {
  return post(role.getResTree, { roleId: id })
}

export function updateResTree(formData) {
  return post(role.updateResTree, formData)
}

// 角色字典
export function getRoleDict() {
  return post(role.dict)
}