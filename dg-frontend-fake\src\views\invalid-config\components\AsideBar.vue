<template>
  <div class="aside-bar">
    <div class="y-bar">
      <h2 class="y-title">无效工单类型分类</h2>
      <svg-icon
        icon="add"
        @click.native="openDialog(null)"></svg-icon>
    </div>
    <div
      class="y-bar"
      style="padding-top: 0">
      <el-input
        v-model="keyword"
        v-trim
        placeholder="请输入关键字"
        size="small"
        clearable
        suffix-icon="el-icon-search"></el-input>
    </div>
    <class-menu
      v-loading="loading"
      :list="filteredList"
      :active-menu="activeMenu"
      id-prop="TYPE_ID"
      @set-current="setCurrent($event)">
      <template v-slot="{ item }">
        <span>{{ item.TYPE_NAME }}</span>
        <el-dropdown
          trigger="click"
          placement="top">
          <div>
            <svg-icon
              icon="more"
              v-show="item.TYPE_ID === activeMenu"></svg-icon>
          </div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              icon="el-icon-edit"
              @click.native="openDialog(item)"
              >编辑</el-dropdown-item
            >
            <el-dropdown-item
              icon="el-icon-delete"
              @click.native="deleteClass(item)"
              >删除</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </class-menu>
    <el-dialog
      :title="dialogTitle"
      width="500px"
      :visible.sync="dialogShow">
      <el-form
        ref="editForm"
        class="clearfix"
        :model="editForm"
        :rules="rules"
        label-position="right"
        label-width="90px">
        <el-col :span="24">
          <el-form-item
            label="分类名称"
            prop="typeName">
            <el-input
              v-model="editForm.typeName"
              v-trim
              placeholder="请输入分类名称"
              maxlength="20"
              clearable
              show-word-limit></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="是否默认"
            prop="isDefault">
            <el-radio
              v-model="editForm.isDefault"
              label="1"
              >是</el-radio
            >
            <el-radio
              v-model="editForm.isDefault"
              label="0"
              >否</el-radio
            >
          </el-form-item>
        </el-col>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button
          v-debounce="submitForm"
          type="primary"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getClassList, addClass, updateClass, deleteClass } from '@/api/invalid-config'
import ClassMenu from '@/components/ClassMenu'

export default {
  components: {
    ClassMenu,
  },
  data() {
    return {
      loading: false,
      activeMenu: '',
      keyword: '',
      menuList: [],
      dialogTitle: '',
      dialogType: '',
      dialogShow: false,
      editForm: { typeName: null, isDefault: null },
      rules: {
        typeName: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
        isDefault: [{ required: true, message: '请选择是否默认', trigger: 'blur' }],
      },
    }
  },
  computed: {
    filteredList() {
      if (this.keyword.trim()) {
        return this.menuList.filter((item) => item.TYPE_NAME.includes(this.keyword.trim()))
      }
      return this.menuList
    },
  },
  watch: {},
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true

      const [err, res] = await getClassList({ typeName: '' })
      if (res) {
        this.menuList = res?.data
        this.setCurrent(this.menuList?.[0])
      }

      this.loading = false
    },
    setCurrent(item) {
      this.activeMenu = item.TYPE_ID
      this.$emit('set-current', this.activeMenu)
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const payload = { ...this.editForm }

        let submitAction = addClass
        if (this.dialogType === 'edit') {
          submitAction = updateClass
          payload.typeId = this.activeMenu
        }

        const [err, res] = await submitAction(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.dialogShow = false
              this.fetchData()
            },
          })
        }
      })
    },
    async deleteClass(data) {
      try {
        await this.$confirm('此操作将永久删除选中分类, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }
      const [err, res] = await deleteClass(data.TYPE_ID)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    initDialog(data) {
      this.$refs?.editForm?.resetFields()
      if (data) {
        this.dialogTitle = '编辑分类'
        this.dialogType = 'edit'
        this.editForm = { typeName: data.TYPE_NAME, isDefault: data.IS_DEFAULT }
      } else {
        this.dialogTitle = '新增分类'
        this.dialogType = 'add'
        this.editForm = { typeName: null, isDefault: null }
      }
    },
    openDialog(data) {
      this.initDialog(data)
      this.dialogShow = true
    },
    closeDialog() {
      this.dialogShow = false
    },
  },
}
</script>

<style lang="scss" scoped>
.aside-bar {
  @include flex-col;
  flex-shrink: 0;
  width: 288px;
  height: 100%;

  .y-title {
    line-height: 24px;
  }

  > .y-bar {
    padding: 16px 24px;
    padding-bottom: 8px;

    .svg-icon {
      font-size: 16px;
      color: $txtColor-light;
      cursor: pointer;

      &:hover {
        color: $themeColor;
      }
    }
  }
}
</style>
