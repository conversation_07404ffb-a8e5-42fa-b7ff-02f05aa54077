export default {
  '全部': 'ALL',
  '场景通知模型列表': 'List of scene notification models',
  '新增场景通知模型': 'New scene notification model',
  '编辑': 'Edit',
  '删除': 'Delete',
  '序号': 'Order',
  '通知模型名称': 'Notification model name',
  '所属场景': 'Owning scene',
  '提醒类型': 'Reminder type',
  '提醒条件': 'Reminder condition',
  '提醒对象': 'Reminder object',
  '启用状态': 'Enabled state',
  '操作': 'Operation',
  '搜索': 'Search',
  '场景通知模型': 'Scene notification model',
  '已开启场景通知模型': 'Enabled scene notification model',
  '已暂停场景通知模型': 'Suspended scene notification model',
  '请选择': 'Please select',
  '重置': 'Reset',
  '确定': 'Confirm',
  '取消': 'Cancel',
  '场景通知模型名称': 'Scene  model name',
  '所属通知场景': 'Ownership  scenario',
  '提醒参数': 'Reminder parameter',
  '通知渠道': 'Notification channel',
  '消息模板': 'Message template',
  '提醒标题': 'Reminder heading',
  '提醒内容': 'Reminder content',
  '通知URL': 'Notification URL',
  '提醒间隔': 'Reminder interval',
  '启用': 'Enable',
  '停用': 'Deactivate',
  '参数列表': 'Parameter list',
  '原始参数名': 'Original parameter name',
  '参数名': 'Parameter name',
  '参数描述': 'Parameter description',
  '默认值': 'Default value',
  '新建场景通知模型': 'New Scene Notification Model',
  '确认新建通知模型': 'Confirm new notification model',
  '编辑场景通知模型': 'Edit Scene Notification Model',
  '确认编辑通知模型': 'Confirm edit notification model',
  //通知场景
  '通知场景配置': 'Notification scenario configuration',
  '新增通知场景': 'New notification scenario',
  '场景通知方式': 'Scene notification mode',
  '场景名称': 'Scene name',
  '是否内置': 'Built-in or not',
  '是否启用': 'Enable or not',
  '创建时间': 'Creation time',
  '确认更新场景': 'Confirm update scenario',
  '确认新建场景': 'Confirm new scene',
  '更新场景': 'Update scene',
  '新建场景': 'New scene',
  '通知场景名称': 'Notification scene name',
  '通知场景编号': 'Notification scene code',
  '通知方式': 'Mode of notification',
  '短信发送渠道': 'SMS sending channel',
  '邮件发送账号': 'Email sending account',
  '全媒体渠道账号': 'All-media channel',
  '第三方发送渠道': 'Third party channel',
  // 自定义组配置
  '自定义组': 'Custom group',
  '外部人员': 'External personnel',
  '新增自定义组': 'Add Custom group',
  '通知模式': 'Notification mode',
  '通知对象': 'Notification object',
  '通知组名称': 'Notification group name',
  '创建人': 'Founder',
  '按号码通知': 'Call by number',
  '按成员通知': 'Notification by member',
  '编辑自定义组': 'Edit custom group',
  '新建自定义组': 'Add custom group',
  '确认编辑通知组': 'Confirm edit notification group',
  '确认新建通知组': 'Confirm add notification group',
  '通知组编号': 'Notification group',
  '提醒方式': 'Reminder mode',
  '手机号码': 'Mobile phone',
  // 外部人员
  '所属通知组': 'Owning notification group',
  '人员名称': 'Personnel name',
  '邮箱': 'Mailbox',
  '手机号': 'Mobile',
  '内部登录账号': 'Internal login account',
  '编辑通知人员': 'Editorial notification staff',
  '新建通知人员': 'New notifier',
  '确认编辑录用人员': 'Confirm editor hires',
  '确认新建录用人员': 'Confirm new hires',
  '人员编号': 'Personnel number',
  '所选通知组': 'Selected notification group',
  '第三方渠道账号': 'Third-party channel ',
  //通知对象
  '配置通知方式': 'Configuring notification mode',
  '短信通知': 'Message notification',
  '邮件通知': 'Email notification',
  '内部通知': 'Internal notification',
  '设置时间': 'Set time',
  '提醒时间': 'Reminder time',
  '已配置通知组': 'Configured notification',
  '未配置通知组': 'Not-configured notification',
  '已配置人员': 'Assigned personnel',
  '未配置人员': 'Unassigned personnel',
  '确认配置': 'Confirm configuration',
  '配置方式': 'Configuration mode',
  '全局配置': 'Global configuration',
  '单项配置': 'Single configuration',
  '通知人员': 'Notifying officer',
  '提醒开始时间': 'Reminder start',
  '提醒结束时间': 'Reminder end',
  '短信': 'Message',
  '邮件': 'Mail',
  '第三方': 'Third party',
  '短信接收': 'SMS reception',
  '邮件接收账号': 'Mail receiving',
  '内部通知账号': 'Internal notification',
  '第三方接收渠道': 'Third party receiving',
  '人员': 'Personnel',
  '部门组': 'Departmental group',
  '技能组': 'Skill group',
  '工作组': 'Working group',
  '通知组': 'Notification group',
  '通知类型': 'Notification type',
  //  邮件渠道列表
  '邮件渠道列表': 'Mailing channel list',
  '新增邮件渠道': 'New mail channel',
  '邮箱账号': 'New mail channel',
  '用户名': 'UserName',
  '处理模式': 'Processing mode',
  '运行状态': 'Running state',
  '默认渠道设置': 'Default channel Settings',
  '邮箱密码': 'Email password',
  '账号用户名': 'Account userName',
  '重复次数': 'Number of repeats',
  '默认发送账号': 'Default sending account',
  '是否SSL': 'SSL or not',
  '自动派发工单': 'Automatic distribution of work orders',
  '当日最大发送量': 'Maximum daily delivery',
  '当月最大发送量': 'Maximum outgoing volume',
  '每天每人发送量': 'Daily delivery',
  '是否需要验证': 'Verification isRequired',
  '自动发送回执': 'Send a return receipt automatically',
  '回执模板': 'Receipt template',
  '满意度默认标题': 'Satisfaction default title',
  '拓展字段': 'Extended field',
  '开启自动化任务处理': 'Enable automated task processing',
  '自动将已挂起的邮件设置为已解决': 'Automatically sets  messages to resolved',
  '自动为已解决的邮件发送满意度': 'Automatically send satisfaction for resolved ',
  '自动关闭已发送满意度的邮件': 'Automatically closes emails with satisfaction',
  '首次回复超时提醒时限': 'First reply timeout reminder time limit',
  '公共信息': 'Public information',
  '自动化任务处理(单位：小时)': 'Automated task processing (unit: hour)',
  '邮件发送信息': 'Mail sending message',
  '邮件接收信息': 'Mail receiving information',
  '发送端口': 'Sending port',
  '接收端口': 'Receiving port',
  '确认更新邮件渠道': 'Confirm the updated email channel',
  '确认新建邮件渠道': 'Confirm a new email channel',
  '更新邮件渠道账号': 'Update the email channel account',
  '新建邮件渠道账号': 'Create an email channel account',
//  短信渠道列表
  '短信渠道列表': 'SMS channel list',
  '新增短信渠道': 'New SMS channel',
  '渠道类型': 'Channel type',
  '渠道名称': 'Channel name',
  '渠道编码': 'Channel coding',
  '接口': 'Interface',
  '账号': 'Account number',
  '确认更新短信渠道': 'Confirm to update the SMS channel',
  '确认新建短信渠道': 'Confirm the new SMS channel',
  '更新短信渠道账号': 'Update the SMS channel account',
  '新建短信渠道账号': 'Create an SMS channel account',
  '扩展参数': 'Extended parameter',
  '短信编码格式': 'SMS coding format',
  '是否允许重发': 'Whether to allow resend',
  '接口地址': 'Interface address',
  '是否默认渠道': 'Default channel or not',
  '密码': 'Password',
  '格式: 参数名1=值1&参数名2=值2,如A=1&B=2': 'Format: Parameter name1=value1&Parametername2=value2,for example, A=1&b=2',
//全媒体渠道账号列表
  '全媒体渠道账号列表': 'List of all media channel accounts',
  '新增全媒体渠道': 'New all-media channels',
  '编辑邮件渠道账号': 'Edit the email channel account',
  '应用APPSECRET': 'Application APPSECRET',
  '公众号名称': 'Public account name',
  '应用APP ID': 'Application APP ID',
  '应用令牌': 'Application token',
  '消息加密方式': 'Message encryption mode',
  '加密密钥': 'Encryption key',
  'token管理方式': 'Token manage mode',
  '原始ID': 'Original ID',
  '发送消息方式 ID': 'Message sending mode ID',
  '第三方Token地址': 'Third party Token address',
  'mediazk地址': 'mediazk address',
  '第三方校验Token': 'Third-party verification Token',
  'Token刷新地址': 'Token refresh address',
  '第三方通知地址': 'Third party  address',
  '转接人工服务关键字': 'Transfer manual service keyword',
  '微信网关地址': 'Wechat gateway address',
  '关键词回复': 'Keyword reply',
  '企业名称': 'Enterprise name',
  '企业微信ID': 'Enterprise wechat ID',
  '企业应用': 'Enterprise application',
  '微博名称': 'Microblog name',
  'AppKey': 'AppKey',
  'AppSecret': 'AppSecret',
  '小程序名称': 'Applet name',
  '小程序 ID': 'Applet ID',
  '小程序密钥': 'Applet key',
  '消息加密密钥': 'Message encryption key',
  '获取token方式': 'Token acquisition mode',
  '发送客户消息方式': 'Send customer messages',
  '获取token服务': 'Obtaining token service',
  '发送客户消息服务': 'Send customer messaging service',
  '保存': 'Save',
  '企微应用': 'Enterprise micro application',
  '名称': 'Name',
  '类型': 'Type',
  'AgentId': 'AgentId',
  'Secret': 'Secret',
  'EncodingAESKey': 'EncodingAESKey',
  'Token': 'Token',
  '备注': 'Remarks',
  '增加表单': 'Add form',
  //通知渠道列表
  '通知渠道列表': 'Notification channel list',
  '新增通知渠道': 'New notification channel',
  '通知渠道账号': 'Notification channel account',
  '更新通知渠道账号': 'Update the notification channel account',
  '新建通知渠道账号': 'Create a notification channel account',
  'APP_ID': 'APP_ID',
  'APP_SERCET': 'APP_SERCET',
  'webhook地址': 'Webhook address',
  '是否启用加密': 'Enable to encryption',
  '加密串': 'Encryption string',
  // 通知模板列表
  '短信模板': 'SMS',
  '邮件模板': 'Mail',
  '全媒体模板': 'All-media',
  '自定义模板': 'Custom',
  '新增消息模板': 'New message template',
  '请输入关键词进行搜索': 'Please enter keywords to search',
  '模板分类': 'Template classification',
  '确定删除该模板吗': 'Are you sure to delete the template',
  '提示': 'prompt',
  '删除成功': 'Deleted successfully',
  '已取消删除': 'Deleted already cancelled',
  '附件管理': 'Attachment management',
  '已上传附件': 'Attachment has been uploaded.',
  '创建日期': 'Creation date',
  '确认新建模板': 'Confirm a new template',
  '确认修改模板': 'Confirm template modification',
  '编辑消息模板': 'Edit message template',
  '模板': 'Template',
  '请选择模板分组': 'Select a template group',
  '请输入模板编号': 'Please enter the template number',
  '请输入模板名称': 'Please enter a template name',
  '请选择模板类型': 'Select a template type',
  '请选择关联渠道': 'Select an associated channel',
  '请选择短信类型': 'Select a short message type',
  '请选择是否同步': 'Please select whether to synchronize',
  '请输入序号': 'Please enter serial number',
  '请输入名称': 'Please enter name',
  '请输入内容': 'Please enter the content',
  '请输入': 'Please enter',
  '请选择消息模板': 'Select a message template',
  '请选择启用状态': 'Please select Enable status',
  '请选择关联模板': 'Select an associated template',
  '请输入消息内容': 'Please enter the message content',
  '请输入参数配置': 'Please enter the parameter configuration',
  '修改成功': 'Modified successfully',
  '新建模板成功': 'Creating a template succeeds',
  '请输入关键字搜索': 'Please enter keyword search',
  '关联渠道': 'Associated channel',
  '关联模板': 'Association template',
  '模板名称': 'Template name',
  '模板内容': 'Template content',
  '请输入参数描述': 'Please enter a parameter description',
  '请输入默认值': 'Please enter the default value',
  '模板分组': 'Template grouping',
  '模板编号': 'Template number',
  '模板类型': 'Template type',
  '短信类型': 'SMS type',
  '同步到短信服务': 'Synchronize to the SMS service',
  '第三方模板编码': 'Third-party template code',
  '短信内容': 'Text message content',
  '请输入短信内容': 'Enter the content of the short message',
  '请输入申请说明': 'Please enter the application instructions',
  '申请说明': 'Application instructions',
  '请输入备注': 'Please enter remarks',
  // 场景消息发送
  '发送场景消息内容': 'Send the scene message content',
  '场景消息': 'Scene Message',
  '接收对象': 'Receiving object',
  '暂无': 'Temporarily absent',
  '选择场景': 'Select scene',
  '发送渠道': 'Transmission channel',
  '请选择发送渠道': 'Select a sending channel',
  '请选择模板': 'Please select template',
  '接收类型': 'Receiving type',
  '请选择接收类型': 'Please select a receive type',
  '请选择接收对象': 'Select the recipient',
  '主题': 'Theme',
  '请输入主题': 'Please enter the subject',
  '发送时间': 'Sending time',
  '即时发送': 'Instant sending',
  '定时发送': 'Timed transmission',
  '请选择发送时间': 'Please select the sending time',
  '选择日期时间': 'Select date and time',
  '参数值': 'Parameter value',
  '请输入参数值': 'Please enter parameter values',
  '消息内容': 'Message content',
  '请输入文字': 'Please enter text',
  '请选择场景': 'Please select scene',
  '请选择提醒条件': 'Please select a reminder condition',
  '发送成功': 'Sent successfully',
  '保存成功': 'Save successfully',
  '重置信息': 'Reset message',
  '保存到草稿箱': 'Save to draft box',
  '提交并发送': 'Submit and send',
  // 自定义发送
  '自定义发送': 'Custom Send',
  '渠道': 'channel',
  '短信渠道': 'SMS channel',
  '邮件渠道': 'Mail channel',
  '卡片消息渠道': 'Card message channel',
  '自定义渠道': 'Custom channel',
  '发送': 'Send',
  '发件人': 'Sender',
  '发件人邮箱地址': 'Sender email address',
  '邮件主题': 'Email subject',
  '附件': 'Attachment',
  '收件人': 'Addressee',
  '邮件签名': 'Mail signature',
  '通知号码': 'Notification number',
  '选择模板': 'Select template',
  '标题': 'Title',
  '请输入标题': 'Please enter the title',
  '短信签名': 'SMS signature',
  '签名在前': 'Sign first',
  '签名在后': 'Sign after',
  '配置': 'Configuration',
  '同步': 'Synchronization',
  '请选择通知对象': 'Select a notification object',
  '请输入短信标题': 'Please enter the SMS title',
  '无': 'None',
  '不使用签名': 'No signature',
  '同步完成': 'Synchronization completion',
  '短信群配置': 'SMS group configuration',
  '短信签名配置': 'SMS signature configuration',
  '请选择发件人': 'Please select Sender',
  '通知邮箱': 'Notification mailbox',
  '抄送': 'Copy',
  '请输入主题文字': 'Please enter the subject text',
  '邮件内容': 'Email content',
  '请输入邮件内容': 'Please enter the email content',
  '请输入号码，按回车键生成': 'Please enter the number and press Enter to generate',
  '请输入邮箱，按回车键生成': 'Please enter email and press Enter to generate',
  '号码已存在': 'Number already exists',
  '请输入正确的手机号': 'Please enter the correct mobile phone number',
  '邮箱已存在': 'Mailbox already exists',
  '请输入正确的邮箱': 'Please enter the correct email address',
  // 消息草稿箱
  '消息草稿箱': 'Message draft box',
  '通知标题': 'Notice title',
  '通知内容': 'Content of notice',
  '消息类型': 'Message type',
  '场景消息发送': 'Scene message sending',
  '确定要删除吗？': 'Are you sure you want to delete it?',
  '温馨提示': 'Warm reminder',
  // 通知发送记录
  '通知发送记录': 'Notification sending record',
  '至': 'To',
  '开始日期': 'Start date',
  '结束日期': 'End date',
  '发送人': 'Sender',
  '通知数量': 'Quantity of notice',
  '通知抵达情况': 'Notice of arrival',
  '是否顶置': 'Overhead or not',
  '查看明细': 'View details',
  '通知明细记录': 'Notice detail record',
  '推送状态': 'Push status',
  '推送成功': 'Push success',
  '推送失败': 'Push failure',
  '通知账号': 'Notification account',
  '推送时间': 'Push time',
  '发送次数': 'Transmission times',
  '次': 'Times',
  // 数据看板
  '内容': 'Content',
  '最新通知记录TOP10': '最新通知记录TOP10',
  '单条通知最大数': 'TOP10 latest notification records',
  '累计通知成功率': 'Cumulative notification success rate',
  '各渠道通知量': 'Number of notifications by channel',
  '发出类型': 'Issuing type',
  '各渠道类型通知量': 'Amount of notifications by channel type',
  '投放接收': 'Drop and receive',
  '通知场景类型分析': 'Type analysis of notification scenarios',
  '配置数量': 'Number of configurations',
  '场景模型通知预警月度分析': 'Monthly analysis of scenario model notification warning',
  '通知量': 'Notification quantity',
  '月': '月',
  '通知发送量': 'Number of notifications sent',
  '通知成功量': 'Number of successful notifications',
  '场景发送量': 'Number of scene sent',
  '自定义发送量': 'Customize the number of sends',
  '总渠道通知量': 'Total number of channel notifications',
  '成功率': 'Success rate',
  '成功通知量': 'Number of successful notifications',
  '通知异常量': 'Number of notified exceptions',
  '日期': 'Date',
  '内部消息': 'Inside information',
  'APP': 'APP',
  '第三方渠道': 'Third-party channel',
  '全媒体渠道': 'All-media channel',

  '经纬度': 'Longitude and Latitude',
  '最后身份确认时间': 'Last identity confirmation time',
  '联系电话': 'Contact phone number',
  '服务状态': 'Service Status',
  '区划': 'Zoning',
  '修改': 'Modify',
  '导入清单': 'Import List',
  '导出清单': 'Export List',
  '关键字': 'Keyword',
  '人员类型': 'Personnel Type',
  '叫应负责人架构': 'Responsible Person Architecture',
  '成员列表': 'Member List',
  '行政级别': 'Administrative level',
  '行政职务': 'Administrative position',
  '姓名': 'Name'
}
