<template>
  <div id="y-layout" class="clearfix">
    <top-bar></top-bar>
    <div class="container flex-col">
      <tag-bar></tag-bar>
      <div class="y-layout_main">
        <!-- iframe容器 -->
        <div
          v-if="showIframe"
          class="iframe-container"
          v-loading="iframeLoading"
          element-loading-text="正在加载...">
          <iframe
            ref="iframe"
            :src="currentIframeUrl"
            @load="handleIframeLoad"
            class="iframe-content"
            frameborder="0">
          </iframe>
        </div>

        <!-- 普通路由视图 -->
        <transition
          v-else
          appear
          name="fade-transform"
          mode="out-in">
          <keep-alive :include="cachedViews">
            <router-view :key="routeKey"></router-view>
          </keep-alive>
        </transition>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'layout',
  components: {
    'top-bar': () => import('./components/TopBar.vue'),
    'tag-bar': () => import('./components/TagBar.vue'),
  },
  data() {
    return {}
  },
  computed: {
    cachedViews() {
      return this.$store.state.tagBar.cachedViews
    },
    routeKey() {
      return this.$route.path
    },
    showIframe() {
      return this.$store.getters['iframe/showIframe']
    },
    currentIframeUrl() {
      return this.$store.getters['iframe/currentUrl']
    },
    iframeLoading() {
      return this.$store.getters['iframe/loading']
    }
  },
  created() {
    // 保持原有逻辑
  },
  beforeDestroy() {
    // 保持原有逻辑
  },
  methods: {
    handleIframeLoad() {
      this.$store.dispatch('iframe/iframeLoaded')
    }
  },
}
</script>

<style lang="scss">
#y-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: $bgColor-dark;

  & > .container {
    flex: 1;
    overflow: hidden;
  }

  .y-layout_main {
    flex: 1;
    margin: 16px;
    padding: 0;
    height: calc(100vh - 64px - 40px - 32px); // 减去TopBar、TagBar和边距
    overflow: auto;

    &:has(> div[class*="fade-transform"]) {
      margin: 16px;
      padding: 0;
      overflow: hidden;
    }
  }

  .flex-col {
    display: flex;
    flex-direction: column;
  }

  .flex-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  .iframe-container {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .iframe-content {
      width: 100%;
      height: 100%;
      border: none;
      display: block;
    }
  }
}
</style>
