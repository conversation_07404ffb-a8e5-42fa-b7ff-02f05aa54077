<template>
  <div class="aside-bar">
    <div class="y-bar header">
      <h2 class="y-title">热词排行</h2>
    </div>
    <div class="y-bar">
      <el-input
        v-model="keyword"
        v-trim
        placeholder="请输入关键字"
        size="small"
        clearable
        suffix-icon="el-icon-search"></el-input>
    </div>
    <class-menu
      v-loading="loading"
      :list="filteredList"
      :active-menu="activeMenu"
      @set-current="setCurrent($event)">
      <template v-slot="{ item }">
        <span>{{ item.name }}</span>
      </template>
    </class-menu>
  </div>
</template>

<script>
import { getClassTree } from '@/api/hotword-manage'
import ClassMenu from '@/components/ClassMenu'

export default {
  components: {
    ClassMenu,
  },
  data() {
    return {
      loading: false,
      activeMenu: '',
      keyword: '',
      classList: [],
    }
  },
  computed: {
    filteredList() {
      if (this.keyword.trim()) {
        return this.classList.filter((item) => item.name.includes(this.keyword.trim()))
      }
      return this.classList
    },
  },
  watch: {},
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true

      const [err, res] = await getClassTree()
      if (res && res?.data) {
        this.classList = res.data?.children?.map((i) => i?.children)?.flat() || []
        this.setCurrent(this.classList[0])
      }

      this.loading = false
    },
    setCurrent(item) {
      this.activeMenu = item?.id
      this.$emit('set-current', item)
    },
  },
}
</script>

<style lang="scss" scoped>
.aside-bar {
  @include flex-col;
  flex-shrink: 0;
  width: 240px;
  height: 100%;

  .header {
    border-bottom: 1px solid $borderColor;
  }

  .y-title {
    line-height: 24px;
  }

  > .y-bar {
    padding: 16px 24px;
    padding-bottom: 8px;

    .svg-icon {
      font-size: 16px;
      color: $txtColor-light;
      cursor: pointer;

      &:hover {
        color: $themeColor;
      }
    }
  }
}
</style>
