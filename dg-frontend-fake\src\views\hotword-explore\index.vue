<template>
  <base-card
    id="hotword-explore"
    class="y-page">
    <div class="y-container no-padding">
      <div class="y-bar">
        <h2 class="y-title">新发热词</h2>
        <el-button
          class="mini"
          type="primary"
          @click.native="checkHotword(null)">
          <i class="el-icon-finished"></i>
          批量审核
        </el-button>
      </div>
      <div class="y-container--tight">
        <el-form
          ref="searchForm"
          :inline="true"
          :model="searchForm"
          style="margin-top: 16px">
          <el-form-item
            label="关键字"
            prop="key">
            <el-input
              v-model="searchForm.key"
              v-trim
              clearable
              size="small"
              style="width: 220px"
              placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item
            label="分类"
            prop="typeName">
            <el-select
              v-model="searchForm.typeName"
              size="small"
              filterable
              placeholder="请选择务"
              clearable
              style="width: 220px">
              <el-option
                v-for="(label, key) in typeDict"
                :key="key"
                :label="label"
                :value="key">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              class="mini"
              type="primary"
              size="small"
              plain
              @click.native="resetSearch">
              <svg-icon icon="reset"></svg-icon>
              重置
            </el-button>
            <el-button
              v-debounce="{ evtHandler: fetchData }"
              class="mini"
              type="primary">
              <i class="el-icon-search"></i>
              搜索
            </el-button>
          </el-form-item>
        </el-form>
        <el-table
          :data="list"
          v-loading="loading"
          v-reset-scroll="'div.el-table__body-wrapper'"
          stripe
          height="100%"
          fit
          ref="table"
          style="width: 100%">
          <el-table-column width="50">
            <template slot-scope="scope">
              <el-checkbox
                v-model="scope.row.checked"
                :disabled="scope.row.EXAMINE_STATE !== '0'"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column
            type="index"
            label="序号"
            width="50">
          </el-table-column>
          <el-table-column
            label="热词"
            prop="WORD_NAME"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            label="热词分类"
            prop="TYPE_NAME"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            label="审核状态"
            prop="EXAMINE_STATE"
            show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tag
                :type="getState(scope.row.EXAMINE_STATE, 'type')"
                size="small"
                >{{ getState(scope.row.EXAMINE_STATE, 'label') }}</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column
            label="操作时间"
            prop="OPER_TIME"
            min-width="180"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            label="操作"
            width="120"
            fixed="right">
            <template
              v-if="scope.row.EXAMINE_STATE === '0'"
              slot-scope="scope">
              <el-link
                type="primary"
                :underline="false"
                @click="checkHotword(scope.row)"
                >审核</el-link
              >
              <el-link
                type="primary"
                :underline="false"
                @click="openEdit(scope.row)"
                >编辑</el-link
              >
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/no-info.png')"
            description="暂无信息"></el-empty>
        </el-table>
      </div>
      <pagination
        :current-page.sync="formData.pageIndex"
        :page-size.sync="formData.pageSize"
        :total="total"
        @page="fetchData"></pagination>
    </div>
    <el-dialog
      title="编辑热词"
      width="500px"
      :visible.sync="dialogShow">
      <el-form
        ref="editForm"
        class="clearfix"
        :model="editForm"
        :rules="editRules"
        label-position="right"
        label-width="100px">
        <el-col :span="24">
          <el-form-item
            label="热词名称"
            prop="wordName">
            <el-input
              v-model="editForm.wordName"
              v-trim
              placeholder="请输入"
              maxlength="20"
              clearable
              show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button
          v-debounce="{ evtHandler: updateHotword }"
          type="primary"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </base-card>
</template>

<script>
import { getList, checkHotword, updateHotword, deleteHotword, getTypeDict } from '@/api/hotword-explore'

export default {
  name: 'HotwordExplore',
  components: {},
  data() {
    return {
      loading: false,
      searchForm: {
        key: '',
        typeName: '',
      },
      formData: {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
      },
      total: 0,
      list: [],
      dialogShow: false,
      editForm: {},
      editRules: {
        wordName: [{ required: true, message: '请输入热词名称', trigger: 'blur' }],
      },
      typeDict: {},
    }
  },
  computed: {},
  created() {
    this.getTypeDict()
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true

      const payload = {
        ...this.searchForm,
        ...this.formData,
      }

      const [err, res] = await getList(payload)
      if (res) {
        this.list = res.data || []
        this.list = this.list.map((i) => ({ ...i, checked: false }))
        this.total = res.totalRow
      }
      this.loading = false
    },
    resetSearch() {
      this.$refs.searchForm?.resetFields()
      this.formData = {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
      }
      this.fetchData()
    },
    async checkHotword(data) {
      let tableSelection = this.list.filter((i) => i.checked)
      if (!data && tableSelection?.length === 0) {
        this.$message({
          message: '请选择至少一项进行审核',
          duration: 800,
        })
        return false
      }

      const payload = {
        ids: data ? [data.ID] : tableSelection.map((i) => i.ID),
      }
      const [err, res] = await checkHotword(payload)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    async getTypeDict() {
      const [err, res] = await getTypeDict()
      if (res) {
        this.typeDict = res.data || {}
      }
    },
    openEdit(data) {
      this.$refs?.editForm?.resetFields()
      const { ID, WORD_NAME } = data
      this.editForm = { id: ID, wordName: WORD_NAME }
      this.dialogShow = true
    },
    closeDialog() {
      this.dialogShow = false
    },
    async updateHotword() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const payload = {
          ...this.editForm,
        }

        const [err, res] = await updateHotword(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.dialogShow = false
              this.fetchData()
            },
          })
        }
      })
    },
    getState(state, flag) {
      const stateMap = {
        label: {
          0: '待审核',
          1: '已审核',
        },
        type: {
          0: 'primary',
          1: 'success',
        },
      }
      return stateMap[flag][state]
    },
  },
}
</script>

<style lang="scss">
#hotword-explore {
  > .y-container {
    > .y-bar {
      justify-content: flex-start;
      border-bottom: 1px solid $borderColor;
    }

    > .y-container--tight {
      padding-bottom: 0;
      border-bottom: 1px solid $borderColor;

      .el-table {
        &::before {
          content: none;
        }
      }
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }
}
</style>
