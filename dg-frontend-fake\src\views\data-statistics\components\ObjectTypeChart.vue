<template>
  <div
    v-if="!data || data?.length === 0"
    :key="0"
    class="y-container--tight no-padding">
    <el-empty
      :image="require('@/assets/images/no-data.png')"
      description="暂无数据"></el-empty>
  </div>
  <div
    v-else
    :key="1"
    class="object-type-chart y-container no-padding">
    <div
      class="chart-container y-container no-padding"
      ref="chart-container"></div>
    <chart-legend
      :data="dataList"
      :color="color"
      @disactive-item="handleDisactiveItem($event)"></chart-legend>
  </div>
</template>

<script>
import { formatNumberWithComma } from '@/utils/formatter.js'
import style from '../style.js'
import chartMixins from '@/mixins/chartMixins.js'

export default {
  components: {
    'chart-legend': () => import('./ChartLegend'),
  },
  mixins: [chartMixins, style],
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {}
  },
  computed: {
    labelList() {
      return this.data.map((item) => item.DG_APPEAL_TYPE)
    },
    dataList() {
      return this.data.map((item) => {
        return {
          name: item.DG_APPEAL_TYPE,
          value: item.APPEAL_TYPE_TOTAL,
          disactive: item.disactive,
        }
      })
    },
    filteredDataList() {
      return this.dataList.map((item) => {
        if (item.disactive) {
          return {}
        } else {
          return item
        }
      })
    },
    option() {
      return {
        color: this.color,
        tooltip: this.tooltipStyle,
        series: [
          {
            name: '诉求对象构成',
            type: 'pie',
            radius: ['40%', '50%'],
            center: ['50%', '45%'],
            data: this.filteredDataList,
            itemStyle: {
              emphasis: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
            label: {
              formatter: '{text|{b}}\n{number|{d}%}',
              rich: this.rich,
              alignTo: 'labelLine',
            },
            ...this.labelLineStyle,
          },
        ],
      }
    },
  },
  methods: {
    handleDisactiveItem(idx) {
      if (this.data?.[idx]) {
        let data = [...this.data]
        data[idx].disactive = !data[idx].disactive
        this.$emit('update:data', data)
      }
    },
  },
}
</script>

<style lang="scss">
.object-type-chart {
  .chart-container {
    max-height: calc(70% - 24px);
  }

  .chart-legend {
    margin-top: 12px;
    flex: 0 0 max-content;
  }
}
</style>
