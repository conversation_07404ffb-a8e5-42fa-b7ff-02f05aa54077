import { get, post } from '@/http/request'
import { processModule } from '@/api/PATH'

// 数据处理组件

// 分类
export function getClassTree() {
  return post(processModule.classTree)
}

export function addClass(formData) {
  return post(processModule.addClass, { data: JSON.stringify(formData) })
}

export function updateClass(formData) {
  return post(processModule.updateClass, { data: JSON.stringify(formData) })
}

export function deleteClass(formData) {
  return post(processModule.deleteClass, { data: JSON.stringify(formData) })
}

// 组件
export function getList(formData) {
  return post(processModule.list, { data: JSON.stringify(formData) })
}

export function getDetail(id) {
  return post(processModule.detail, { dataComId: id })
}

export function addModule(formData) {
  return post(processModule.addModule, { data: JSON.stringify(formData) })
}

export function updateModule(formData) {
  return post(processModule.updateModule, { data: JSON.stringify(formData) })
}

export function deleteModule(formData) {
  return post(processModule.deleteModule, { data: JSON.stringify(formData) })
}

export function getServiceDict() {
  return post(processModule.serviceDict)
}
