<template>
  <base-card
    id="object-manage"
    class="y-page">
    <aside-bar
      ref="asideBar"
      @set-current="setCurrent($event)"></aside-bar>
    <div class="y-container no-padding">
      <div class="y-bar">
        <h2 class="y-title">诉求对象列表</h2>
        <span>关键字</span>
        <el-input
          v-model="keyword"
          v-trim
          size="small"
          style="width: 220px"
          clearable
          placeholder="请输入关键字进行搜索"></el-input>
        <el-button
          v-debounce="fetchData"
          class="mini"
          type="primary">
          <i class="el-icon-search"></i>
          搜索
        </el-button>
        <el-button
          class="mini"
          type="primary"
          @click="downloadTemplate">
          <i class="el-icon-download"></i>
          下载模板
        </el-button>
        <el-upload
          ref="upload"
          action=""
          accept=".xlsx, .xls"
          :auto-upload="false"
          :show-file-list="false"
          :multiple="false"
          :on-change="importObject">
          <el-button
            class="mini"
            type="primary"
            plain
            style="margin-left: 8px">
            <svg-icon icon="enter"></svg-icon>
            批量导入
          </el-button>
        </el-upload>
        <!-- <el-button
          class="mini"
          type="primary">
          <svg-icon icon="robot-plus"></svg-icon>
          智能生成
        </el-button> -->
      </div>
      <div
        v-if="!objectList || objectList?.length === 0"
        class="y-container--tight no-padding">
        <el-empty
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </div>
      <div
        v-else
        ref="objectList"
        class="object-item-wrapper y-card-wrapper y-container--tight no-padding"
        @mousewheel="throttleHandleScroll">
        <object-card
          v-for="item in virtualList"
          :data="item"
          :active-menu="activeMenu"
          @edit-object="openEdit(item)"
          @delete-object="deleteObject(item)"></object-card>
      </div>
    </div>
    <el-drawer
      title="编辑诉求对象"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { throttle } from '@/utils'
import { getObjectList, importObject, deleteObject } from '@/api/object-manage'

export default {
  name: 'VirtualList',
  components: {
    'aside-bar': () => import('./components/AsideBar.vue'),
    'object-card': () => import('./components/ObjectCard.vue'),
    'edit-form': () => import('./components/EditForm.vue'),
  },
  props: {},
  data() {
    return {
      activeMenu: null,
      keyword: '',
      formData: {
        pageSize: 9999,
        pageIndex: 1,
        pageType: 3,
      },
      objectList: [],
      startIdx: 0,
      listSize: 15,
      gap: 16,
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {
    virtualList() {
      return this.objectList.slice(this.startIdx, this.startIdx + this.listSize)
    },
    wrapperHeight() {
      return this.$refs?.objectList.clientHeight || 0
    },
    childHeight() {
      return this.$refs?.objectList.children[0].clientHeight || 0
    },
    bufferHeight() {
      return (this.childHeight * this.listSize + this.gap * (this.listSize - 1) - this.wrapperHeight) / 2
    },
  },
  watch: {
    activeMenu: {
      handler(menu) {
        if (menu || menu === 0) {
          this.formData = {
            pageSize: 9999,
            pageIndex: 1,
            pageType: 3,
          }
          this.fetchData()
        }
      },
      deep: true,
    },
  },
  created() {},
  mounted() {
    this.throttleHandleScroll = throttle(this.handleScroll, 300)
  },
  beforeDestroy() {},
  methods: {
    async fetchData() {
      let loadingInstance = this.$loading({ target: '.object-item-wrapper' })

      const payload = {
        appealName: this.keyword,
        appealType: this.activeMenu[0],
      }
      Object.assign(payload, this.formData)
      const [err, res] = await getObjectList(payload)
      if (res) {
        this.objectList = res.data
      }

      this.$nextTick(() => {
        loadingInstance.close()
      })
    },
    async importObject(file, fileList) {
      if (
        file.raw.type !== 'application/vnd.ms-excel' &&
        file.raw.type !== 'application/x-excel' &&
        file.raw.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ) {
        this.$message({
          message: '文件类型不正确，请重新上传',
          type: 'error',
          duration: 800,
        })
        return false
      }

      const payload = {
        appealType: this.activeMenu?.[0],
        file: file.raw,
      }

      const [err, res] = await importObject(payload)
      if (!err && res?.msg.includes('成功')) {
        this.$message({
          message: res.msg,
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      } else if (!err) {
        this.$message({
          message: res.msg,
          type: 'error',
          duration: 800,
          onClose: () => {},
        })
      }
    },
    async deleteObject(data) {
      try {
        await this.$confirm('此操作将永久删除选中诉求对象, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const payload = {
        appealId: data.APPEAL_INFO_ID,
        contentBefore: data,
      }
      const [err, res] = await deleteObject(payload)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    openEdit(data) {
      if (data) {
        this.editDrawerData = data
      } else {
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    downloadTemplate() {
      window.open(process.env.VUE_APP_BASE_API + '/dg-portal/template/template_appeal_info.xlsx', '_blank')
    },
    setCurrent(menu) {
      this.activeMenu = menu
    },
    handleScroll(e) {
      const flag = e.deltaY > 0
      const scrollTop = this.$refs.objectList.scrollTop
      const scrollBottom = scrollTop + this.wrapperHeight

      // console.log('wra-buf', this.wrapperHeight, this.bufferHeight)
      // console.log('top-bot', scrollTop, scrollBottom)
      // console.log(flag, this.startIdx, this.listSize)
      if (flag) {
        // 下滑
        if (this.startIdx + this.listSize >= this.objectList.length) {
          return false
        }
        const increment = Math.floor((scrollBottom - this.wrapperHeight - this.bufferHeight) / (this.childHeight + this.gap))
        // console.log('inc', increment)

        if (increment > 0) {
          this.startIdx += increment

          if (this.startIdx + this.listSize > this.objectList.length) {
            this.startIdx = this.objectList.length - this.listSize
          }

          this.$refs.objectList.scrollTop -= increment * (this.childHeight + this.gap)
        }
      } else {
        // 上滑
        if (this.startIdx <= 0) {
          return false
        }

        const discrement = Math.floor((this.bufferHeight - scrollTop) / (this.childHeight + this.gap))
        // console.log('disc', discrement)

        if (discrement > 0) {
          this.startIdx -= discrement

          if (this.startIdx < 0) {
            this.startIdx = 0
          }

          this.$refs.objectList.scrollTop += discrement * (this.childHeight + this.gap)
        }
      }
      // console.log(flag, this.startIdx, this.listSize)
    },
  },
}
</script>

<style lang="scss">
#object-manage {
  @include flex-row;
  background-color: transparent;

  > .aside-bar {
    margin-right: 16px;
    border-radius: 4px;
    background-color: $bgColor;
  }

  > .y-container {
    width: calc(100% - 304px);
    min-width: 685px;

    .y-bar {
      justify-content: flex-start;
      background-color: $bgColor;
      border-radius: 4px;
    }
  }

  .object-item-wrapper {
    margin-top: 16px;
    padding-bottom: 16px;
    grid-auto-rows: 186px;
    grid-template-columns: repeat(auto-fill, minmax(416px, 1fr));
  }
}
</style>
