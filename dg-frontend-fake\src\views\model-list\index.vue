<template>
  <base-card
    id="model-list"
    class="y-page">
    <div class="y-bar">
      <h2 class="y-title">模型列表</h2>
      <span>关键字</span>
      <el-input
        v-model="formData.name"
        v-trim
        size="small"
        style="width: 220px"
        clearable
        placeholder="请输入关键字进行搜索"></el-input>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
      <el-button
        class="mini"
        type="primary"
        @click.native="openEdit(null)">
        <svg-icon icon="add"></svg-icon>
        新建模型
      </el-button>
    </div>
    <empty-wrapper
      class="y-container--tight"
      :toggle="!modelList || modelList?.length === 0"
      v-loading="loading">
      <div
        class="etl-wrapper y-card-wrapper y-container--tight no-padding"
        v-reset-scroll>
        <config-card
          v-for="item in modelList"
          :key="item.etlId"
          :data="item">
          <template #left>
            <div class="title-wrapper">
              <h5 class="title">
                {{ item.NAME }}
              </h5>
              <span
                class="taggy info"
                style="margin-left: 4px"
                >{{ `Ver. ${item.VERSION || '-'}` }}</span
              >
              <span
                :class="['taggy', getTag(item.STATUS, 'class', 'STATUS')]"
                style="margin-left: 8px"
                >{{ getTag(item.STATUS, 'label', 'STATUS') }}</span
              >
              <span
                :class="['taggy', getTag(item.TYPE, 'class', 'TYPE')]"
                style="margin-left: 8px"
                >{{ getTag(item.TYPE, 'label', 'TYPE') }}</span
              >
            </div>
            <p class="desc">{{ item.DESC }}</p>
            <div class="tags">
              <div class="tag">更新时间：{{ item.CREATE_TIME || item.UPDATE_TIME }}</div>
            </div>
          </template>
          <template #right>
            <div class="switch">
              <span class="prefix">启用状态：</span>
              <el-switch
                v-model="item.eltState"
                active-color="#52C41A"
                :active-value="1"
                :inactive-value="0"
                @change="handleSwitch(item)">
              </el-switch>
            </div>
            <!-- <etl-version-selector :etl-id="item.etlId"></etl-version-selector> -->
            <div class="btn-set">
              <el-button
                class="mini"
                type="danger"
                plain
                size="small"
                @click.native="deleteModel(item)"
                >删除</el-button
              >
              <el-button
                class="mini"
                type="primary"
                plain
                size="small"
                @click.native="openEdit(item)"
                >编辑</el-button
              >
              <el-button
                class="mini"
                type="primary"
                plain
                size="small"
                @click.native="downloadModelFile(item)"
                >下载模型</el-button
              >
              <el-button
                class="mini"
                type="primary"
                plain
                size="small"
                @click.native="generateModelVersion(item)"
                >生成模型版本</el-button
              >
              <el-button
                class="mini"
                type="primary"
                size="small"
                @click.native="openCodeEditor(item)"
                >开发模型</el-button
              >
            </div>
          </template>
        </config-card>
      </div>
    </empty-wrapper>
    <div class="footer">
      <pagination
        :current-page.sync="formData.pageIndex"
        :page-size.sync="formData.pageSize"
        :total="total"
        @page="fetchData"></pagination>
    </div>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
    <el-dialog
      title="模型开发"
      custom-class="code-editor"
      fullscreen
      append-to-body
      @closed="editorUrl = ''"
      :visible.sync="showCodeEditor">
      <iframe :src="editorUrl"></iframe>
    </el-dialog>
  </base-card>
</template>

<script>
import { getModelList, deleteModel, generateVersion, downloadModel } from '@/api/model-list'
import ConfigCard from '@/components/ConfigCard'
import EtlVersionSelector from '@/components/EtlVersionSelector'
import EditForm from './components/EditForm'

export default {
  name: 'ModelList',
  components: {
    ConfigCard,
    // EtlVersionSelector,
    EditForm,
  },
  props: {},
  data() {
    return {
      total: 0,
      loading: false,
      formData: {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
        name: '',
      },
      modelList: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
      showCodeEditor: false,
      editorUrl: '',
    }
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true
      const [err, res] = await getModelList(this.formData)
      if (res) {
        this.modelList = res.data
        this.total = res.totalRow
      }

      this.loading = false
    },
    async handleSwitch(data) {
      // const payload = {
      //   etlId: data.etlId,
      //   status: data.eltState,
      // }
      // const [err, res] = await updateEtl(payload)
      // if (!err) {
      //   this.$message({
      //     message: '操作成功！',
      //     type: 'success',
      //     duration: 800,
      //     onClose: () => {},
      //   })
      // } else {
      //   this.fetchData()
      // }
    },
    async deleteModel(item) {
      try {
        await this.$confirm('此操作将永久删除该模型, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }
      const [err, res] = await deleteModel(item.ID)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    getTag(status, flag, type) {
      const map = {
        STATUS: {
          class: {
            2: 'primary',
            1: 'success',
          },
          label: {
            2: '待发布',
            1: '已发布',
          },
        },
        TYPE: {
          class: {
            1: 'warning',
            2: 'danger',
          },
          label: {
            1: '公有',
            2: '私有',
          },
        },
      }

      return map[type][flag][status]
    },
    openEdit(data) {
      if (data) {
        this.editDrawerTitle = '编辑模型'
        this.editDrawerData = data
      } else {
        this.editDrawerTitle = '添加模型'
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    openCodeEditor(data) {
      const { token } = data
      this.editorUrl = `http://172.16.91.103:8000/hub/login?token=${token}`
      // this.showCodeEditor = true
      window.open(this.editorUrl)
    },
    async generateModelVersion(item) {
      const payload = {
        SYS_URL: item.SYS_URL,
        ID: item.ID,
      }
      const [err, res] = await generateVersion(payload)
      if (!err) {
        const url = URL.createObjectURL(new Blob([res]))
        window.open(url)
      }
    },
    async downloadModelFile(item) {
      const [err, res] = await downloadModel(item.SYS_URL)
      if (!err) {
        this.$message({
          message: '下载成功！',
          type: 'success',
          duration: 800,
        })
      }
    },
  },
}
</script>

<style lang="scss">
#model-list {
  @import '@/assets/style/modules/config-card-page-with-footer.scss';
  background-color: transparent;
}

.code-editor {
  .el-dialog__body {
    padding: 0;
    width: 100%;
    height: calc(100% - 64px);

    iframe {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
