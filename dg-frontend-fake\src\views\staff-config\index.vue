<template>
  <base-card
    id="staff-config"
    class="y-page">
    <aside-bar
      ref="asideBar"
      @set-current="setCurrent($event)"></aside-bar>
    <div class="y-container no-padding">
      <div class="y-bar">
        <h2 class="y-title">证监人员信息配置</h2>
        <span>关键字</span>
        <el-input
          v-model="keyword"
          v-trim
          size="small"
          style="width: 220px"
          clearable
          placeholder="请输入关键字进行搜索"></el-input>
        <el-button
          v-debounce="fetchData"
          class="mini"
          type="primary">
          <i class="el-icon-search"></i>
          搜索
        </el-button>
        <el-button
          class="mini"
          type="primary"
          @click="openEdit(null)">
          <svg-icon icon="add"></svg-icon>
          新增
        </el-button>
        <el-button
          class="mini"
          type="primary"
          @click="downloadTemplate">
          <i class="el-icon-download"></i>
          下载模板
        </el-button>
        <el-upload
          ref="upload"
          action=""
          accept=".xlsx, .xls"
          :auto-upload="false"
          :show-file-list="false"
          :multiple="false"
          :on-change="importStaff">
          <el-button
            class="mini"
            type="primary"
            plain
            style="margin-left: 8px">
            <svg-icon icon="enter"></svg-icon>
            批量导入
          </el-button>
        </el-upload>
      </div>
      <empty-wrapper
        class="y-container--tight"
        :toggle="!staffList || staffList?.length === 0"
        v-loading="loading">
        <div class="staff-card-wrapper y-card-wrapper y-container--tight no-padding">
          <staff-card
            v-for="item in staffList"
            :key="item.INFO_ID"
            :data="item"
            @edit-staff="openEdit(item)"
            @delete-staff="deleteStaff(item)"></staff-card>
        </div>
      </empty-wrapper>
    </div>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        :active-menu="activeMenu"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getStaffList, importStaff, deleteStaff } from '@/api/csrc-info'
import AsideBar from './components/AsideBar'
import StaffCard from './components/StaffCard'
import EditForm from './components/EditForm'
import { downloadFile } from '@/utils'

export default {
  name: 'StaffConfig',
  components: {
    AsideBar,
    EditForm,
    StaffCard,
  },
  props: {},
  data() {
    return {
      loading: false,
      activeMenu: '',
      staffList: [],
      keyword: '',
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {},
  watch: {
    activeMenu: {
      handler(menu) {
        if (menu || menu === 0) {
          this.fetchData()
        }
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true

      const payload = {
        orgId: this.activeMenu,
        infoName: this.keyword,
      }
      const [err, res] = await getStaffList(payload)
      if (res) {
        this.staffList = res.data
      }

      this.loading = false
    },
    async importStaff(file, fileList) {
      if (
        file.raw.type !== 'application/vnd.ms-excel' &&
        file.raw.type !== 'application/x-excel' &&
        file.raw.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ) {
        this.$message({
          message: '文件类型不正确，请重新上传',
          type: 'error',
          duration: 800,
        })
        return false
      }

      const payload = {
        orgId: this.activeMenu,
        file: file.raw,
      }

      const [err, res] = await importStaff(payload)
      if (!err && res?.msg.includes('成功')) {
        this.$message({
          message: res.msg,
          type: 'success',
          duration: 3000,
          onClose: () => {
            this.fetchData()
          },
        })
      } else if (!err) {
        this.$message({
          message: res.msg,
          type: 'error',
          duration: 3000,
          onClose: () => {},
        })
      }
    },
    async deleteStaff(data) {
      try {
        await this.$confirm('此操作将永久删除选中人员, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }
      const payload = {
        infoId: data.INFO_ID,
        contentBefore: data,
      }
      const [err, res] = await deleteStaff(payload)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    setCurrent(item) {
      this.activeMenu = item
    },
    openEdit(data) {
      if (data) {
        this.editDrawerTitle = '证监人员信息编辑'
        this.editDrawerData = data
      } else {
        this.editDrawerTitle = '证监人员信息新增'
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    downloadTemplate() {
      // downloadFile('/dg-portal/template/template_informant_info.xlsx')
      downloadFile('/dg-portal/servlet/informant?action=ExportTemplate')
    },
  },
}
</script>

<style lang="scss">
#staff-config {
  @include flex-row;

  > .aside-bar {
    border-right: 1px solid $borderColor;
  }

  > .y-container {
    width: calc(100% - 240px);

    .y-bar {
      justify-content: flex-start;
    }
  }

  .staff-card-wrapper {
    padding: 0 24px;
    grid-auto-rows: 208px;
    grid-template-columns: repeat(auto-fill, minmax(440px, 1fr));
  }
}
</style>
