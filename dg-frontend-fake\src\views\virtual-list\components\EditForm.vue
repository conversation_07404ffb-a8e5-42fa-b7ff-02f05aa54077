<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="90px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="公司名称"
            prop="appealName">
            <el-input
              v-model="formData.appealName"
              v-trim
              clearable
              placeholder="请输入公司名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="公司全称"
            prop="appealFullName">
            <el-input
              v-model="formData.appealFullName"
              v-trim
              clearable
              placeholder="请输入公司全称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="类别"
            prop="appealType">
            <el-select
              v-model="formData.appealType"
              placeholder="请选择类别">
              <el-option
                v-for="(value, key) in classList"
                :label="value"
                :value="key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="机构代码"
            prop="appealCode">
            <el-input
              v-model="formData.appealCode"
              v-trim
              clearable
              placeholder="请输入机构代码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="别名"
            prop="anotherName">
            <div class="y-multi-input">
              <div
                class="y-multi-input_item"
                v-for="(item, idx) in formData.anotherName">
                <el-input
                  v-model="formData.anotherName[idx]"
                  v-trim
                  clearable
                  placeholder="请输入别名"></el-input>
                <el-button
                  type="primary"
                  plain
                  icon="el-icon-plus"
                  @click.native="addInput"
                  v-if="idx === 0"></el-button>
                <el-button
                  type="danger"
                  plain
                  icon="el-icon-delete"
                  @click.native="removeInput(idx)"
                  v-else></el-button>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="扩展字段"
            prop="extJson">
            <el-input
              v-model="formData.extJson"
              v-trim
              clearable
              placeholder="请输入扩展字段"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { getClassList, getObjectDetail, updateObject } from '@/api/object-manage'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      formData: {
        appealName: null,
        appealFullName: null,
        appealType: null,
        appealCode: null,
        anotherName: [''],
        extJson: null,
      },
      rules: {
        appealName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
        appealType: [{ required: true, message: '请选择类别', trigger: 'blur' }],
      },
      classList: [],
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data?.APPEAL_INFO_ID) {
        this.getObjectDetail(this.data.APPEAL_INFO_ID)
      } else {
        this.formData = {
          appealName: null,
          appealFullName: null,
          appealType: null,
          appealCode: null,
          anotherName: [''],
          extJson: null,
        }
      }
      this.getClassList()
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const payload = { ...this.formData }
        payload.anotherName = payload.anotherName.filter((item) => item.trim()).join(',')
        payload.appealId = this.data.APPEAL_INFO_ID

        const [err, res] = await updateObject(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
    async getObjectDetail(id) {
      const [err, res] = await getObjectDetail(id)
      if (res) {
        const data = res.data
        this.contentBefore = data
        this.$set(this.formData, 'appealName', data?.APPEAL_NAME)
        this.$set(this.formData, 'appealType', data?.APPEAL_TYPE)
        this.$set(this.formData, 'appealCode', data?.APPEAL_CODE)
        this.$set(this.formData, 'appealFullName', data?.APPEAL_FULLNAME)
        this.$set(this.formData, 'anotherName', data?.ANOTHER_NAME.split(','))
        this.$set(this.formData, 'extJson', data?.EXT_JSON)
      }
    },
    async getClassList() {
      const [err, res] = await getClassList({ typeName: '' })
      if (res) {
        this.classList = res?.data
      }
    },
    addInput() {
      this.formData.anotherName.push('')
    },
    removeInput(idx) {
      this.formData.anotherName.splice(idx, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
