function scrollHorizontal(e) {
  e.preventDefault()
  // 增加滚动速度系数，使滚动更流畅
  const scrollSpeed = 1.5
  this.scrollLeft += e.deltaY * scrollSpeed
}

export default {
  bind(el, binding, vnode) {
    // 使用mousewheel和wheel事件，提高兼容性
    el.addEventListener('mousewheel', scrollHorizontal, { passive: false })
    el.addEventListener('wheel', scrollHorizontal, { passive: false })
  },
  unbind(el) {
    // 移除两种事件监听
    el.removeEventListener('mousewheel', scrollHorizontal)
    el.removeEventListener('wheel', scrollHorizontal)
  }
}
