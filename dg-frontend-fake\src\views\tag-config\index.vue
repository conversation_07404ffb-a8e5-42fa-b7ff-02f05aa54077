<template>
  <base-card
    v-if="false"
    id="tag-config"
    class="y-page">
    <aside-bar
      ref="asideBar"
      @set-current="setCurrent($event)"></aside-bar>
    <div class="y-container no-padding">
      <div class="y-bar">
        <h2 class="y-title">标签列表</h2>
        <span>关键字</span>
        <el-input
          v-trim
          size="small"
          style="width: 220px"
          clearable
          placeholder="请输入关键字进行搜索"></el-input>
        <el-button
          v-debounce="fetchData"
          class="mini"
          type="primary">
          <i class="el-icon-search"></i>
          搜索
        </el-button>
        <el-button
          class="mini"
          type="primary"
          @click.native="openEdit(null)">
          <svg-icon icon="add"></svg-icon>
          添加标签
        </el-button>
      </div>
      <empty-wrapper
        class="y-container--tight"
        :toggle="!ruleList || ruleList?.length === 0"
        v-loading="loading">
        <div class="rule-item-wrapper y-container--tight no-padding">
          <div
            class="rule-item"
            v-for="item in ruleList">
            <h5 class="name">{{ item.name }}</h5>
            <div class="tag-wrapper">
              <span class="prefix">规则描述：包含</span>
              <span
                class="tag"
                v-for="tag in item.tags"
                >{{ tag }}</span
              >
              <span class="prefix">其中之一，同时包括</span>
              <span
                class="tag"
                v-for="tag in item.tags2"
                >{{ tag }}</span
              >
            </div>
            <span class="date">添加日期：{{ item.date }}</span>
            <el-button
              class="mini"
              type="primary"
              plain
              @click.native="openEdit(tag)">
              配置规则
            </el-button>
          </div>
        </div>
      </empty-wrapper>
    </div>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="1200">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
  <div v-else>{{ '未开发功能\n敬请期待' }}</div>
</template>

<script>
import AsideBar from '@/views/object-manage/components/AsideBar.vue'
import EditForm from './components/EditForm'

export default {
  name: 'TagConfig',
  components: {
    AsideBar,
    EditForm,
  },
  props: {},
  data() {
    return {
      loading: false,
      activeMenu: '',
      ruleList: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {},
  watch: {
    activeMenu(menu) {
      if (menu) {
        this.fetchData()
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true

      this.ruleList = [
        {
          name: '咨询制度规则',
          tags: ['拟上市', 'IPO', '欲上市'],
          tags2: ['拟上市', 'IPO', '欲上市'],
          date: '2023-08-15 17:45:42',
        },
        {
          name: '违法违规行为',
          tags: ['拟上市', 'IPO', '欲上市'],
          tags2: ['拟上市', 'IPO', '欲上市'],
          date: '2023-08-15 17:45:42',
        },
      ]

      this.loading = false
    },
    setCurrent(item) {
      this.activeMenu = item
    },
    openEdit(data) {
      this.editDrawerTitle = '添加标签'
      if (data) {
        this.editDrawerTitle = '配置标签'
        this.editDrawerData = data
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
  },
}
</script>

<style lang="scss">
#tag-config {
  @include flex-row;

  > .aside-bar {
    border-right: 1px solid $borderColor;
  }

  > .y-container {
    width: calc(100% - 288px);

    .y-bar {
      justify-content: flex-start;
      border-bottom: 1px solid $borderColor;
    }
  }

  .rule-item-wrapper {
    .rule-item {
      position: relative;
      margin: 16px 24px;
      margin-bottom: 0;
      padding-bottom: 16px;
      width: calc(100% - 48px);
      border-bottom: 1px solid $borderColor;

      .name {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        color: $txtColor;
      }

      .tag-wrapper {
        @include flex-row;
        justify-content: flex-start;
        flex-wrap: wrap;
        margin-bottom: 4px;

        .tag {
          padding: 0 8px;
          margin-left: 8px;
          font-size: 14px;
          font-weight: normal;
          line-height: 24px;
          color: $txtColor;
          border-radius: 4px;
          background: $bgColor-dark;
        }
      }

      .prefix,
      .date {
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        color: $txtColor-light;
      }

      .el-button {
        position: absolute;
        right: 0;
        bottom: 16px;
      }
    }
  }
}
</style>
