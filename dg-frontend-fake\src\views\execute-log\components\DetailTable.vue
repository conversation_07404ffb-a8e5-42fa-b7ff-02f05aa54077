<template>
  <div class="detail-table y-container--tight no-padding">
    <div class="y-container--tight">
      <div
        v-if="groupedShowList?.length > 0"
        class="table-data y-table el-table">
        <table>
          <col width="15%" />
          <col width="35%" />
          <col width="15%" />
          <col width="35%" />
          <tr v-for="group in groupedShowList">
            <template v-for="item in group">
              <th class="el-table__cell">
                <div class="cell">
                  <overflow-text
                    :max="8"
                    :content="headList[item?.[0]] || item?.[0]"></overflow-text>
                </div>
              </th>
              <td
                class="el-table__cell"
                v-if="group.length % 2 === 0">
                <div class="cell">
                  <overflow-text
                    :max="120"
                    :content="item?.[1]"></overflow-text>
                </div>
              </td>
              <td
                class="el-table__cell"
                colspan="3"
                v-else>
                <div class="cell">
                  <overflow-text
                    :max="300"
                    :content="item?.[1]"></overflow-text>
                </div>
              </td>
            </template>
          </tr>
        </table>
      </div>
    </div>
    <div class="footer y-bar">
      <el-button
        type="primary"
        size="small"
        @click="$emit('close-detail')"
        >确认</el-button
      >
    </div>
  </div>
</template>

<script>
import { getDetailHead, getDetail } from '@/api/etl-log'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      dataList: null,
      headList: null,
    }
  },
  computed: {
    pickedList() {
      if (!this.headList || !this.dataList) {
        return []
      }
      const filteredList = Object.entries(this.headList)
      const limit = 24
      const stack = []
      filteredList.forEach((item, idx, arr) => {
        if (this.dataList[item[0]] && this.dataList[item[0]].length > limit) {
          stack.push(item)
          arr.splice(idx, 1)
        }
      })
      stack.sort((a, b) => this.dataList[a[0]].length - this.dataList[b[0]].length)
      return filteredList.concat(stack)
    },
    // dataList两个一组分的新数组
    groupedShowList() {
      return this.pickedList.reduce((acc, cur, idx) => {
        let item = [cur[1], this.dataList[cur[0]]] // key, value
        if (!acc[acc.length - 1] || acc[acc.length - 1].length === 2) {
          acc.push([item])
        } else {
          acc[acc.length - 1].push(item)
        }
        return acc
      }, [])
    },
  },
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      if (this.data && JSON.stringify(this.data) != '{}') {
        this.getDetail()
        this.getDetailHead()
      } else {
        this.dataList = null
        this.headList = null
      }
    },
    async getDetail() {
      const [err, res] = await getDetail(this.data.DATA_ROW_ID)
      if (res) {
        this.dataList = res.data
      }
    },
    async getDetailHead() {
      const [err, res] = await getDetailHead(this.data.ETL_ID)
      if (res) {
        this.headList = res.data
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.detail-table::v-deep {
  > .y-container--tight {
    .y-table {
      flex: 0 0 max-content;

      th {
        background-color: transparentize($themeColor, 0.95);
      }
    }
  }

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }
}
</style>
