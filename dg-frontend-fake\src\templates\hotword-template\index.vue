<template>
  <base-card
    id="hotword-template"
    class="y-page">
    <aside-bar
      ref="asideBar"
      :name="name"
      :module-type="moduleType"
      @set-current="setCurrent($event)"></aside-bar>
    <div class="y-container no-padding">
      <div class="y-bar">
        <h2 class="y-title">{{ name }}列表</h2>
        <el-button
          class="mini"
          type="primary"
          plain
          @click="generateModel">
          <i class="el-icon-refresh"></i>
          生成模型
        </el-button>
        <el-button
          class="mini"
          type="primary"
          plain
          @click="downloadTemplate">
          <i class="el-icon-download"></i>
          下载模板
        </el-button>
        <el-button
          class="mini"
          type="primary"
          plain
          @click="exportHotword">
          <svg-icon icon="exit"></svg-icon>
          导出{{ name }}
        </el-button>
        <el-upload
          ref="upload"
          action=""
          accept=".xlsx, .xls"
          :auto-upload="false"
          :show-file-list="false"
          :multiple="false"
          :on-change="importHotword">
          <el-button
            class="mini"
            type="primary"
            plain
            style="margin-left: 8px">
            <svg-icon icon="enter"></svg-icon>
            导入{{ name }}
          </el-button>
        </el-upload>
        <el-button
          class="mini"
          type="danger"
          plain
          style="margin-left: 8px"
          @click.native="deleteHotword(null)">
          <svg-icon icon="remove"></svg-icon>
          批量删除
        </el-button>
        <el-button
          class="mini"
          type="primary"
          @click.native="openEdit(null)">
          <svg-icon icon="add"></svg-icon>
          添加{{ name }}
        </el-button>
      </div>
      <div class="y-container--tight">
        <el-form
          ref="searchForm"
          :inline="true"
          :model="searchForm"
          size="small"
          style="margin-top: 16px">
          <el-form-item
            label="关键字"
            prop="keyword">
            <el-input
              v-model="searchForm.keyword"
              v-trim
              style="width: 220px"
              clearable
              placeholder="请输入关键字进行搜索"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              v-debounce="fetchData"
              class="mini"
              type="primary">
              <i class="el-icon-search"></i>
              搜索
            </el-button>
          </el-form-item>
        </el-form>
        <el-table
          :data="hotwordList"
          v-loading="loading"
          v-reset-scroll="'div.el-table__body-wrapper'"
          @selection-change="handleSelectionChange"
          stripe
          height="100%"
          fit
          ref="table"
          style="width: 100%">
          <el-table-column
            type="selection"
            width="50">
          </el-table-column>
          <el-table-column
            type="index"
            label="序号"
            width="50">
          </el-table-column>
          <el-table-column
            :label="name"
            prop="WORD_NAME"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            :label="name + '类别'"
            prop="TYPE_NAME"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            label="创建时间"
            prop="OPER_TIME"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            label="创建人"
            prop="OPER_NAME"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            label="操作"
            width="100"
            fixed="right">
            <template slot-scope="scope">
              <el-link
                type="primary"
                :underline="false"
                @click.native="openEdit(scope.row)"
                >编辑</el-link
              >
              <el-link
                type="danger"
                :underline="false"
                @click.native="deleteHotword(scope.row)"
                >删除</el-link
              >
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/no-info.png')"
            description="暂无信息"></el-empty>
        </el-table>
      </div>
      <pagination
        :current-page.sync="formData.pageIndex"
        :page-size.sync="formData.pageSize"
        :total="total"
        @page="fetchData"></pagination>
    </div>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        :active-menu="activeMenu"
        :name="name"
        :module-type="moduleType"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getList, generateModel, importHotword, exportHotword, deleteHotword } from '@/api/hotword-manage'
import AsideBar from './components/AsideBar.vue'
import EditForm from './components/EditForm.vue'
import { downloadFile } from '@/utils'

export default {
  name: 'HotwordTemplate',
  components: {
    AsideBar,
    EditForm,
  },
  props: {
    name: {
      type: String,
      default: '热词',
    },
    moduleType: {
      type: String,
      default: '1',
    },
  },
  data() {
    return {
      loading: false,
      activeMenu: null,
      total: 0,
      searchForm: {
        keyword: '',
      },
      formData: {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
      },
      hotwordList: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
      tableSelection: [],
    }
  },
  computed: {},
  watch: {
    activeMenu: {
      handler(menu) {
        if (menu || menu === 0) {
          this.formData = {
            pageSize: 15,
            pageIndex: 1,
            pageType: 3,
          }
          this.fetchData()
        }
      },
      deep: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true

      const payload = {
        key: this.searchForm.keyword,
        typeId: this.activeMenu.id,
        moduleType: this.moduleType,
      }
      Object.assign(payload, this.formData)
      const [err, res] = await getList(payload)
      if (res) {
        this.hotwordList = res.data
        this.total = res.totalRow
      }

      this.loading = false
    },
    async exportHotword() {
      this.handleCheckActiveMenu()
      const payload = {
        typeId: this.activeMenu.id,
        moduleType: this.moduleType,
      }
      const [err, res] = await exportHotword(payload)
      if (!err) {
        this.$message({
          message: '正在导出中，请移步系统管理-导出记录进行查看',
          type: 'success',
          duration: 800,
          onClose: () => {},
        })
      }
    },
    async importHotword(file, fileList) {
      this.handleCheckActiveMenu()
      if (
        file.raw.type !== 'application/vnd.ms-excel' &&
        file.raw.type !== 'application/x-excel' &&
        file.raw.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ) {
        this.$message({
          message: '文件类型不正确，请重新上传',
          type: 'error',
          duration: 800,
        })
        return false
      }

      const payload = {
        file: file.raw,
        moduleType: this.moduleType,
      }

      const [err, res] = await importHotword(this.activeMenu.id, payload)
      if (!err && res?.msg.includes('成功')) {
        this.$message({
          message: res.msg,
          type: 'success',
          duration: 3000,
          onClose: () => {
            this.fetchData()
          },
        })
      } else if (!err) {
        this.$message({
          message: res.msg,
          type: 'error',
          duration: 3000,
          onClose: () => {},
        })
      }
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
    },
    async deleteHotword(data) {
      if (!data && this.tableSelection?.length === 0) {
        this.$message({
          message: '请选择至少一项进行删除',
          duration: 800,
        })
        return false
      }
      try {
        await this.$confirm('此操作将永久删除选中目标, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const payload = {
        wordIds: data ? [data.ID] : this.tableSelection.map((i) => i.ID),
      }
      const [err, res] = await deleteHotword(payload)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    async generateModel() {
      const payload = {
        moduleType: this.moduleType,
      }
      const [err, res] = await generateModel(payload)
      if (!err) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 800,
          onClose: () => {},
        })
      }
    },
    openEdit(data) {
      if (data) {
        this.editDrawerTitle = '编辑' + this.name
        this.editDrawerData = data
      } else {
        this.editDrawerTitle = '新增' + this.name
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    downloadTemplate() {
      downloadFile('/dg-portal/servlet/hotWords?action=exportTemplate&moduleType=' + this.moduleType)
    },
    setCurrent(menu) {
      this.activeMenu = menu
    },
    handleCheckActiveMenu() {
      if (!this.activeMenu) {
        this.$message({
          message: '请选择菜单再进行操作',
          type: 'warning',
        })
      }
    },
  },
}
</script>

<style lang="scss">
#hotword-template {
  @include flex-row;

  > .aside-bar {
    border-right: 1px solid $borderColor;
  }

  > .y-container {
    width: calc(100% - 240px);

    .y-bar {
      justify-content: flex-start;
      border-bottom: 1px solid $borderColor;
    }

    > .y-container--tight {
      padding-bottom: 0;
      border-bottom: 1px solid $borderColor;

      .el-table {
        &::before {
          content: none;
        }
      }
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }
}
</style>
