<template>
  <div class="aside-bar">
    <div class="y-bar">
      <h2 class="y-title">诉求对象分类</h2>
      <svg-icon
        icon="add"
        @click.native="openDialog(null)"></svg-icon>
    </div>
    <div
      class="y-bar"
      style="padding-top: 0">
      <el-input
        v-model="keyword"
        v-trim
        placeholder="请输入关键字"
        size="small"
        clearable
        suffix-icon="el-icon-search"></el-input>
    </div>
    <class-menu
      v-loading="loading"
      :list="filteredList"
      :active-menu="activeMenu[0]"
      id-prop="0"
      @set-current="setCurrent($event)">
      <template v-slot="{ item }">
        <span>{{ item[1] }}</span>
        <el-dropdown
          trigger="click"
          placement="top">
          <div>
            <svg-icon
              icon="more"
              v-show="item[0] === activeMenu[0]"></svg-icon>
          </div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              icon="el-icon-edit"
              @click.native="openDialog(item)"
              >编辑</el-dropdown-item
            >
            <el-dropdown-item
              icon="el-icon-delete"
              @click.native="deleteClass(item)"
              >删除</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </class-menu>
    <el-dialog
      :title="dialogTitle"
      width="500px"
      :visible.sync="dialogShow">
      <el-form
        ref="editForm"
        class="clearfix"
        :model="editForm"
        :rules="rules"
        label-position="right"
        label-width="90px">
        <el-col :span="24">
          <el-form-item
            label="分类编码"
            prop="appealType">
            <el-input
              v-model="editForm.appealType"
              v-trim
              placeholder="请输入分类编码"
              clearable
              :disabled="dialogType === 'edit'"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="分类名称"
            prop="appealTypeName">
            <el-input
              v-model="editForm.appealTypeName"
              v-trim
              clearable
              placeholder="请输入分类名称"></el-input>
          </el-form-item>
        </el-col>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button
          v-debounce="submitForm"
          type="primary"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getClassList, addClass, updateClass, deleteClass } from '@/api/object-manage'
import ClassMenu from '@/components/ClassMenu'

export default {
  components: {
    ClassMenu,
  },
  data() {
    return {
      loading: false,
      activeMenu: [],
      keyword: '',
      menuList: [],
      dialogTitle: '',
      dialogShow: false,
      dialogType: '',
      editForm: { appealType: null, appealTypeName: null },
      contentBefore: null,
      rules: {
        appealType: [{ required: true, message: '请输入分类编码', trigger: 'blur' }],
        appealTypeName: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
      },
    }
  },
  computed: {
    filteredList() {
      if (this.keyword.trim()) {
        return this.menuList.filter(([key, value]) => value.includes(this.keyword.trim()))
      }
      return this.menuList
    },
  },
  watch: {},
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true

      const [err, res] = await getClassList({ typeName: '' })
      if (res && res?.data) {
        this.menuList = Object.entries(res?.data)
        // [key,value]
        this.setCurrent(this.menuList?.[0])
      }

      this.loading = false
    },
    setCurrent(item) {
      this.activeMenu = item
      this.$emit('set-current', this.activeMenu)
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const payload = { ...this.editForm }

        let submitAction = addClass
        if (this.dialogType === 'edit') {
          submitAction = updateClass
          payload.contentBefore = this.contentBefore
        }

        const [err, res] = await submitAction(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.dialogShow = false
              this.fetchData()
            },
          })
        }
      })
    },
    async deleteClass(data) {
      try {
        await this.$confirm('此操作将永久删除选中分类, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const payload = {
        appealType: data[0],
        contentBefore: Object.fromEntries([data]),
      }

      const [err, res] = await deleteClass(payload)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    initDialog(data) {
      this.$refs?.editForm?.resetFields()
      if (data) {
        this.dialogTitle = '编辑分类'
        this.dialogType = 'edit'
        this.editForm = { appealType: data[0], appealTypeName: data[1] }
        this.contentBefore = Object.fromEntries([data])
      } else {
        this.dialogTitle = '新增分类'
        this.dialogType = 'add'
        this.editForm = { appealType: null, appealTypeName: null }
        this.contentBefore = null
      }
    },
    openDialog(data) {
      this.initDialog(data)
      this.dialogShow = true
    },
    closeDialog() {
      this.dialogShow = false
    },
  },
}
</script>

<style lang="scss" scoped>
.aside-bar {
  @include flex-col;
  flex-shrink: 0;
  width: 288px;
  height: 100%;

  .y-title {
    line-height: 24px;
  }

  > .y-bar {
    padding: 16px 24px;
    padding-bottom: 8px;

    .svg-icon {
      font-size: 16px;
      color: $txtColor-light;
      cursor: pointer;

      &:hover {
        color: $themeColor;
      }
    }
  }
}
</style>
