<template>
  <base-card
    id="process-module"
    class="y-page">
    <aside-bar
      ref="asideBar"
      @set-current="setCurrent($event)"></aside-bar>
    <div class="y-container no-padding">
      <div class="y-bar">
        <h2 class="y-title">服务组件</h2>
        <span>关键字</span>
        <el-input
          v-model="formData.key"
          v-trim
          size="small"
          style="width: 220px"
          clearable
          placeholder="请输入关键字进行搜索"></el-input>
        <el-button
          v-debounce="fetchData"
          class="mini"
          type="primary">
          <i class="el-icon-search"></i>
          搜索
        </el-button>
        <el-button
          class="mini"
          type="primary"
          @click.native="openEdit(null)">
          <svg-icon icon="add"></svg-icon>
          添加处理组件
        </el-button>
      </div>
      <empty-wrapper
        :toggle="!moduleList || moduleList?.length === 0"
        class="y-container--tight"
        v-loading="loading">
        <div class="module-wrapper y-card-wrapper y-container--tight no-padding">
          <module-card
            v-for="item in moduleList"
            :key="item.DATA_COM_ID"
            :data="item"
            @edit-module="openEdit(item)"
            @delete-module="deleteModule(item)">
          </module-card>
        </div>
      </empty-wrapper>
    </div>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="1200">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        :activeMenu="activeMenu"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getList, getDetail, deleteModule } from '@/api/process-module'
import AsideBar from './components/AsideBar'
import ModuleCard from './components/ModuleCard'
import EditForm from './components/EditForm'

export default {
  name: 'ProcessModule',
  components: {
    AsideBar,
    ModuleCard,
    EditForm,
  },
  props: {},
  data() {
    return {
      loading: false,
      activeMenu: '',
      formData: {
        pageSize: 999,
        pageIndex: 1,
        pageType: 3,
        key: '',
      },
      moduleList: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {},
  watch: {
    activeMenu: {
      handler(menu) {
        if (menu) {
          this.fetchData()
        }
      },
    },
  },
  created() {},
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true

      const payload = {
        ...this.formData,
        typeId: this.activeMenu,
      }
      const [err, res] = await getList(payload)
      if (res) {
        this.moduleList = res.data
      }
      this.loading = false
    },
    openEdit(data) {
      if (data) {
        this.editDrawerTitle = '编辑处理组件'
        this.editDrawerData = data
      } else {
        this.editDrawerTitle = '添加处理组件'
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    async deleteModule(data) {
      try {
        await this.$confirm('此操作将永久删除选中组件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const payload = {
        dataComId: data.DATA_COM_ID,
        contentBefore: data,
      }
      const [err, res] = await deleteModule(payload)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    setCurrent(menu) {
      this.activeMenu = menu
    },
  },
}
</script>

<style lang="scss">
#process-module {
  @include flex-row;
  background-color: transparent;

  > .aside-bar {
    margin-right: 16px;
    border-radius: 4px;
    background-color: $bgColor;
  }

  > .y-container {
    width: calc(100% - 304px);
    min-width: 685px;

    > .y-bar {
      justify-content: flex-start;
      background-color: $bgColor;
      border-radius: 4px;
    }
  }

  .module-wrapper {
    margin-top: 16px;
    grid-auto-rows: 225px;
    grid-template-columns: repeat(auto-fill, minmax(440px, 1fr));
  }
}
</style>
