<template>
  <base-card
    id="data-query"
    class="y-page">
    <aside-bar
      ref="asideBar"
      fixType="1"
      @set-current="setCurrent($event)"
      @change-type="setType($event)"></aside-bar>
    <div class="y-container no-padding">
      <div class="y-bar header">
        <h2 class="y-title">调度数据查询</h2>
        <el-button
          class="mini"
          type="primary"
          plain
          @click.native="exportList">
          <svg-icon icon="exit"></svg-icon>
          数据导出
        </el-button>
      </div>
      <div class="y-bar search-bar">
        <template v-if="type === '1'">
          <span style="margin-left: 16px">时间范围</span>
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            size="small"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="dateChange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
            style="width: 340px">
          </el-date-picker>
        </template>
        <query-set
          ref="querySet"
          :query="query"
          :config-list="queryList"
          :show-range="showRange"
          style="margin: 0 8px"></query-set>
        <el-button
          v-if="!queryList || queryList.length > showRange"
          class="mini"
          type="primary"
          size="small"
          :icon="showRange === 'all' ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
          plain
          @click.native="handleQueryRange">
          高级查询
        </el-button>
        <el-button
          class="mini"
          type="primary"
          size="small"
          plain
          @click.native="resetSearch">
          <svg-icon icon="reset"></svg-icon>
          重置
        </el-button>
        <el-button
          v-debounce="fetchData"
          class="mini"
          type="primary">
          <i class="el-icon-search"></i>
          搜索
        </el-button>
      </div>
      <div class="y-container--tight">
        <el-table
          :data="tableData"
          v-loading="tableLoading"
          v-reset-scroll="'div.el-table__body-wrapper'"
          stripe
          height="100%"
          fit
          ref="table"
          style="width: 100%">
          <template v-if="headList?.length > 0">
            <el-table-column
              v-for="{ column, columnLabel, width, fixed } in headList"
              :key="column"
              :prop="column"
              :label="columnLabel"
              :min-width="width || '100'"
              :fixed="fixed"
              align="left"
              show-overflow-tooltip>
            </el-table-column>
          </template>
          <el-table-column v-else></el-table-column>
          <el-table-column
            label="操作"
            width="50"
            fixed="right">
            <template slot-scope="scope">
              <el-link
                type="primary"
                :underline="false"
                @click.native="openDetail(scope.row)"
                >详情</el-link
              >
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/no-info.png')"
            description="暂无信息"></el-empty>
        </el-table>
      </div>
      <pagination
        :current-page.sync="formData.pageIndex"
        :page-size.sync="formData.pageSize"
        :total="total"
        @page="fetchData"></pagination>
    </div>
    <el-drawer
      title="详细信息"
      :visible.sync="detailVisible"
      direction="rtl"
      :size="1200">
      <detail-table
        ref="detailTable"
        :data="detailData"
        :idData="idData"
        @close-detail="closeDetail"></detail-table>
    </el-drawer>
  </base-card>
</template>

<script>
import {
  getTagQueryList,
  getEtlQueryList,
  getTagHeadList,
  getEtlHeadList,
  getTagDataList,
  getEtlDataList,
  exportByTarget,
  exportByEtl,
} from '@/api/data-query'
import AsideBar from './components/AsideBar'
import QuerySet from '@/components/QuerySet'
import DetailTable from './components/DetailTable'

export default {
  name: 'DataQuery',
  components: {
    AsideBar,
    QuerySet,
    DetailTable,
  },
  props: {},
  data() {
    return {
      activeMenu: null,
      type: null,
      queryList: [],
      headList: [],
      tableData: [],
      tableLoading: false,
      total: 0,
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        startTime: '',
        endTime: '',
      },
      dateRange: [],
      query: [],
      detailVisible: false,
      detailData: null,
      orderTypeList: {
        '00': '预处理',
        '05': '新建工单',
        '02': '处理中',
        '04': '已办结',
        T: '暂存',
      },
      showRange: 3,
    }
  },
  computed: {
    idData() {
      if (this.type === '1') {
        return {
          etlId: this.activeMenu,
        }
      } else if (this.type === '0') {
        try {
          let [ds, table] = this.activeMenu.split('|')
          return {
            tagDsId: ds, // 接口其实用不到
            tagDsTable: table,
          }
        } catch (e) {
          return null
        }
      } else {
        return null
      }
    },
  },
  watch: {
    activeMenu(menu) {
      if (!menu) {
        return false
      }

      // 防止点击目标员一级菜单时触发
      if (this.idData?.tagDsId && !this.idData?.tagDsTable) {
        return false
      }
      this.showRange = 3
      this.getQueryList()
      this.getHeadList()
      this.fetchData()
    },
    type(type) {
      this.formData = {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
      }
      this.dateChange()
      this.query = []
    },
  },
  created() {
    this.initDateRange()
  },
  mounted() {},
  methods: {
    async fetchData() {
      if (!this.idData) {
        return false
      }

      if (
        this.query.some((item) => {
          item.range === '2' && item.value1 && !item.value2 && item.value2 !== 0
        })
      ) {
        this.$message({
          message: '范围值需填写完整',
          type: 'info',
          duration: 800,
        })
        return false
      }
      this.tableLoading = true

      await this.getDataList()

      this.tableLoading = false
    },
    async getHeadList() {
      const payload = {
        ...this.idData,
      }
      let action
      if (payload.etlId) {
        action = getEtlHeadList
      } else if (payload.tagDsId) {
        action = getTagHeadList
      }

      if (typeof action !== 'function') {
        return false
      }
      const [err, res] = await action(payload)
      if (res) {
        this.headList = res.data
      }
    },
    async getQueryList() {
      const payload = {
        ...this.idData,
      }
      let action
      if (payload.etlId) {
        action = getEtlQueryList
      } else if (payload.tagDsId) {
        action = getTagQueryList
      }

      if (typeof action !== 'function') {
        return false
      }

      const [err, res] = await action(payload)
      if (res) {
        this.queryList = res.data || []
        this.queryList.sort((a, b) => (b.range % 2) - (a.range % 2))
        this.query = this.queryList.map((item) => {
          return {
            range: item.range,
            column: item.column,
            value1: '',
            value2: '',
          }
        })
      }
    },
    async getDataList() {
      const payload = {
        ...this.idData,
        ...this.formData,
        query: JSON.stringify(this.query),
      }

      let action
      if (payload.etlId) {
        action = getEtlDataList
      } else if (payload.tagDsId) {
        action = getTagDataList
      }

      if (typeof action !== 'function') {
        return false
      }

      const [err, res] = await action(payload)
      if (res) {
        this.tableData = res.data
        this.total = res.totalRow
      }
    },
    setCurrent(item) {
      this.activeMenu = item
    },
    setType(type) {
      this.type = type
    },
    openDetail(data) {
      this.detailData = data
      this.detailVisible = true
    },
    closeDetail() {
      this.detailVisible = false
    },
    async exportList() {
      const payload = {
        ...this.idData,
        ...this.formData,
      }
      delete payload.pageIndex
      delete payload.pageSize
      delete payload.pageType

      let action
      if (this.type === '0') {
        action = exportByTarget
      } else {
        action = exportByEtl
      }
      payload.query = JSON.stringify(this.query)

      const [err, res] = await action(payload)
      if (!err) {
        this.$message({
          message: '正在导出中，请移步系统管理-导出记录进行查看',
          type: 'success',
          duration: 800,
          onClose: () => {},
        })
      }
    },
    dateChange() {
      if (!this.dateRange || this.dateRange?.length === 0) {
        this.formData.startTime = ''
        this.formData.endTime = ''
        return false
      }
      const [begin, end] = this.dateRange
      this.formData.startTime = begin
      this.formData.endTime = end
    },
    initDateRange() {
      const now = new Date(+new Date() + 8 * 3600 * 1000)
      const dateStr = now.toISOString().slice(0, 10).replace('T', ' ')
      this.dateRange = [dateStr + ' 00:00:00', dateStr + ' 23:59:59']
      this.dateChange()
    },
    resetSearch() {
      this.formData = {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
      }
      this.query = this.queryList.map((item) => {
        return {
          range: item.range,
          column: item.column,
          value1: '',
          value2: '',
        }
      })
      this.$refs.querySet.dateRange = {}
      this.initDateRange()
      this.fetchData()
    },
    handleQueryRange() {
      if (this.showRange === 3) {
        this.showRange = 'all'
      } else {
        this.showRange = 3
      }
    },
  },
}
</script>

<style lang="scss">
#data-query {
  @include flex-row;

  > .aside-bar {
    border-right: 1px solid $borderColor;
  }

  > .y-container {
    width: calc(100% - 288px);

    > .header {
      justify-content: flex-start;
      border-bottom: 1px solid $borderColor;
    }

    > .search-bar {
      justify-content: flex-start;
      padding: 16px 24px;
      padding-bottom: 8px;
    }

    > .y-container--tight {
      padding-bottom: 0;
      border-bottom: 1px solid $borderColor;

      .el-table::before {
        content: none;
      }
    }

    .base-pagination {
      align-self: flex-end;
      padding: 24px 8px;
    }
  }

  .el-dialog__wrapper {
    &.no-padding {
      .el-dialog__body {
        padding: 0;
      }
    }
  }
}
</style>
