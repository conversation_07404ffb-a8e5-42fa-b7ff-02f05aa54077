<template>
  <div class="aside-panel y-container no-padding">
    <div class="header y-bar">
      <h2 class="y-title">{{ '字典项' }}</h2>
      <el-button
        class="mini"
        type="primary"
        icon="el-icon-plus"
        @click.native="$emit('open-edit', null)"
        >{{ $t('新增') }}</el-button
      >
    </div>
    <div class="y-container--tight">
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <el-table-column
          type="index"
          label="序号"
          width="50">
        </el-table-column>
        <el-table-column
          :label="$t('类型名称')"
          prop="NAME"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          :label="$t('编号')"
          prop="CODE"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          :label="$t('是否启用')"
          prop="ACCOUNT">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.ENABLE_STATUS"
              active-color="#0555CE"
              active-value="01"
              inactive-value="00"
              @change="handleSwitch(scope.row)">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="100"
          fixed="right">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :underline="false"
              @click.native="$emit('open-edit', scope.row)"
              >编辑</el-link
            >
            <el-link
              type="danger"
              :underline="false"
              @click.native="deleteDictItem(scope.row)"
              >删除</el-link
            >
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
  </div>
</template>

<script>
import { getDictItemList, getDictItemDetail, updateDictItem, deleteDictItem } from '@/api/dict-config'

export default {
  components: {},
  props: {
    activeMenu: {
      type: String,
    },
  },
  data() {
    return {
      tableLoading: false,
      keyword: '',
      total: 0,
      formData: {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
        dictGroupId: '',
      },
      tableData: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {},
  watch: {
    activeMenu: {
      handler(activeMenu) {
        this.formData.dictGroupId = activeMenu
        this.fetchData()
      },
      immediate: true,
    },
  },
  methods: {
    async fetchData() {
      this.tableLoading = true
      const [err, res] = await getDictItemList(this.formData)
      if (res) {
        this.tableData = res.data
        this.total = res.totalRow
      }
      this.tableLoading = false
    },
    async deleteDictItem(data) {
      try {
        await this.$confirm('此操作将永久删除选中字典项, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const { ID, DICT_GROUP_ID } = data
      const payload = { ID, dictGroupId: DICT_GROUP_ID, ...data }

      const [err, res] = await deleteDictItem(payload)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    async handleSwitch(data) {
      const { ID, NAME, CODE, SORT_NUM, ENABLE_STATUS, BAKUP, DICT_GROUP_ID } = data
      const payload = { ID, NAME, CODE, SORT_NUM, ENABLE_STATUS, BAKUP, dictGroupId: DICT_GROUP_ID }

      const [err, res] = await updateDictItem(payload)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {},
        })
      } else {
        this.fetchData()
      }
    },
  },
}
</script>

<style lang="scss"></style>
