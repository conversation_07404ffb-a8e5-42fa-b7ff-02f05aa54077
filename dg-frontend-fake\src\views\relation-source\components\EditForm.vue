<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="原数据源名称"
            prop="SRC_DS_NAME">
            <el-input
              v-model="formData.SRC_DS_NAME"
              v-trim
              placeholder="请输入原数据源名称"
              maxlength="30"
              clearable
              show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="系统数据源名称"
            prop="SYS_DS_NAME">
            <el-select
              v-model="formData.SYS_DS_NAME"
              placeholder="请选择系统数据源">
              <el-option
                v-for="item in systemList"
                :value="item"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="24">
          <el-form-item
            label="数据类型"
            prop="SRC_DS_TYPE">
            <el-select
              v-model="formData.SRC_DS_TYPE"
              placeholder="请选择数据类型">
              <el-option
                label="数据库"
                value="ds"></el-option>
              <el-option
                label="EXCEL"
                value="excel"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="启用状态"
            prop="SRC_DS_STATE">
            <el-switch
              v-model="formData.SRC_DS_STATE"
              active-color="#52C41A"
              active-value="1"
              inactive-value="0">
            </el-switch>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { addSource, getSystemList } from '@/api/source'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      formData: {
        // SRC_DS_TYPE: null,
        SYS_DS_NAME: null,
        SRC_DS_NAME: null,
        SRC_DS_STATE: '0',
      },
      rules: {
        SRC_DS_NAME: [{ required: true, message: '请输入原数据源名称', trigger: 'blur' }],
        SYS_DS_NAME: [
          { required: true, message: '请选择系统数据源名称', trigger: 'change' },
          // { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '数据名称不合法，只能含有字母、数字或下划线且不以数字开头！', trigger: 'blur' },
        ],
        // SRC_DS_TYPE: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
        SRC_DS_STATE: [{ required: true, message: '请选择是否启用', trigger: 'change' }],
      },
      systemList: [],
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data && JSON.stringify(this.data) != '{}') {
        Object.assign(this.formData, this.data)
      } else {
        this.formData = {
          // SRC_DS_TYPE: null,
          SRC_DS_NAME: null,
          SYS_DS_NAME: null,
          SRC_DS_STATE: '0',
        }
      }
      this.getSystemList()
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const [err, res] = await addSource(this.formData, 'src')
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
    async getSystemList() {
      const [err, res] = await getSystemList()
      if (!err) {
        this.systemList = res.data
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form::v-deep {
  position: relative;

  // .el-form-item.modify {
  //   &.is-success {
  //     .el-input::after {
  //       content: '\e6da';
  //       position: absolute;
  //       right: 16px;
  //       font-family: element-icons;
  //       font-size: 16px;
  //       color: $color-success;
  //     }

  //     .el-input__inner {
  //       border-color: #eef9e9;
  //       background: #eef9e9;

  //       &:focus {
  //         border-color: $color-success;
  //         box-shadow: 0px 0px 8px 0px transparentize($color-success, 0.4);
  //       }

  //       &:hover {
  //         border-color: $color-success;
  //       }
  //     }
  //   }
  // }

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
