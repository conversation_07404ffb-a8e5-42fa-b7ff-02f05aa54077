<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="数据源名称"
            prop="SRC_DS_NAME">
            <el-input
              v-model="formData.SRC_DS_NAME"
              v-trim
              placeholder="请输入数据源名称"
              maxlength="50"
              clearable
              show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="数据库类型"
            prop="dbType">
            <el-select
              v-model="formData.dbType"
              placeholder="请选择数据库类型">
              <el-option label="MySQL" value="1"></el-option>
              <el-option label="Oracle" value="2"></el-option>
              <el-option label="SQL Server" value="3"></el-option>
              <el-option label="PostgreSQL" value="4"></el-option>
              <el-option label="DB2" value="5"></el-option>
              <el-option label="Sybase" value="6"></el-option>
              <el-option label="SQLite" value="7"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item
            label="数据库IP"
            prop="dbIp">
            <el-input
              v-model="formData.dbIp"
              v-trim
              placeholder="请输入数据库IP地址"
              clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="端口"
            prop="dbPort">
            <el-input
              v-model="formData.dbPort"
              v-trim
              placeholder="端口"
              clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="数据库名称"
            prop="SYS_DS_NAME">
            <el-input
              v-model="formData.SYS_DS_NAME"
              v-trim
              placeholder="请输入数据库名称"
              maxlength="30"
              clearable
              show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item
            label="用户名"
            prop="dbUsername">
            <el-input
              v-model="formData.dbUsername"
              v-trim
              placeholder="请输入数据库用户名"
              clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="密码"
            prop="dbPassword">
            <el-input
              v-model="formData.dbPassword"
              type="password"
              placeholder="请输入数据库密码"
              clearable
              show-password></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item
            label="最小连接数"
            prop="minConnections">
            <el-input
              v-model="formData.minConnections"
              v-trim
              placeholder="默认5，可选"
              clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="最大连接数"
            prop="maxConnections">
            <el-input
              v-model="formData.maxConnections"
              v-trim
              placeholder="默认20，可选"
              clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item
            label="描述"
            prop="description">
            <el-input
              v-model="formData.description"
              v-trim
              type="textarea"
              :rows="3"
              placeholder="请输入数据源描述"
              maxlength="200"
              show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="启用状态"
            prop="SRC_DS_STATE">
            <el-switch
              v-model="formData.SRC_DS_STATE"
              active-color="#52C41A"
              active-value="1"
              inactive-value="0">
            </el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 连接池高级配置 -->
      <div @click="showAdvancedConfig = !showAdvancedConfig">
        <span class="config-title">
          连接池高级配置
        </span>
        <el-button
          type="text"
          size="mini"
          :icon="showAdvancedConfig ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
          style="color: #409EFF;">
          {{ showAdvancedConfig ? '收起' : '展开' }}
        </el-button>
      </div>
      <transition name="slide-fade">
        <div v-show="showAdvancedConfig" class="pool-config-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="初始连接数"
                prop="initialSize">
                <el-input
                  v-model="formData.initialSize"
                  v-trim
                  placeholder="默认5，可选"
                  clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="最大空闲连接数"
                prop="maxIdle">
                <el-input
                  v-model="formData.maxIdle"
                  v-trim
                  placeholder="默认10，可选"
                  clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="最大等待时间(ms)"
                prop="maxWait">
                <el-input
                  v-model="formData.maxWait"
                  v-trim
                  placeholder="默认10000，可选"
                  clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="回收空闲连接时间(ms)"
                prop="minEvictableIdleTimeMillis">
                <el-input
                  v-model="formData.minEvictableIdleTimeMillis"
                  v-trim
                  placeholder="默认30000，可选"
                  clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="检查空闲连接频率(ms)"
                prop="timeBetweenEvictionRunsMillis">
                <el-input
                  v-model="formData.timeBetweenEvictionRunsMillis"
                  v-trim
                  placeholder="默认60000，可选"
                  clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="强制回收连接时限(秒)"
                prop="removeAbandonedTimeout">
                <el-input
                  v-model="formData.removeAbandonedTimeout"
                  v-trim
                  placeholder="默认180，可选"
                  clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </transition>
      
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { addSource } from '@/api/source'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      formData: {
        SRC_DS_NAME: null,           // 数据源名称 → dataSourceName
        dbType: '1',                 // 数据库类型，默认为1
        dbIp: null,                  // 数据库IP
        dbPort: '3306',              // 数据库端口，默认3306
        minConnections: '5',         // 最小连接数
        maxConnections: '20',        // 最大连接数
        SYS_DS_NAME: null,           // 数据库名称 → dbName
        dbUsername: null,            // 数据库用户名
        dbPassword: null,            // 数据库密码
        SRC_DS_STATE: '0',           // 状态 → status
        description: null,           // 描述
        // 连接池详细参数
        initialSize: '5',            // 初始连接数
        maxIdle: '10',               // 最大空闲连接数
        maxWait: '10000',            // 获取连接最大等待时间(ms)
        minEvictableIdleTimeMillis: '30000',  // 回收空闲连接的最小空闲时间(ms)
        timeBetweenEvictionRunsMillis: '60000', // 检查空闲连接的频率(ms)
        removeAbandonedTimeout: '180',        // 强制回收连接的时限(秒)
      },
      rules: {
        SRC_DS_NAME: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }],
        dbType: [{ required: true, message: '请选择数据库类型', trigger: 'change' }],
        dbIp: [
          { required: true, message: '请输入数据库IP地址', trigger: 'blur' },
          { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: 'IP地址格式不正确', trigger: 'blur' }
        ],
        dbPort: [
          { required: true, message: '请输入数据库端口', trigger: 'blur' },
          { pattern: /^\d+$/, message: '端口必须为数字', trigger: 'blur' }
        ],
        minConnections: [
          { pattern: /^\d+$/, message: '连接数必须为数字', trigger: 'blur' }
        ],
        maxConnections: [
          { pattern: /^\d+$/, message: '连接数必须为数字', trigger: 'blur' }
        ],
        initialSize: [
          { pattern: /^\d+$/, message: '初始连接数必须为数字', trigger: 'blur' }
        ],
        maxIdle: [
          { pattern: /^\d+$/, message: '最大空闲连接数必须为数字', trigger: 'blur' }
        ],
        maxWait: [
          { pattern: /^\d+$/, message: '最大等待时间必须为数字', trigger: 'blur' }
        ],
        minEvictableIdleTimeMillis: [
          { pattern: /^\d+$/, message: '回收空闲连接时间必须为数字', trigger: 'blur' }
        ],
        timeBetweenEvictionRunsMillis: [
          { pattern: /^\d+$/, message: '检查空闲连接频率必须为数字', trigger: 'blur' }
        ],
        removeAbandonedTimeout: [
          { pattern: /^\d+$/, message: '强制回收连接时限必须为数字', trigger: 'blur' }
        ],
        SYS_DS_NAME: [
          { required: true, message: '请输入数据库名称', trigger: 'blur' },
          { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '数据库名称不合法，只能含有字母、数字或下划线且不以数字开头！', trigger: 'blur' },
        ],
        dbUsername: [{ required: true, message: '请输入数据库用户名', trigger: 'blur' }],
        dbPassword: [{ required: true, message: '请输入数据库密码', trigger: 'blur' }],
        SRC_DS_STATE: [{ required: true, message: '请选择是否启用', trigger: 'change' }],
      },
      showAdvancedConfig: false, // 高级配置展开控制

    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data && JSON.stringify(this.data) != '{}') {
        // 如果是编辑模式，需要从原始数据映射到表单字段
        const originalData = this.data._originalData || this.data

        // 解析连接池参数
        let poolParams = {}
        try {
          poolParams = originalData.CONNECTION_POOL_PARAMS ? JSON.parse(originalData.CONNECTION_POOL_PARAMS) : {}
        } catch (e) {
          console.warn('连接池参数解析失败:', e)
        }

        this.formData = {
          SRC_DS_NAME: this.data.SRC_DS_NAME || originalData.DATA_SOURCE_NAME,
          dbType: originalData.DB_TYPE || '1',
          dbIp: originalData.DB_IP || null,
          dbPort: originalData.DB_PORT || '3306',
          minConnections: originalData.MIN_CONNECTIONS || '5',
          maxConnections: originalData.MAX_CONNECTIONS || '20',
          SYS_DS_NAME: this.data.SYS_DS_NAME || originalData.DB_NAME,
          dbUsername: originalData.DB_USERNAME || null,
          dbPassword: originalData.DB_PASSWORD || null,
          SRC_DS_STATE: this.data.SRC_DS_STATE || originalData.STATUS || '0',
          description: originalData.DESCRIPTION || null,
          // 连接池详细参数
          initialSize: poolParams.initialSize?.toString() || '5',
          maxIdle: poolParams.maxIdle?.toString() || '10',
          maxWait: poolParams.maxWait?.toString() || '10000',
          minEvictableIdleTimeMillis: poolParams.minEvictableIdleTimeMillis?.toString() || '30000',
          timeBetweenEvictionRunsMillis: poolParams.timeBetweenEvictionRunsMillis?.toString() || '60000',
          removeAbandonedTimeout: poolParams.removeAbandonedTimeout?.toString() || '180',
        }
      } else {
        // 新建模式，使用默认值
        this.formData = {
          SRC_DS_NAME: null,
          dbType: '1',
          dbIp: null,
          dbPort: '3306',
          minConnections: '5',
          maxConnections: '20',
          SYS_DS_NAME: null,
          dbUsername: null,
          dbPassword: null,
          SRC_DS_STATE: '0',
          description: null,
          // 连接池详细参数
          initialSize: '5',
          maxIdle: '10',
          maxWait: '10000',
          minEvictableIdleTimeMillis: '30000',
          timeBetweenEvictionRunsMillis: '60000',
          removeAbandonedTimeout: '180',
        }
      }
      // 移除系统数据源列表获取，因为现在是手动输入
      // this.getSystemList()
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        // 处理连接数默认值
        const minConnections = this.formData.minConnections || '5'
        const maxConnections = this.formData.maxConnections || '20'
        const initialSize = this.formData.initialSize || '5'
        const maxIdle = this.formData.maxIdle || '10'
        const maxWait = this.formData.maxWait || '10000'
        const minEvictableIdleTimeMillis = this.formData.minEvictableIdleTimeMillis || '30000'
        const timeBetweenEvictionRunsMillis = this.formData.timeBetweenEvictionRunsMillis || '60000'
        const removeAbandonedTimeout = this.formData.removeAbandonedTimeout || '180'

        // 构建连接池参数JSON（使用用户配置的详细参数）
        const connectionPoolParams = {
          initialSize: parseInt(initialSize),
          maxActive: parseInt(maxConnections),
          minIdle: parseInt(minConnections),
          maxIdle: parseInt(maxIdle),
          maxWait: parseInt(maxWait),
          minEvictableIdleTimeMillis: parseInt(minEvictableIdleTimeMillis),
          timeBetweenEvictionRunsMillis: parseInt(timeBetweenEvictionRunsMillis),
          removeAbandonedTimeout: parseInt(removeAbandonedTimeout)
        }

        // 构建新接口需要的数据格式
        const payload = {
          dataSourceName: this.formData.SRC_DS_NAME,
          dbType: this.formData.dbType,
          dbIp: this.formData.dbIp,
          dbPort: this.formData.dbPort,
          minConnections: minConnections,
          maxConnections: maxConnections,
          dbName: this.formData.SYS_DS_NAME,
          dbUsername: this.formData.dbUsername,
          dbPassword: this.formData.dbPassword,
          connectionPoolParams: JSON.stringify(connectionPoolParams),
          status: this.formData.SRC_DS_STATE,
          description: this.formData.description || ''
        }

        const [err, res] = await addSource(payload)
        if (!err) {
          this.$message({
            message: res?.msg || '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },

  },
}
</script>

<style lang="scss" scoped>
.edit-form::v-deep {
  position: relative;

  // .el-form-item.modify {
  //   &.is-success {
  //     .el-input::after {
  //       content: '\e6da';
  //       position: absolute;
  //       right: 16px;
  //       font-family: element-icons;
  //       font-size: 16px;
  //       color: $color-success;
  //     }

  //     .el-input__inner {
  //       border-color: #eef9e9;
  //       background: #eef9e9;

  //       &:focus {
  //         border-color: $color-success;
  //         box-shadow: 0px 0px 8px 0px transparentize($color-success, 0.4);
  //       }

  //       &:hover {
  //         border-color: $color-success;
  //       }
  //     }
  //   }
  // }

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }

  // 连接池配置样式
  .pool-config-section {
    margin: 20px 0;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;

    .pool-config-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e4e7ed;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background: #ecf5ff;
      }

      .config-title {
        font-weight: 500;
        color: #303133;
        display: flex;
        align-items: center;
      }
    }

    .pool-config-content {
      padding: 20px 16px;
      background: #fff;
    }
  }

  // 动画效果
  .slide-fade-enter-active {
    transition: all 0.3s ease;
  }
  .slide-fade-leave-active {
    transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
  }
  .slide-fade-enter, .slide-fade-leave-to {
    transform: translateY(-10px);
    opacity: 0;
  }
}
</style>
