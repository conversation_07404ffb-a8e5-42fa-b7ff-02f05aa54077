import { get, post } from '@/http/request'
import { dataStatistics } from '@/api/PATH'

// Excel数据集
export function getTotal(formData) {
  return post(dataStatistics.total, formData)
}

export function getRelation(formData) {
  return post(dataStatistics.relation, formData)
}

export function getObjectType(formData) {
  return post(dataStatistics.objectType, formData)
}

export function getInvestor(formData) {
  return post(dataStatistics.investor, formData)
}

export function getOrderType(formData) {
  return post(dataStatistics.orderType, formData)
}

export function getArea(formData) {
  return post(dataStatistics.area, formData)
}

export function getInvalid(formData) {
  return post(dataStatistics.invalid, formData)
}

export function getSensitive(formData) {
  return post(dataStatistics.sensitive, formData)
}

export function getAllStatistics(formData = {}) {
  return post(dataStatistics.statistics, formData)
}