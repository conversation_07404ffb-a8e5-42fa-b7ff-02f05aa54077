export default [
  {
    name: '数据源',
    module: [
      { nodeType: '1', dsType: 'excel', name: 'Excel数据源' },
      { nodeType: '1', dsType: 'ds', name: '关系数据源' },
      { nodeType: '1', dsType: 'ds', dsTypeFlag: 'file', name: '文件数据源' },
    ]
  },
  {
    name: '目标源',
    module: [
      { nodeType: '7', name: '关系目标源' }
    ]
  },
  {
    name: '数据预处理',
    module: [
      { nodeType: '2', name: '行过滤' },
      { nodeType: '3', name: '空值处理' },
      { nodeType: '4', name: '值替换' },
      { nodeType: '5', name: '派生列' },
      { nodeType: '8', name: '格式转换' },
      { nodeType: '9', name: '数据分割' },
      { nodeType: '10', name: '异常清理' },
      { nodeType: '11', name: '数据过滤' },
      { nodeType: '12', name: '数据脱敏' },
    ]
  },
]