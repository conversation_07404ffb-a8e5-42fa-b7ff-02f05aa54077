<template>
  <base-card
    id="annotation-team"
    class="y-page">
    <div class="y-container no-padding">
      <div class="y-bar">
        <h2 class="y-title">标注团队</h2>
        <el-button
          @click="openEdit(null)"
          class="mini"
          type="primary">
          <svg-icon icon="add"></svg-icon>
          添加团队
        </el-button>
      </div>
      <div class="y-container--tight">
        <el-form
          ref="searchForm"
          :inline="true"
          :model="searchForm"
          :rules="rules"
          size="small"
          style="margin-top: 16px">
          <el-form-item
            label="关键字"
            prop="keyword">
            <el-input
              v-model="searchForm.keyword"
              v-trim
              clearable
              style="width: 220px"
              placeholder="请输入关键字进行搜索"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              class="mini"
              type="primary"
              size="small"
              plain
              @click.native="resetSearch">
              <svg-icon icon="reset"></svg-icon>
              重置
            </el-button>
            <el-button
              v-debounce="fetchData"
              class="mini"
              type="primary">
              <i class="el-icon-search"></i>
              搜索
            </el-button>
          </el-form-item>
        </el-form>
        <el-table
          :data="list"
          v-loading="loading"
          v-reset-scroll="'div.el-table__body-wrapper'"
          stripe
          height="100%"
          fit
          ref="table"
          style="width: 100%">
          <el-table-column
            type="index"
            label="序号"
            width="50">
          </el-table-column>
          <el-table-column
            label="团队名称"
            prop="TEAM_NAME"
            min-width="150"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            label="团队说明"
            prop="TEAM_REMARKS"
            min-width="400"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            label="操作"
            width="100"
            fixed="right">
            <template slot-scope="scope">
              <el-link
                @click="openEdit(scope.row)"
                type="primary"
                :underline="false"
                >编辑</el-link
              >
              <el-link
                type="danger"
                :underline="false"
                @click="deleteTeam(scope.row)"
                >删除</el-link
              >
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/no-info.png')"
            description="暂无信息"></el-empty>
        </el-table>
      </div>
      <pagination
        :current-page.sync="formData.pageIndex"
        :page-size.sync="formData.pageSize"
        :total="total"
        @page="fetchData"></pagination>
    </div>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="1200">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getList, deleteTeam } from '@/api/annotation-team'
import EditForm from './components/EditForm.vue'

export default {
  name: 'AnnotationTeam',
  components: {
    EditForm,
  },
  data() {
    return {
      loading: false,
      searchForm: {
        keyword: '',
      },
      rules: {},
      formData: {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
      },
      total: 0,
      list: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true

      const payload = {
        ...this.searchForm,
        ...this.formData,
      }

      const [err, res] = await getList(payload)
      if (res) {
        this.list = res.data || []
        this.total = res.totalRow
      }
      this.loading = false
    },
    resetSearch() {
      this.$refs.searchForm?.resetFields()
      this.formData = {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
      }
      this.fetchData()
    },
    openEdit(data) {
      if (data) {
        this.editDrawerTitle = '编辑团队'
        this.editDrawerData = data
      } else {
        this.editDrawerTitle = '添加团队'
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    async deleteTeam(row) {
      try {
        const confirm = await this.$confirm('确定要删除该团队吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        if (confirm) {
          const [err, res] = await deleteTeam(row.TEAM_ID)
          if (res) {
            this.$message({
              type: 'success',
              message: '删除成功!',
            })
            this.fetchData()
          }
        }
      } catch (error) { }
    },
  },
}
</script>

<style lang="scss">
#annotation-team {
  @import '@/assets/style/modules/table-page.scss';
}
</style>
