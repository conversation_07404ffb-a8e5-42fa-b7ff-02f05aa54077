<template>
  <div class="input-tag">
    <el-input
      ref="inputTag"
      class="input-tag_add_input"
      v-if="inputVisible"
      v-model="inputValue"
      v-trim
      placeholder="请输入"
      size="small"
      @keyup.enter.native="confirmInput"
      @blur="confirmInput"></el-input>
    <el-button
      v-else
      class="input-tag_add"
      type="primary"
      plain
      icon="el-icon-plus"
      @click.native="showInput"
      >添加条件</el-button
    >
    <div
      class="input-tag_tag"
      v-for="(tag, idx) in value">
      <span>{{ tag }}</span>
      <i
        class="el-icon-close"
        @click="removeTag(idx)"></i>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InputTag',
  components: {},
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      inputVisible: false,
      inputValue: '',
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    confirmInput() {
      if (!this.inputValue.trim()) {
        return false
      }
      const tagList = [...this.value]
      tagList.push(this.inputValue)
      this.$emit('change', [...tagList])
      this.inputVisible = false
      this.inputValue = ''
    },
    showInput() {
      this.inputVisible = true
      this.$nextTick(() => {
        this.$refs.inputTag.$refs.input.focus()
      })
    },
    removeTag(idx) {
      const tagList = [...this.value]
      tagList.splice(idx, 1)
      this.$emit('change', [...tagList])
    },
  },
}
</script>

<style lang="scss">
.input-tag {
  @include flex-row;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  padding: 8px 8px 0 0;
  border-radius: 4px;
  background-color: $bgColor-dark;

  > div,
  > button {
    margin: 0 0 8px 8px;
  }

  .el-button.input-tag_add {
    padding: 4px 8px;
    border: 1px solid $themeColor;
  }

  .el-input.input-tag_add_input {
    width: 92px;
    .el-input__inner {
      padding: 4px 8px;
      width: 92px;
      height: 24px;
      line-height: 22px;
      background-color: $bgColor;
    }
  }

  .input-tag_tag {
    padding: 1px 8px;
    height: 24px;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    color: $txtColor;
    border-radius: 4px;
    background: $bgColor;

    i {
      margin-left: 8px;
      color: $txtColor-light;
      cursor: pointer;

      &:hover {
        color: $txtColor;
      }
    }
  }
}
</style>
