import { formatNumberWithComma } from '@/utils/formatter.js'

export default {
  data() {
    return {
      color: ['#FCC419', '#FF6B6B', '#0555CE', '#5C7CFA', '#22B8CF',],
      rich: {
        icon: {
          width: this.nowSize(20),
          height: this.nowSize(20),
          borderRadius: 2,
        },
        text: {
          fontSize: this.nowSize(14),
          lineHeight: this.nowSize(24),
          align: 'center',
        },
        number: {
          padding: [this.nowSize(8), 0],
          fontSize: this.nowSize(16),
          fontWeight: 700,
          lineHeight: this.nowSize(24),
          align: 'center',
        },
      },
      tooltipStyle: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)',
        appendToBody: true,
      },
      sideLegend: {
        orient: 'vertical',
        top: 'center',
        right: '0%',
        icon: 'roundRect',
        itemGap: this.nowSize(20),
        itemWidth: this.nowSize(20),
        itemHeight: this.nowSize(20),
        data: this.labelList,
        formatter: (name) => {
          for (let i = 0; i < this.dataList.length; i++) {
            if (name === this.dataList[i].name) {
              return `${this.dataList[i].name}        ${formatNumberWithComma(this.dataList[i].value)}`
            }
          }
        },
        textStyle: {
          fontSize: this.nowSize(14),
          fontWeight: 700,
        },
      },
      labelLineStyle: {
        labelLine: {
          show: true,
          showAbove: true,
          length: this.nowSize(10),
          length2: this.nowSize(10),
          // minTurnAngle: 90,
          // maxSurfaceAngle: 90,
        },
        labelLayout: (params) => {
          const points = params.labelLinePoints
          if (!points) {
            return null
          }
          if (params.align === 'right') {
            points[2][0] = params.labelRect.x
          } else {
            points[2][0] = params.labelRect.x + params.labelRect.width
          }

          return {
            labelLinePoints: points,
            hideOverlap: true,
            moveOverlap: true,
          }
        },
      },
      // bottomlegend: {
      //   orient: 'vertical',
      //   bottom: '0%',
      //   left: 'left',
      //   icon: 'roundRect',
      //   itemGap: this.nowSize(10),
      //   itemWidth: this.nowSize(24),
      //   itemHeight: this.nowSize(24),
      //   data: this.labelList,
      //   formatter: (name) => {
      //     for (let i = 0; i < this.dataList.length; i++) {
      //       if (name === this.dataList[i].name) {
      //         return `${this.dataList[i].name}${formatNumberWithComma(this.dataList[i].value)}`
      //       }
      //     }
      //   },
      //   textStyle: {
      //     fontSize: this.nowSize(18),
      //     fontWeight: 700,
      //   },
      // },
    }
  },
}
