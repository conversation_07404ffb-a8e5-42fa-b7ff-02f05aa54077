import { get, post } from '@/http/request'

// 团队
export function getList(formData) {
  return post('/dg-etl-mgr/webcall?action=TeamDao.list', { data: JSON.stringify(formData) })
}

export function updateTeam(formData) {
  return post('/dg-etl-mgr/servlet/team?action=team', { data: JSON.stringify(formData) })
}

export function deleteTeam(TEAM_ID) {
  return post('/dg-etl-mgr/servlet/team?action=del', { data: JSON.stringify({ TEAM_ID }) })
}

// 成员
export function getMemberList(formData) {
  return post('/dg-etl-mgr/webcall?action=TeamDao.userList', { data: JSON.stringify(formData) })
}

export function updateMember(formData) {
  return post('/dg-etl-mgr/servlet/team?action=user', { data: JSON.stringify(formData) })
}
