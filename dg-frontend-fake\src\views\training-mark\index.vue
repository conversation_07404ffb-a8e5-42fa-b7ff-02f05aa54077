<template>
  <base-card
    id="training-mark"
    class="y-page">
    <div class="y-bar header">
      <h2 class="y-title">{{ overrideTitle || '工单标注' }}</h2>
      <!-- <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button> -->
    </div>
    <!-- <div class="y-bar search-bar">
      <query-set
        :query="query"
        :config-list="queryList"></query-set>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
      <span>工单类型</span>
      <el-select
        v-model="formData.isMain"
        placeholder="请选择工单类型"
        clearable
        size="small"
        style="width: 160px">
        <el-option
          label="主工单"
          value="1"></el-option>
        <el-option
          label="子工单"
          value="0"></el-option>
        <el-option
          label="全部"
          value=""></el-option>
      </el-select>
      <span>工单状态</span>
      <el-select
        v-model="formData.status"
        placeholder="请选择工单状态"
        clearable
        size="small"
        style="width: 160px">
        <el-option
          label="预处理"
          value="0"></el-option>
        <el-option
          label="处理中"
          value="2"></el-option>
        <el-option
          label="已办结"
          value="4"></el-option>
        <el-option
          label="新建工单"
          value="5"></el-option>
        <el-option
          label="全部"
          value=""></el-option>
      </el-select>
    </div> -->
    <div class="y-container--tight">
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <el-table-column
          type="index"
          label="序号"
          width="50">
        </el-table-column>
        <template v-if="filteredHeaders">
          <el-table-column
            v-for="(value, key) in filteredHeaders"
            :key="key"
            :prop="key"
            align="center"
            min-width="132"
            show-overflow-tooltip>
            <template
              slot="header"
              slot-scope="scope">
              <overflow-text
                :max="8"
                :content="value"></overflow-text>
            </template>
          </el-table-column>
        </template>
        <el-table-column :width="filteredHeaders?.length > 0 ? '0' : undefined"></el-table-column>
        <el-table-column
          label="操作"
          width="50"
          fixed="right">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :underline="false"
              @click.native="openDetail(scope.row)"
              >详情</el-link
            >
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
    <el-drawer
      title="工单详情"
      :visible.sync="detailVisible"
      direction="rtl"
      :size="1200">
      <div class="detail-drawer y-container--tight no-padding">
        <div class="y-container--tight">
          <detail-table
            ref="detailTable"
            :data="detailData"
            @close-detail="closeDetail"
            @update-data="fetchData"></detail-table>
        </div>
        <div class="footer y-bar">
          <el-button
            type="text"
            class="btn-reset"
            @click.native="$refs.detailTable && $refs.detailTable.initPage()">
            <svg-icon icon="reset"></svg-icon>
            重置
          </el-button>
          <el-button
            type="primary"
            plain
            size="small"
            @click.native="closeDetail"
            >取消</el-button
          >
          <el-button
            v-debounce="() => $refs.detailTable && $refs.detailTable.submitForm()"
            type="primary"
            size="small"
            >确认</el-button
          >
        </div>
      </div>
    </el-drawer>
  </base-card>
</template>

<script>
import { getMarkList, getMarkListHeaders } from '@/api/data-training'
import { getEtlQueryList } from '@/api/data-query'
import DetailTable from './components/DetailTable'
// import QuerySet from '@/components/QuerySet'

export default {
  name: 'TrainingMark',
  components: {
    // QuerySet,
    DetailTable,
  },
  props: {},
  data() {
    return {
      overrideTitle: '',
      tableData: [],
      headers: null,
      tableLoading: false,
      total: 0,
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        etlId: '',
        trainDatasetId: '',
        trainBatchId: '',
        status: '',
        isMain: '',
      },
      query: [],
      detailVisible: false,
      detailData: null,
      queryList: [],
    }
  },
  computed: {
    filteredHeaders() {
      if (this.headers) {
        return Object.fromEntries(Object.entries(this.headers))
        // return Object.fromEntries(Object.entries(this.headers).filter(([k, v]) => this.tableData.some((i) => i[k] || i[k] === 0)))
      } else {
        return []
      }
    },
  },
  watch: {
    'formData.etlId': {
      handler(etlId) {
        if (etlId) {
          this.getEtlQueryList(etlId)
        }
      },
      immediate: true,
    },
  },
  created() {
    this.handleRouteData()
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.tableLoading = true
      await this.getMarkListHeaders()
      await this.getMarkList()
      this.tableLoading = false
    },
    handleRouteData() {
      let { etlId, trainDatasetId, overrideTitle } = this.$route.params
      let { trainBatchId } = this.$route.query
      Object.assign(this.formData, { etlId, trainDatasetId, trainBatchId })
      this.overrideTitle = overrideTitle
      // this.$set(this.formData, 'status', '')
      // this.$set(this.formData, 'isMain', '')
      // this.query = []
    },
    async getMarkListHeaders() {
      const [err, res] = await getMarkListHeaders(this.formData.etlId)
      if (res) {
        this.headers = res.data
      }
    },
    async getMarkList() {
      const payload = { ...this.formData, query: JSON.stringify(this.query) }
      const [err, res] = await getMarkList(payload)
      if (res) {
        this.tableData = res.data
        this.total = res.totalRow

        this.$nextTick(() => {
          this.$refs.table.doLayout()
        })
      }
    },
    async getEtlQueryList(id) {
      const payload = {
        etlId: id,
      }
      const [err, res] = await getEtlQueryList(payload)
      if (res) {
        this.queryList = res.data
        this.query = this.queryList.map((item) => {
          return {
            range: item.range,
            column: item.column,
            value1: '',
            value2: '',
          }
        })
      }
    },
    openDetail(data) {
      if (data) {
        this.detailData = { ...data, etlId: this.formData.etlId }
      } else {
        this.detailData = null
      }
      this.detailVisible = true
    },
    closeDetail() {
      this.detailVisible = false
    },
  },
}
</script>

<style lang="scss">
#training-mark {
  > .header {
    border-bottom: 1px solid $borderColor;
    justify-content: flex-start;
  }

  > .search-bar {
    padding-top: 16px;
    justify-content: flex-start;
  }

  > .y-container--tight {
    padding-top: 16px;
    padding-bottom: 0;
    border-bottom: 1px solid $borderColor;

    .el-table::before {
      content: none;
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }

  .detail-drawer {
    @import '@/assets/style/modules/drawer.scss';
  }
}
</style>
