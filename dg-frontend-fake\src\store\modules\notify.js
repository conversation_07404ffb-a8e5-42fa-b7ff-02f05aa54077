import { Notification } from 'element-ui'
// TODO: 待完善的错误栈，有重复以及顺序问题，介于现在只需要提示一条错误信息就堪堪能用
export default {
  state: {
    logStack: [],
    max: 100,
  },
  mutations: {
    ADD_LOG(state, log) {
      if (state.logStack.length >= state.max) {
        state.logStack.shift()
      }
      state.logStack.push(log)
    },
    RESET_STACK(state) {
      state.logStack = []
    }
  },
  actions: {
    resetVerify({ dispatch, commit }) {
      commit('RESET_STACK')
    },
    async notify({ dispatch, state }, option = { once: true, onlyError: true }) {
      const { once, onlyError } = option
      if (state.logStack.length > 0) {
        for (let i = once ? state.logStack.length - 1 : 0; i < state.logStack.length; i++) {
          const config = await dispatch('setMsg', state.logStack[i])
          Notification(config)
        }
      } else {
        if (!onlyError) {
          Notification({
            type: 'success',
            duration: 800,
            title: '流程检查',
            message: '流程配置正确',
          })
        }
      }
    },
    createLog({ commit, state }, log) {
      commit('ADD_LOG', log)
    },
    setMsg({ commit, dispatch }, log) {
      const { node, type, columns, msg } = log
      switch (type) {
        case 'empty-config':
          return {
            type: 'error',
            duration: 2000,
            title: '流程未配置',
            message: `${node.name}节点未配置`,
          }
        case 'column-missing':
          return {
            type: 'error',
            duration: 2000,
            title: '流程未配置',
            message: `${node.name}节点存在未配置的映射字段，请及时添加`,
          }
        case 'column-removed':
          return {
            type: 'error',
            duration: 5000,
            title: '流程配置丢失',
            message: `${node.name}节点以下映射字段已丢失，请重新配置：\n${columns.filter(col => col).join(', ')}`,
          }
        case 'integrity':
          return {
            type: 'error',
            duration: 2000,
            title: '流程完整性检查',
            message: msg,
          }
        default:
          break
      }
    }
  },
}