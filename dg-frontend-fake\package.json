{"name": "AI-Middleware", "version": "2.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build --no-module && node scripts/afterBuild.js --portal", "build:only": "vue-cli-service build --no-module", "build:login": "vue-cli-service build --target app --name login src/views/login/index.js --no-module && node scripts/afterBuild.js --login", "build:model": "vue-cli-service build --target app --name model src/views/model-presentation/index.js --no-module && node scripts/afterBuild.js --model", "build:dev": "vue-cli-service build --mode development --no-module", "lint": "vue-cli-service lint", "report": "vue-cli-service build --report", "new": "plop"}, "dependencies": {"@vue/preload-webpack-plugin": "^2.0.0", "blueimp-md5": "^2.19.0", "chalk": "^4.1.2", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "echarts": "^5.4.3", "element-ui": "^2.15.13", "js-cookie": "^3.0.5", "jsplumb": "^2.15.5", "nprogress": "^0.2.0", "panzoom": "^9.4.1", "path-browserify": "^1.0.1", "vue": "^2.7.16", "vue-click-outside": "^1.1.0", "vue-contextmenujs": "^1.3.13", "vue-i18n": "^8.28.2", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "~5.0.8", "axios": "^1.4.0", "babel-loader": "^9.2.1", "babel-plugin-component": "^1.1.1", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "happypack": "^5.0.1", "plop": "^3.1.2", "regenerator-runtime": "^0.14.1", "sass": "1.26.2", "sass-loader": "^13.3.2", "svg-sprite-loader": "^6.0.11", "vue-cli-plugin-axios": "0.0.4", "vue-cli-plugin-element": "~1.0.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "not dead", "Chrome 40.0", "ie >= 9"]}