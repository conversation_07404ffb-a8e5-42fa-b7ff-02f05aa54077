<template>
  <base-card
    id="penalty-decision"
    class="y-page">
    <div class="y-bar header">
      <h2 class="y-title">行政处罚决定书</h2>
      <span>日期</span>
      <el-date-picker
        v-model="dateRange"
        type="datetimerange"
        size="small"
        value-format="yyyy-MM-dd HH:mm:ss"
        @change="dateChange"
        :picker-options="pickerOptions"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :default-time="['00:00:00', '23:59:59']"
        style="width: 340px">
      </el-date-picker>
      <span>关键字</span>
      <el-input
        v-model="formData.punishName"
        v-trim
        placeholder="请输入关键词进行搜索"
        size="small"
        clearable
        style="width: 220px"></el-input>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
    </div>
    <div class="y-container--tight">
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <el-table-column
          label="处罚书名称"
          prop="PUNISH_INFO_NAME"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="处罚书编号"
          prop="PUNISH_INFO_NUM">
        </el-table-column>
        <el-table-column
          label="创建时间"
          prop="CREATE_TIME">
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
  </base-card>
</template>

<script>
import { getList } from '@/api/penalty-decision'

export default {
  name: 'PenaltyDecision',
  components: {},
  props: {},
  data() {
    return {
      tableData: [],
      tableLoading: false,
      total: 0,
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        startTime: null,
        endTime: null,
        punishName: null,
      },
      dateRange: [],
      pickerOptions: {
        // 禁止选中今日之后的日期
        disabledDate(time) {
          const now = new Date()
          const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
          return time.getTime() > todayEnd.getTime()
        },
      },
    }
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.tableLoading = true
      const [err, res] = await getList(this.formData)
      if (res) {
        this.tableData = res.data
        this.total = res.totalRow
      }
      this.tableLoading = false
    },
    dateChange() {
      if (!this.dateRange || this.dateRange?.length === 0) {
        this.formData.startTime = ''
        this.formData.endTime = ''
        return false
      }
      const [begin, end] = this.dateRange
      this.formData.startTime = begin
      this.formData.endTime = end
    },
  },
}
</script>

<style lang="scss">
#penalty-decision {
  .header {
    border-bottom: 1px solid $borderColor;
    justify-content: flex-start;
  }

  > .y-container--tight {
    padding-top: 16px;
    padding-bottom: 0;
    border-bottom: 1px solid $borderColor;

    .el-table::before {
      content: none;
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }
}
</style>
