<template>
  <base-card id="user-profile" class="y-page">
    <div class="y-bar">
      <h2 class="y-title">客户画像列表</h2>
    </div>
    <div class="y-bar search-bar" :class="{ expanded: showExpanded }">
      <el-form :inline="true" :model="formData" ref="searchForm">
        <!-- 第一行搜索条件 -->
        <div class="search-row">
          <el-form-item label="业务关注点：" prop="BUSINESS_FOCUS">
            <el-select
              v-model="formData.BUSINESS_FOCUS"
              size="small"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in businessFocusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客户类型：" prop="CUSTOMER_TYPE">
            <el-select
              v-model="formData.CUSTOMER_TYPE"
              size="small"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in customerTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="意向程度：" prop="INTENTION">
            <el-select
              v-model="formData.INTENTION"
              size="small"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in intentionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="互动深度：" prop="INTERACTION_DEPTH">
            <el-select
              v-model="formData.INTERACTION_DEPTH"
              size="small"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in interactionDepthOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <div v-if="!showExpanded" class="action-buttons">
            <el-button
              class="mini"
              type="primary"
              size="small"
              :icon="showExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
              plain
              @click="toggleExpanded"
            >
              {{ showExpanded ? "收起" : "展开更多" }}
            </el-button>
            <el-button v-debounce="fetchData" class="mini" type="primary">
              <i class="el-icon-search"></i>
              搜索
            </el-button>
            <el-button class="mini" @click="resetSearch">
              <i class="el-icon-refresh"></i>
              重置
            </el-button>
          </div>
        </div>

        <!-- 展开状态下的搜索条件 -->
        <transition name="expand">
          <div v-show="showExpanded" class="expanded-search">
            <!-- 第二行搜索条件 -->
            <div class="search-row">
              <el-form-item label="决策风格：" prop="DECISION_STYLE">
                <el-select
                  v-model="formData.DECISION_STYLE"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in decisionStyleOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="痛点类型：" prop="PAIN_POINT">
                <el-select
                  v-model="formData.PAIN_POINT"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in painPointOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="跟进可行性：" prop="FOLLOW_UP_FEASIBILITY">
                <el-select
                  v-model="formData.FOLLOW_UP_FEASIBILITY"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in followUpFeasibilityOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>

            <!-- 第三行搜索条件 -->
            <div class="search-row">
              <el-form-item label="机器人助理：" prop="ROBOT_ASSISTANT">
                <el-select
                  v-model="formData.ROBOT_ASSISTANT"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in robotAssistantOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="投诉风险：" prop="COMPLAINT_RISK">
                <el-select
                  v-model="formData.COMPLAINT_RISK"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in complaintRiskOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="挂断行为：" prop="HANGUP_BEHAVIOR">
                <el-select
                  v-model="formData.HANGUP_BEHAVIOR"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in hangupBehaviorOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="时间范围：" prop="MSG_TIME">
                <el-date-picker
                  v-model="formData.MSG_TIME"
                  type="datetimerange"
                  size="small"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
              </el-form-item>
            </div>

            <!-- 展开状态下的操作按钮行 -->
            <div class="search-row action-row">
              <div class="action-buttons">
                <el-button
                  class="mini"
                  type="primary"
                  size="small"
                  :icon="
                    showExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
                  "
                  plain
                  @click="toggleExpanded"
                >
                  {{ showExpanded ? "收起" : "展开更多" }}
                </el-button>
                <el-button v-debounce="fetchData" class="mini" type="primary">
                  <i class="el-icon-search"></i>
                  搜索
                </el-button>
                <el-button class="mini" @click="resetSearch">
                  <i class="el-icon-refresh"></i>
                  重置
                </el-button>
              </div>
            </div>
          </div>
        </transition>
      </el-form>
    </div>
    <empty-wrapper
      class="y-container--tight"
      :toggle="!profileList || profileList?.length === 0"
      v-loading="loading"
    >
      <el-table
        :data="profileList"
        v-loading="loading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        @selection-change="handleSelectionChange"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="70"></el-table-column>
        <!-- 客户名称列，点击弹出聊天记录 -->
        <el-table-column label="客户名称" prop="CUST_NAME" width="130">
          <template slot-scope="scope">
            <el-button type="text" @click="showChatRecords(scope.row)">
              {{ scope.row.CUST_NAME }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="消息时间"
          prop="MSG_TIME"
          width="150"
        ></el-table-column>
        <el-table-column label="客户类型" prop="CUSTOMER_TYPE" width="120"></el-table-column>
        <el-table-column label="客户需求意向标签" prop="INTENTION" width="150"></el-table-column>
        <el-table-column label="客户互动深度标签" prop="INTERACTION_DEPTH" width="150">
        </el-table-column>
        <el-table-column label="客户业务关注点标签" prop="BUSINESS_FOCUS" width="180">
        </el-table-column>
        <el-table-column label="客户业务痛点标签" prop="PAIN_POINT" width="160">
        </el-table-column>
        <el-table-column label="客户决策风格标签" prop="DECISION_STYLE" width="160">
        </el-table-column>
        <el-table-column label="客户跟进可行性标签" prop="FOLLOW_UP_FEASIBILITY" width="180">
        </el-table-column>
        <el-table-column label="是否由机器人助理服务" prop="ROBOT_ASSISTANT" width="180">
        </el-table-column>
        <el-table-column label="客户投诉风险标签" prop="COMPLAINT_RISK" width="160">
        </el-table-column>
        <el-table-column label="客户挂断行为标签" prop="HANGUP_BEHAVIOR" width="160">
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"
        ></el-empty>
      </el-table>
    </empty-wrapper>
    <div class="footer">
      <pagination
        :current-page.sync="formData.pageIndex"
        :page-size.sync="formData.pageSize"
        :total="total"
        @page="fetchData"
      ></pagination>
    </div>

    <!-- 聊天记录弹窗 -->
    <el-dialog
      title="聊天记录详情"
      :visible.sync="dialogVisible"
      width="70%"
      :before-close="handleClose"
    >
      <div class="chat-container">
        <div v-for="(message, index) in currentChatRecords" :key="index" class="chat-message" :class="message.isRobot ? 'robot-message' : 'user-message'">
          <div class="message-content">
            <div class="message-header">
              <span class="message-role">{{ message.isRobot ? '机器人' : '用户' }}</span>
              <span class="message-time">{{ index + 1 }}</span>
            </div>
            <div class="message-body">{{ message.content }}</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </base-card>
</template>

<script>
import { getList } from "@/api/user-profile";

// import ConfigCard from "@/components/ConfigCard";
// import EditForm from './components/EditForm'

export default {
  name: "UserProfile",
  components: {
    // ConfigCard,
    // EditForm,
  },
  props: {},
  data() {
    return {
      total: 0,
      loading: false,
      formData: {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
        MSG_TIME: null,
        CUSTOMER_TYPE: "",
        INTENTION: "",
        INTERACTION_DEPTH: "",
        BUSINESS_FOCUS: "",
        DECISION_STYLE: "",
        PAIN_POINT: "",
        FOLLOW_UP_FEASIBILITY: "",
        ROBOT_ASSISTANT: "",
        COMPLAINT_RISK: "",
        HANGUP_BEHAVIOR: "",
      },
      profileList: [],
      showExpanded: false,

      tableSelection: [],

      // 聊天记录弹窗相关
      dialogVisible: false,
      currentChatRecords: [],

      // 搜索选项数组
      customerTypeOptions: [
        { value: "谨慎理性型", label: "谨慎理性型" },
        { value: "友好配合型", label: "友好配合型" },
        { value: "轻度反感型", label: "轻度反感型" },
        { value: "冷静理性型", label: "冷静理性型" },
        { value: "强烈反感型", label: "强烈反感型" },
      ],
      intentionOptions: [
        { value: "强意向", label: "强意向" },
        { value: "潜在意向", label: "潜在意向" },
        { value: "无意向", label: "无意向" },
      ],
      interactionDepthOptions: [
        { value: "深度互动", label: "深度互动" },
        { value: "中度互动", label: "中度互动" },
        { value: "浅度互动", label: "浅度互动" },
      ],
      businessFocusOptions: [
        { value: "优惠", label: "优惠" },
        { value: "理财", label: "理财" },
        { value: "电子钱包", label: "电子钱包" },
        { value: "贷款", label: "贷款" },
        { value: "利率", label: "利率" },
        { value: "服务", label: "服务" },
        { value: "保险", label: "保险" },
        { value: "风险", label: "风险" },
        { value: "信用卡", label: "信用卡" },
      ],
      decisionStyleOptions: [
        { value: "拖延型", label: "拖延型" },
        { value: "谨慎型", label: "谨慎型" },
        { value: "果断型", label: "果断型" },
        { value: "禁止跟进", label: "禁止跟进" },
      ],
      painPointOptions: [
        { value: "优惠痛点", label: "优惠痛点" },
        { value: "产品痛点", label: "产品痛点" },
        { value: "服务痛点", label: "服务痛点" },
      ],
      followUpFeasibilityOptions: [
        { value: "限制跟进", label: "限制跟进" },
        { value: "允许跟进", label: "允许跟进" },
        { value: "禁止跟进", label: "禁止跟进" },
        { value: "机器人助理标签", label: "机器人助理标签" },
      ],
      robotAssistantOptions: [
        { value: "是", label: "是" },
        { value: "否", label: "否" },
      ],
      complaintRiskOptions: [
        { value: "低投诉风险", label: "低投诉风险" },
        { value: "高投诉风险", label: "高投诉风险" },
        { value: "中投诉风险", label: "中投诉风险" },
      ],
      hangupBehaviorOptions: [
        { value: "自然挂断", label: "自然挂断" },
        { value: "被动挂断", label: "被动挂断" },
        { value: "不耐烦挂断", label: "不耐烦挂断" },
      ],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData();
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true;
      // TODO: 替换为实际的API调用
      const [err, res] = await getList(this.formData);
      if (res) {
        this.profileList = res.data;
        this.total = res.totalRow;
      }

      this.loading = false;
    },
    async handleSwitch(data) {
      // TODO: 实现状态切换逻辑
    },
    toggleExpanded() {
      this.showExpanded = !this.showExpanded;
    },
    getTag(status, flag, type) {
      const map = {
        STATUS: {
          class: {
            2: "primary",
            1: "success",
            0: "danger",
          },
          label: {
            2: "待发布",
            1: "已发布",
            0: "已停用",
          },
        },
        CATEGORY: {
          class: {
            1: "warning",
            2: "danger",
            3: "info",
          },
          label: {
            1: "行为画像",
            2: "偏好画像",
            3: "价值画像",
          },
        },
      };

      return map[type][flag][status];
    },
    resetSearch() {
      this.formData = {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
        MSG_TIME: null,
        CUSTOMER_TYPE: "",
        INTENTION: "",
        INTERACTION_DEPTH: "",
        BUSINESS_FOCUS: "",
        DECISION_STYLE: "",
        PAIN_POINT: "",
        FOLLOW_UP_FEASIBILITY: "",
        ROBOT_ASSISTANT: "",
        COMPLAINT_RISK: "",
        HANGUP_BEHAVIOR: "",
      };
      this.fetchData();
    },

    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },

    // 显示聊天记录
    showChatRecords(row) {
      if (row.CHAT_RECORDS && row.CHAT_RECORDS.length > 0) {
        this.currentChatRecords = row.CHAT_RECORDS;
      } else {
        // 如果没有聊天记录，显示空数组
        this.currentChatRecords = [];
      }
      this.dialogVisible = true;
    },
    
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false;
      this.currentChatRecords = [];
    }
  },
};
</script>

<style lang="scss">
#user-profile {
  @import "@/assets/style/modules/config-card-page-with-footer.scss";
  background-color: transparent;

  .search-bar {
    padding-top: 16px;
    // justify-content: flex-start;
    width: 100%;

    .el-form {
      width: 100%;
    }

    .search-row {
      display: flex;
      align-items: center;
      gap: 24px;
      margin-bottom: 16px;

      .el-form-item {
        flex: 1;
        margin-right: 0;
        margin-bottom: 0;
        display: flex;
        align-items: center;

        .el-form-item__label {
          white-space: nowrap;
          padding-right: 8px;
          font-size: 14px;
          color: #606266;
          margin-bottom: 0;
          font-weight: bold;
        }

        .el-form-item__content {
          flex: 1;

          .el-select,
          .el-date-picker {
            width: 100%;
          }
        }
      }

      .action-buttons {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        gap: 8px;

        .el-button {
          margin-left: 0;
        }
      }
    }

    .expanded-search {
      width: 100%;
      margin: 0;

      .search-row:not(.action-row) {
        display: flex;
        align-items: center;
        gap: 24px;
        margin-bottom: 16px;

        .el-form-item {
          flex: 1;
          margin-right: 0;
          margin-bottom: 0;
          display: flex;
          align-items: center;

          .el-form-item__label {
            white-space: nowrap;
            padding-right: 8px;
            font-size: 14px;
            color: #606266;
            margin-bottom: 0;
          }

          .el-form-item__content {
            flex: 1;

            .el-select,
            .el-date-picker {
              width: 100%;
            }
          }
        }
      }

      .action-row {
        display: flex !important;
        justify-content: flex-end !important;
        flex-wrap: nowrap !important;
        margin-top: 24px;
        margin-bottom: 0;

        .action-buttons {
          margin-left: 0;
          margin-right: 0;
        }
      }
    }
  }

  // 展开/收起动画
  .expand-enter-active,
  .expand-leave-active {
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .expand-enter,
  .expand-leave-to {
    max-height: 0;
    opacity: 0;
  }

  .expand-enter-to,
  .expand-leave {
    max-height: 500px;
    opacity: 1;
  }

  // 聊天记录样式
  .chat-container {
    max-height: 500px;
    overflow-y: auto;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;

    .chat-message {
      margin-bottom: 12px;
      display: flex;
      
      &.robot-message {
        justify-content: flex-start;
        
        .message-content {
          background-color: #e8f4ff;
          border-radius: 8px 8px 8px 0;
        }
      }
      
      &.user-message {
        justify-content: flex-end;
        
        .message-content {
          background-color: #d2f9d2;
          border-radius: 8px 8px 0 8px;
        }
      }

      .message-content {
        max-width: 70%;
        padding: 10px;
        
        .message-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
          font-size: 12px;
          color: #606266;
        }
        
        .message-body {
          word-break: break-word;
          font-size: 14px;
        }
      }
    }
  }

  // 加粗表头
  .el-table th {
    font-weight: bold;
    white-space: normal;
    line-height: 1.4;
    padding: 12px 8px;
    height: auto;
    min-height: 60px;
    vertical-align: middle;
  }

  .el-table .cell {
    word-break: break-word;
    line-height: 1.4;
    white-space: pre-line;
    display: flex;
    align-items: center;
    height: 100%;
  }

  // 客户名称链接样式
  .el-button--text {
    padding: 0;
    font-size: 14px;
    color: #303133;
    font-weight: normal;
    
    &:hover {
      color: #409EFF;
      text-decoration: underline;
    }
  }
}
</style>
