<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="150px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="团队名称"
            prop="TEAM_NAME">
            <el-input
              v-model="formData.TEAM_NAME"
              v-trim
              clearable
              placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="团队说明"
            prop="TEAM_REMARKS">
            <el-input
              v-model="formData.TEAM_REMARKS"
              v-trim
              type="textarea"
              placeholder="请输入说明"
              maxlength="200"
              show-word-limit
              clearable
              :autosize="{ minRows: 2, maxRows: 10 }"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="data?.TEAM_ID">
        <el-col :span="24">
          <el-form-item
            label="团队成员"
            prop="TEAM_REMARKS">
            <el-transfer
              v-model="memberList"
              :data="userList"
              :titles="['全部用户', '团队成员']"
              :props="{
                key: 'USER_ID',
                label: 'USERNAME',
              }"
              filterable
              style="height: 500px"></el-transfer>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { getList } from '@/api/user-manage'
import { getMemberList, updateTeam, updateMember } from '@/api/annotation-team'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      formData: {
        TEAM_NAME: '',
        TEAM_REMARKS: '',
      },
      userList: [],
      memberList: [],
      rules: {
        TEAM_NAME: [{ required: true, message: '请输入团队名称', trigger: 'blur' }],
      },
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
    this.getUserList()
  },
  methods: {
    initPage() {
      this.$refs.editForm?.resetFields()
      if (this.data?.TEAM_ID) {
        Object.assign(this.formData, this.data)
        this.getMemberList()
      } else {
        this.formData = {
          TEAM_NAME: '',
          TEAM_REMARKS: '',
        }
        this.memberList = []
      }
    },
    async getMemberList() {
      const payload = {
        TEAM_ID: this.data?.TEAM_ID,
      }
      const [err, res] = await getMemberList(payload)
      if (res) {
        this.memberList = (res.data || []).map((i) => i.USER_ID)
      }
    },
    async getUserList() {
      const payload = {
        pageSize: 999,
        pageIndex: 1,
        pageType: 3,
        keyword: '',
      }
      const [err, res] = await getList(payload)
      if (res) {
        this.userList = res.data || []
      }
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        let promise = this.data?.TEAM_ID
          ? Promise.all([
              updateTeam(this.formData),
              updateMember({
                TEAM_ID: this.data.TEAM_ID,
                USER_ARR: this.memberList.join(),
              }),
            ])
          : updateTeam(this.formData)

        const result = await promise
        let err
        if (this.data?.TEAM_ID) {
          err = result[0][0] || result[1][0]
        } else {
          err = result[0]
        }
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}

.el-transfer::v-deep {
  .el-transfer-panel {
    min-height: 100%;
  }
}
</style>
