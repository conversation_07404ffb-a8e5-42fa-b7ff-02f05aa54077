<template>
  <el-drawer
    :size="1000"
    v-bind="$attrs"
    v-on="$listeners"
    title="格式转换节点配置">
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight no-padding"
        :disabled="!editable">
        <el-table
          :data="tableData"
          style="width: 100%"
          :height="'100%'">
          <el-table-column
            label="序号"
            type="index"
            width="50">
          </el-table-column>
          <el-table-column label="原字段名称">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.column"
                placeholder="请选择">
                <el-option
                  v-for="(item, key) in currentCols"
                  :key="key"
                  :value="item.column">
                  {{ item.column }} ({{ item.columnLabel }})
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="目标字段名称">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.mapColumn"
                v-trim
                clearable
                placeholder="请输入"></el-input>
            </template>
          </el-table-column>
          <el-table-column
            label="开启转换"
            width="80"
            align="center">
            <template slot-scope="scope">
              <el-checkbox
                v-model="scope.row.openFormat"
                true-label="1"
                false-label="0"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="原格式">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.srcParttern"
                :disabled="scope.row.openFormat === '0'"
                placeholder="请输入"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="目标格式">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.tagParttern"
                :disabled="scope.row.openFormat === '0'"
                placeholder="请输入"></el-input>
            </template>
          </el-table-column>
          <el-table-column
            width="80"
            align="center">
            <template slot="header">
              <el-button
                @click="addRow"
                type="primary"
                icon="el-icon-plus"
                style="padding: 8px 12px"></el-button>
            </template>
            <template slot-scope="scope">
              <el-link
                type="danger"
                :underline="false"
                :disabled="!editable"
                @click.native="handleDelete(scope.$index, scope.row)"
                >删除</el-link
              >
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="footer y-bar">
        <el-button
          type="primary"
          plain
          size="small"
          @click.native="close"
          >取消</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="handelConfirm"
          >保存</el-button
        >
      </div>
    </div>
  </el-drawer>
</template>
<script>
export default {
  inheritAttrs: false,
  components: {},
  props: ['node', 'data', 'currentCols', 'editable'],

  data() {
    return {
      tableData: [],
    }
  },
  computed: {},
  watch: {},
  created() {
    if (this.data) {
      this.tableData = JSON.parse(JSON.stringify(this.data))
    }
  },
  methods: {
    addRow() {
      this.tableData.push({
        column: '',
        mapColumn: '',
        openFormat: '1',
        srcParttern: '',
        tagParttern: '',
      })
    },
    close() {
      this.$emit('update:visible', false)
    },
    handelConfirm() {
      // 校验
      if (this.tableData.length <= 0) {
        this.$message.warning('请添加至少一个字段进行保存')
        return false
      }
      if (this.tableData.some((item) => !item.column)) {
        this.$message.warning('请选择原字段')
        return false
      }
      if (this.tableData.some((item) => !item.mapColumn)) {
        this.$message.warning('请输入目标字段')
        return false
      }
      if (this.tableData.some((item) => item.openFormat === '1' && !(item.srcParttern && item.tagParttern))) {
        this.$message.warning('请输入格式')
        return false
      }

      this.$emit('update', this.node, this.tableData)
      this.close()
    },
    handleDelete(index, row) {
      if (!this.editable) {
        return false
      }
      this.tableData.splice(index, 1)
    },
  },
}
</script>

<style lang="scss" scoped></style>
