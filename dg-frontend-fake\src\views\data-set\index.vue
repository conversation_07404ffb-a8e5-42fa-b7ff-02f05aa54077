<template>
  <base-card
    id="data-set"
    class="y-page">
    <div class="y-bar header">
      <h2 class="y-title">Excel数据集</h2>
      <el-button
        class="mini"
        type="primary"
        @click.native="openEdit(null)">
        <svg-icon icon="add"></svg-icon>
        添加文件
      </el-button>
    </div>
    <div class="y-bar search-bar">
      <span>日期</span>
      <el-date-picker
        v-model="dateRange"
        type="datetimerange"
        size="small"
        value-format="yyyy-MM-dd HH:mm:ss"
        @change="dateChange"
        :picker-options="pickerOptions"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :default-time="['00:00:00', '23:59:59']"
        style="width: 340px">
      </el-date-picker>
      <span>关键字</span>
      <el-input
        v-model="formData.fileName"
        v-trim
        placeholder="请输入关键词进行搜索"
        size="small"
        clearable
        style="width: 220px"></el-input>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
    </div>
    <div class="y-container--tight">
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <el-table-column
          type="index"
          label="序号"
          width="50">
        </el-table-column>
        <el-table-column
          label="文件名称"
          show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.FILE_NAME.slice(0, scope.row.FILE_NAME.lastIndexOf('.')) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="文件格式">
          <template slot-scope="scope">
            <span>{{ scope.row.FILE_NAME.slice(scope.row.FILE_NAME.lastIndexOf('.') + 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="数据行数"
          prop="ROW_COUNT"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="成功数"
          prop="SUCC_COUNT"
          show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.SUCC_COUNT || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          label="失败数"
          prop="FAIL_COUNT"
          show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.FAIL_COUNT || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          label="上传状态"
          prop="UPLOAD_STATE">
          <template slot-scope="scope">
            <el-tag :type="getState(scope.row.UPLOAD_STATE, 'type')">{{ getState(scope.row.UPLOAD_STATE, 'label') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="上传日期"
          prop="UPLOAD_TIME"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="操作"
          width="200">
          <template slot-scope="scope">
            <template v-if="scope.row.UPLOAD_STATE === '1'">
              <el-link
                type="primary"
                :underline="false"
                @click.native="getDetail(scope.row, 'detail')"
                >查看数据</el-link
              >
              <el-link
                v-if="Number(scope.row.FAIL_COUNT) > 0"
                type="primary"
                :underline="false"
                @click.native="getDetail(scope.row, 'log')"
                >失败日志</el-link
              >
              <el-link
                type="danger"
                :underline="false"
                @click.native="deleteExcel(scope.row)"
                >删除</el-link
              >
            </template>
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
    <el-drawer
      title="添加文件"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="1200">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
    <el-drawer
      :title="detailFlag === 'detail' ? '详细信息' : '错误日志'"
      :visible.sync="detailVisible"
      direction="rtl"
      :size="800">
      <detail-table
        :data="detailData"
        :flag="detailFlag"></detail-table>
    </el-drawer>
  </base-card>
</template>

<script>
import { getList, getDetail, deleteExcel } from '@/api/data-set'
import EditForm from './components/EditForm'
import DetailTable from './components/DetailTable'

export default {
  name: 'DataSet',
  components: {
    EditForm,
    DetailTable,
  },
  props: {},
  data() {
    return {
      tableData: [],
      tableLoading: false,
      total: 0,
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        fileName: '',
        beginDate: '',
        endDate: '',
      },
      dateRange: [],
      pickerOptions: {
        // 禁止选中今日之后的日期
        disabledDate(time) {
          const now = new Date()
          const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
          return time.getTime() > todayEnd.getTime()
        },
      },
      editDrawerShow: false,
      editDrawerData: null,
      detailVisible: false,
      detailData: null,
      detailFlag: '',
    }
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.tableLoading = true
      const [err, res] = await getList(this.formData)
      if (res) {
        this.tableData = res.data
        this.total = res.totalRow
      }
      this.tableLoading = false
    },
    async deleteExcel(data) {
      try {
        await this.$confirm('此操作将永久删除选中文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const [err, res] = await deleteExcel(data.FILE_ID)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    dateChange() {
      if (!this.dateRange || this.dateRange?.length === 0) {
        this.formData.beginDate = ''
        this.formData.endDate = ''
        return false
      }
      const [begin, end] = this.dateRange
      this.formData.beginDate = begin
      this.formData.endDate = end
    },
    getDetail(data, flag) {
      this.detailFlag = flag
      this.detailData = { FILE_ID: data.FILE_ID, FILE_NAME: data.FILE_NAME }
      this.detailVisible = true
    },
    openEdit(data) {
      if (data) {
        this.editDrawerData = data
      } else {
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    getState(state, type) {
      const map = {
        type: {
          0: 'primary',
          1: 'success',
        },
        label: {
          0: '正在上传',
          1: '上传完成',
        },
      }

      return map[type][state]
    },
  },
}
</script>

<style lang="scss">
#data-set {
  > .header {
    border-bottom: 1px solid $borderColor;
  }

  > .search-bar {
    padding-top: 16px;
    justify-content: flex-start;
  }

  > .y-container--tight {
    padding-bottom: 0;
    border-bottom: 1px solid $borderColor;

    .el-table::before {
      content: none;
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }

  .el-dialog__wrapper {
    &.no-padding {
      .el-dialog__body {
        padding: 0;
      }
    }
  }
}
</style>
