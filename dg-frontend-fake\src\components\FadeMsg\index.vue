<template>
  <transition
    name="fade-away"
    mode="out-in"
    @after-leave="handleAfterLeave">
    <div :class="['fade-msg', 'el-message', type && !iconClass ? className : '', center ? 'is-center' : '']" :style="positionStyle" v-show="visible">
      <i :class="typeClass"></i>
      <slot>
        <p
          v-html="message"
          class="el-message__content"></p>
      </slot>
      <i
        v-if="showClose"
        class="el-message__closeBtn el-icon-close"
        @click="close"></i>
    </div>
  </transition>
</template>

<script>
const typeMap = {
  success: 'success',
  info: 'info',
  warning: 'warning',
  error: 'error',
}

export default {
  name: 'FadeMsg',
  components: {},
  props: {},
  data() {
    return {
      visible: false,
      message: '',
      showClose: false,
      type: 'info',
      iconClass: '',
      onClose: null,
      closed: false,
      verticalOffset: 20,
      duration: 1000,
    }
  },
  computed: {
    typeClass() {
      return this.type && !this.iconClass ? `el-message__icon el-icon-${typeMap[this.type]}` : ''
    },
    positionStyle() {
      return {
        top: `${this.verticalOffset}px`,
      }
    },
    className() {
      return `el-message--${this.type}`
    }
  },
  watch: {
    closed(newVal) {
      if (newVal) {
        this.visible = false
      }
    },
  },
  created() {},
  mounted() {
    this.startTimer()
  },
  methods: {
    handleAfterLeave() {
      this.$destroy(true)
      this.$el.parentNode.removeChild(this.$el)
    },
    close() {
      this.closed = true
      if (typeof this.onClose === 'function') {
        this.onClose(this)
      }
    },
    clearTimer() {
      clearTimeout(this.timer)
    },

    startTimer() {
      if (this.duration > 0) {
        this.timer = setTimeout(() => {
          if (!this.closed) {
            this.close()
          }
        }, this.duration)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.fade-msg {
}

// 过渡样式
/* fade-away */
.fade-away-leave-active {
  transition: all 3s;
}
.fade-away-enter-active {
  transition: all 0.5s;
}

.fade-away-enter {
  opacity: 1;
}

.fade-away-leave-to {
  opacity: 0;
}
</style>
