<template>
  <div :class="['draw-area-detail', { showDetail }]">
    <el-button
      icon="el-icon-copy-document"
      type="primary"
      plain
      @click="showDetail = !showDetail"
      style="position: absolute; right: 10px; top: 5px; padding: 6px 12px; z-index: 2"></el-button>
    <el-tabs
      v-model="activeName"
      class="tabs-full">
      <el-tab-pane
        label="运行日志"
        name="first">
        <el-timeline v-reset-scroll>
          <el-timeline-item
            v-for="(activity, index) in logTable"
            :key="index"
            :hide-timestamp="true"
            :timestamp="activity.logTime">
            <el-popover
              placement="top"
              width="400"
              trigger="hover">
              {{ activity.logMsg }}
              <div
                slot="reference"
                class="textoverflow">
                <span style="color: #666; margin-right: 20px">{{ activity.logTime }}</span>
                <span style="margin-right: 20px; display: inline-block; width: 60px"
                  ><el-tag :type="getType(activity.type)">{{ activity.type }}</el-tag></span
                >
                {{ activity.logMsg }}
              </div>
            </el-popover>
          </el-timeline-item>
        </el-timeline>
        <div
          class="paging"
          style="text-align: center">
          <el-pagination
            background
            :total="previewLogTotal"
            :current-page="previewLogIndex"
            :page-size="15"
            @current-change="getFlowRunningChange"
            layout="prev, pager, next">
          </el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane
        label="数据预览"
        name="second">
        <el-table
          stripe
          border
          ref="previewTable"
          :data="previewTable"
          fit
          style="width: 100%"
          height="100%">
          <el-table-column
            v-for="item in previewCols"
            :key="item.column"
            align="center"
            min-width="132"
            show-overflow-tooltip>
            <template
              slot="header"
              slot-scope="scope">
              <el-tooltip
                v-if="item.columnLabel && item.columnLabel.length > 8"
                effect="dark"
                placement="top">
                <span>{{ item.columnLabel.slice(0, 6) + '...' }}</span>
                <span slot="content">{{ item.columnLabel }}</span>
              </el-tooltip>
              <span v-else>{{ item.columnLabel }}</span>
            </template>
            <template slot-scope="scope">{{ getPreviewRow(scope.row, item) }}</template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane
        label="执行结果"
        name="third">
        <el-table
          stripe
          border
          ref="multipleTable"
          :data="runningTable"
          tooltip-effect="dark"
          style="width: 100%"
          :height="'100%'">
          <el-table-column
            prop="name"
            label="节点">
            <template slot-scope="scope">
              {{ getNodeName(scope.row.nodeId) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="loadCount"
            label="读取数"></el-table-column>
          <el-table-column
            label="成功数"
            prop="succCount">
          </el-table-column>
          <el-table-column
            prop="failCount"
            label="失败数">
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getFlowRunningLog, getFlowRunningInfo, getFlowNodeData } from '@/api/etl-flow'

let clockPreviewFlag = false

export default {
  props: {
    previewCols: {
      type: Array,
      default: () => [],
    },
    currentNode: {
      type: Object,
      default: () => {},
    },
    nodeList: {
      type: Array,
      default: () => [],
    },
    flowId: {
      type: String,
      default: '',
    },
  },
  components: {},
  data() {
    return {
      timer: null,
      interval: 10000,
      showDetail: false,
      activeName: 'first',
      logTable: [], //日志
      runningTable: [], //运行状态
      previewTable: [], //预览数据
      previewTableIndex: 1,
      previewLogIndex: 1,
      previewLogTotal: 0,
    }
  },
  watch: {
    'previewCols.length'(n, o) {
      if (n !== o) {
        this.$nextTick(() => {
          if (this.$refs.previewTable) {
            this.$refs.previewTable.doLayout()
          }
        })
      }
    },
  },
  computed: {},
  methods: {
    timedTask() {
      if (!this.flowId) {
        return false
      }

      this.timer && clearTimeout(this.timer) 
      this.getFlowRunningInfo()
      this.getFlowRunningLog()
      this.timer = setTimeout(this.timedTask, this.interval)
    },
    //获取运行信息
    async getFlowRunningInfo() {
      const payload = {
        etlId: this.flowId,
        pageSize: 50,
        pageIndex: this.previewTableIndex,
      }

      const [err, res] = await getFlowRunningInfo(payload)
      if (res.state == 1 && res.data) {
        if (res.data.flowNodeCounter) {
          let list = res.data.flowNodeCounter
          this.runningTable = Object.values(list)
        }

        if (res.data?.dgEtlFlowWorkingInfo) {
          let workingStatus = res?.data?.dgEtlFlowWorkingInfo.workingStatus
          this.$emit('update-working-status', workingStatus)
          if (workingStatus === 0) {
            console.timeEnd('任务已暂停')
            console.time('任务已暂停')
            this.interval = 30000
          }
        }
      }
    },
    //获取运行日志
    async getFlowRunningLog() {
      const payload = {
        etlId: this.flowId,
        pageSize: 15,
        pageIndex: this.previewLogIndex,
      }

      const [err, res] = await getFlowRunningLog(payload)
      if (res.state == 1) {
        this.logTable = res.data.datas
        this.previewLogTotal = res.data.total
      }
    },
    getFlowRunningChange(index) {
      this.previewLogIndex = index
      this.getFlowRunningLog()
    },
    //获取节点预览数据
    async getFlowNodeData(nodeId) {
      if (!this.flowId) {
        return false
      }
      const payload = {
        etlId: this.flowId,
        nodeId,
        pageSize: 50,
        pageIndex: this.previewTableIndex,
      }
      const [err, res] = await getFlowNodeData(payload)
      if (res && res.state == 1) {
        this.previewTable = res.data
      }
    },
    // 获取预览列表显示的数据
    // 关系目标源显示column，其他显示mapColumn
    getPreviewRow(row, col) {
      // console.log('getPreviewRow', row, col)
      let prop = col[this.currentNode?.nodeType === '7' ? 'column' : 'mapColumn']
      // console.log('prop:', prop)
      let data = row[prop]
      // console.log('data:', data)
      if (!data && data !== 0 && data !== false) {
        return '-'
      } else {
        return data
      }
    },
    getRunInfo(node) {
      let find = this.runningTable.find((item) => item.nodeId === node.id)
      return find
    },
    getNodeName(id) {
      let node = this.nodeList.find((item) => item.id === id)
      if (node) {
        return node.name
      } else {
        return id
      }
    },
    getType(type) {
      const map = {
        INFO: '',
        WARN: 'warning',
        ERROR: 'danger',
      }
      return map[type]
    },
  },
}
</script>

<style lang="scss" scoped>
.draw-area-detail::v-deep {
  position: relative;
  flex: 0 0 250px;
  margin: 0 20px;
  width: calc(100% - 40px);
  box-shadow: 0px 5px 9px 0px rgba(0, 0, 0, 0.2);
  background-color: #fff;
  overflow: hidden;
  transition: flex-basis 0.5s;

  &.showDetail {
    flex-basis: 50%;
  }

  .el-tabs__nav-wrap {
    padding-left: 20px;
    padding-right: 20px;
  }

  .el-tab-pane {
    @include flex-col;
    padding: 0 20px 20px 20px;

    .el-timeline {
      flex: 1;
      align-self: flex-start;
      padding: 20px;
      width: 100%;
      overflow: auto;
    }
  }
}
</style>
