<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="答复样例名称"
            prop="answerName">
            <el-input
              v-model="formData.answerName"
              v-trim
              clearable
              placeholder="请输入答复样例名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="答复样例内容"
            prop="answerContent">
            <el-input
              v-model="formData.answerContent"
              v-trim
              placeholder="请输入答复样例内容"
              type="textarea"
              maxlength="1000"
              show-word-limit
              clearable
              :autosize="{ minRows: 10, maxRows: 20 }"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { addAnswer, updateAnswer } from '@/api/invalid-config'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
    activeMenu: {
      type: [String, Number],
      default() {
        return null
      },
    },
  },
  data() {
    return {
      formData: {
        answerName: null,
        answerContent: null,
      },
      rules: {
        answerName: [{ required: true, message: '请输入答复样例名称', trigger: 'blur' }],
        answerContent: [{ required: true, message: '请输入答复样例内容', trigger: 'blur' }],
      },
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data && JSON.stringify(this.data) != '{}') {
        this.$set(this.formData, 'answerName', this.data?.ANSWER_NAME)
        this.$set(this.formData, 'answerContent', this.data?.ANSWER_CONTENT)
        this.$set(this.formData, 'typeId', this.data?.TYPE_ID)
      } else {
        this.formData = {
          answerName: null,
          answerContent: null,
        }
      }
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const payload = {
          ...this.formData,
        }

        let submitAction = addAnswer
        if (this.data?.ANSWER_ID) {
          payload.answerId = this.data.ANSWER_ID
          submitAction = updateAnswer
        } else {
          payload.typeId = this.activeMenu
        }

        const [err, res] = await submitAction(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
