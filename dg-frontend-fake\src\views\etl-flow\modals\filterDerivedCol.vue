<template>
  <el-drawer
    :size="800"
    v-bind="$attrs"
    v-on="$listeners"
    title="派生列节点配置">
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight no-padding"
        :disabled="!editable">
        <el-table
          :data="tableData"
          style="width: 100%"
          :height="'100%'">
          <el-table-column
            label="序号"
            type="index"
            width="50">
          </el-table-column>

          <el-table-column label="字段名称">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.column"
                placeholder="请输入内容"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="字段备注">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.remarks"
                placeholder="请输入内容"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="字段类型">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.columnType"
                placeholder="请选择">
                <el-option
                  v-for="(item, key) in dictMap"
                  :key="key"
                  :label="item"
                  :value="key">
                  {{ item }}</el-option
                >
              </el-select>
            </template>
          </el-table-column>

          <el-table-column label="缺省值">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.defaultValue"
                placeholder="请输入内容"></el-input>
            </template>
          </el-table-column>

          <el-table-column
            width="80"
            align="center">
            <template slot="header">
              <el-button
                @click="addRow"
                type="primary"
                icon="el-icon-plus"
                style="padding: 8px 12px"></el-button>
            </template>
            <template slot-scope="scope">
              <el-link
                type="danger"
                :underline="false"
                :disabled="!editable"
                @click.native="handleDelete(scope.$index, scope.row)"
                >删除</el-link
              >
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="footer y-bar">
        <el-button
          type="primary"
          plain
          size="small"
          @click.native="close"
          >取消</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="handelConfirm"
          >保存</el-button
        >
      </div>
    </div>
  </el-drawer>
</template>
<script>
import { VALUE_TYPES as dictMap } from '@/utils/constant'

export default {
  inheritAttrs: false,
  components: {},
  props: ['node', 'data', 'currentCols', 'editable'],
  data() {
    return {
      tableData: [],
      dictMap,
    }
  },
  computed: {},
  watch: {},
  created() {
    if (this.data) {
      this.tableData = JSON.parse(JSON.stringify(this.data))
    }
  },
  methods: {
    addRow() {
      this.tableData.push({
        column: '',
        remarks: '',
      })
    },
    close() {
      this.$emit('update:visible', false)
    },
    handleDelete(index, row) {
      if (!this.editable) {
        return false
      }
      this.tableData.splice(index, 1)
    },
    handelConfirm() {
      // 校验
      if (this.tableData.length <= 0) {
        this.$message.warning( '请添加至少一个字段进行保存' )
        return false
      }
      if (this.tableData.some((item) => !item.column.trim())) {
        this.$message.warning( '添加字段需输入字段名称' )
        return false
      }
      if (this.tableData.some((item) => !item.columnType)) {
        this.$message.warning( '添加字段需选择字段类型' )
        return false
      }
      if (this.tableData.some((item) => !item.defaultValue)) {
        this.$message.warning( '添加字段需输入缺省值' )
        return false
      }

      this.$emit('update', this.node, this.tableData)
      this.close()
    },
  },
}
</script>

<style lang="scss" scoped>

</style>
