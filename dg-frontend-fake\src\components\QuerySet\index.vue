<template>
  <transition-group
    name="query-list"
    class="query-set">
    <div
      v-for="(item, idx) in filteredList"
      :key="item.column"
      class="query-wrap">
      <overflow-text
        class="query-label"
        :max="5"
        :content="item.columnLabel"></overflow-text>
      <template v-if="item.type === 'text'">
        <el-input
          v-if="item.range === '1' || item.range === '3'"
          v-model="query[idx].value1"
          v-trim
          size="small"
          style="width: 160px"
          clearable
          :placeholder="'请输入' + item.columnLabel"></el-input>
        <template v-else>
          <el-input
            v-model="query[idx].value1"
            v-trim
            size="small"
            style="width: 197px"
            clearable
            :placeholder="'请输入' + item.columnLabel"></el-input>
          <span class="divider">-</span>
          <el-input
            v-model="query[idx].value2"
            v-trim
            size="small"
            style="width: 197px"
            clearable
            :placeholder="'请输入' + item.columnLabel"></el-input>
        </template>
      </template>
      <template v-if="item.type === 'dataTime'">
        <el-date-picker
          v-if="item.range === '1' || item.range === '3'"
          v-model="query[idx].value1"
          type="datetime"
          size="small"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 160px"
          placeholder="请选择日期时间"
          :picker-options="pickerOptions">
        </el-date-picker>
        <el-date-picker
          v-else
          type="datetimerange"
          size="small"
          v-model="dateRange[item.column]"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="dateChange($event, idx)"
          :picker-options="pickerOptions"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
          style="width: 414px">
        </el-date-picker>
      </template>
      <template v-if="item.type === 'number'">
        <el-input
          v-if="item.range === '1' || item.range === '3'"
          type="number"
          v-model="query[idx].value1"
          v-trim
          size="small"
          style="width: 160px"
          clearable
          :placeholder="'请输入' + item.columnLabel"></el-input>
        <template v-else>
          <el-input
            type="number"
            v-model="query[idx].value1"
            v-trim
            size="small"
            style="width: 197px"
            clearable
            :placeholder="'请输入' + item.columnLabel"></el-input>
          <span class="divider">-</span>
          <el-input
            type="number"
            v-model="query[idx].value2"
            v-trim
            size="small"
            style="width: 197px"
            clearable
            :placeholder="'请输入' + item.columnLabel"></el-input>
        </template>
      </template>
      <template v-if="item.type === 'select'">
        <el-select
          v-if="item.range === '1' || item.range === '3'"
          v-model="query[idx].value1"
          :placeholder="'请选择' + item.columnLabel"
          clearable
          size="small"
          style="width: 160px">
          <el-option
            v-for="(value, key) in item.values"
            :label="value"
            :value="key"></el-option>
        </el-select>
        <template v-else>
          <el-select
            v-model="query[idx].value1"
            :placeholder="'请选择' + item.columnLabel"
            clearable
            size="small"
            style="width: 197px">
            <el-option
              v-for="(value, key) in item.values"
              :label="value"
              :value="key"></el-option>
          </el-select>
          <span class="divider">-</span>
          <el-select
            v-model="query[idx].value2"
            :placeholder="'请选择' + item.columnLabel"
            clearable
            size="small"
            style="width: 197px">
            <el-option
              v-for="(value, key) in item.values"
              :label="value"
              :value="key"></el-option>
          </el-select>
        </template>
      </template>
    </div>
  </transition-group>
</template>

<script>
export default {
  components: {},
  props: {
    query: {
      type: Array,
      default: () => [],
    },
    configList: {
      type: Array,
      default: () => [],
    },
    showRange: {
      type: [String, Number],
      default: 'all',
    },
  },
  data() {
    return {
      dateRange: {},
      pickerOptions: {
        // 禁止选中今日之后的日期
        disabledDate(time) {
          const now = new Date()
          const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
          return time.getTime() > todayEnd.getTime()
        },
      },
    }
  },
  watch: {
    'configList.length'(l) {
      if (l) {
        this.dateRange = {}
      }
    },
  },
  computed: {
    filteredList() {
      if (this.showRange === 'all') {
        return this.configList
      } else {
        return this.configList.slice(0, this.showRange)
      }
    },
  },
  methods: {
    dateChange(value, idx) {
      let [start, end] = value
      this.query[idx].value1 = start
      this.query[idx].value2 = end
    },
  },
}
</script>

<style lang="scss" scoped>
.query-set {
  @include flex-row;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin: 0;
  margin-bottom: 4px;

  .query-wrap {
    @include flex-row;
  }

  .query-label {
    white-space: nowrap;
    min-width: 70px;
  }

  .divider {
    margin-left: 8px;
  }

  div {
    margin-left: 8px;
    margin-bottom: 4px;
  }
}

.query-list-enter-active,
.query-list-leave-active {
  transition: all 0.3s;
}
.query-list-enter,
.query-list-leave-to {
  opacity: 0;
}
</style>
