import { get, post } from '@/http/request'
import { source } from '@/api/PATH'

// 关系/目标数据源
// src：关系，tag：目标
export function getList(formData) {
  return post(source.relationList, {data: JSON.stringify(formData)})
}

export function getTableList(formData) {
  return post(source.tableList, { data: JSON.stringify(formData) })
}

// export const testSourceController = new AbortController()
export function testSource(name) {
  return post(source.test, { data: JSON.stringify({ name }) }, null, null, { timeout: 3000 })
}

export function addSource(formData) {
  return post(source.add, { data: JSON.stringify(formData) })
}

export function updateSource(formData) {
  return post(source.update, { data: JSON.stringify(formData) })
}

export function deleteSource(id) {
  return post(source.delete, { data: JSON.stringify({ id }) })
}

export function getSystemList(formData) {
  return post(source.systemList, { data: JSON.stringify(formData) })
}