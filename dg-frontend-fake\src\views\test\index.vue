<template>
  <base-card
    id="test"
    class="y-page y-container--tight">
    <el-table
      :data="tableData"
      :span-method="handleSpan"
      border
      style="width: 100%">
      <el-table-column
        v-for="(item, key) in headers"
        :prop="key"
        :label="item.label"
        sortable>
      </el-table-column>
    </el-table>
  </base-card>
</template>

<script>
export default {
  name: 'Test',
  components: {},
  props: {},
  data() {
    return {
      tableData: [
        {
          id: '12987122',
          name: '王小虎',
          amount1: '234',
          amount2: '3.2',
          amount3: 10,
        },
        {
          id: '12987124',
          name: '王小虎',
          amount1: '165',
          amount2: '2.2',
          amount3: 12,
        },
        {
          id: '12987124',
          name: '王小虎2',
          amount1: '324',
          amount2: '2.2',
          amount3: 9,
        },
        {
          id: '12987124',
          name: '王小虎2',
          amount1: '621',
          amount2: '2.2',
          amount3: 17,
        },
        {
          id: '12987126',
          name: '王小虎3',
          amount1: '621',
          amount2: '2.2',
          amount3: 99,
        },
        {
          id: '12987126',
          name: '王小虎3',
          amount1: '621',
          amount2: '1',
          amount3: 99,
        },
        {
          id: '12987126',
          name: '王小虎3',
          amount1: '621',
          amount2: '1',
          amount3: 19,
        },
      ],
      headers: {
        id: {
          label: 'ID',
          span: true,
        },
        name: {
          label: '姓名',
        },
        amount1: {
          label: '数值1',
          span: true,
        },
        amount2: {
          label: '数值2',
          span: true,
        },
        amount3: {
          label: '数值3',
          span: true,
        },
      },
    }
  },
  computed: {
    spanArray() {
      return Object.entries(this.headers).reduce((acc, [key, { span }], idx) => {
        if (!acc[idx]) {
          acc[idx] = []
        }

        if (!span) {
          acc[idx] = new Array(this.signedData.length).fill(1)
          return acc
        }

        for (let i = 0, len = this.signedData.length; i < len; i++) {
          let col = acc[idx]
          if (col[i] === 0) {
            continue
          }
          const item = this.signedData[i]
          col[i] = 1
          for (let j = i + 1; j < len; j++) {
            let item2 = this.signedData[j]
            if (item2[`sid_${key}`] === item[`sid_${key}`]) {
              col[j] = 0
              col[i]++
            } else {
              i = j - 1
              break
            }
          }
        }
        return acc
      }, [])
    },
    signedData() {
      const headers = Object.entries(this.headers)
      return this.tableData.map((item) => {
        headers
          .filter(([key, head]) => head.span)
          .forEach(([key, head], idx, arr) => {
            let signProp = `sid_${key}`
            for (let j = 0; j < 3 && arr[idx - j]; j++) {
              (item[signProp] && (item[signProp] += '-' + item[arr[idx - j][0]])) || (item[signProp] = item[key])
            }
          })
        return item
      })
    },
  },
  watch: {},
  created() {
    console.log(this.spanArray)
    console.log(this.signedData)
  },
  mounted() {},
  methods: {
    handleSpan(data) {
      const { row, column, rowIndex, columnIndex } = data
      const _row = this.spanArray[columnIndex][rowIndex]
      const _col = 1

      return {
        rowspan: _row,
        colspan: _col,
      }
    },
  },
}
</script>

<style lang="scss">
#test {
}
</style>
