/*
路由配置规则：

一级路由
  path: 路径
  name: 在需要添加临时路由的情况下需要
  hidden: Boolean，不显示在菜单栏
  component: 统一为layout或router-view（前者为框架，后者仅作为嵌套时的过渡）
  redirect: 自动跳转路由
  meta.title: 菜单栏和标签页的标题
  meta.icon: 菜单栏的图标（kebab-case命名，可以是element的icon，也可以是svg-icon）
  meta.notPromote: 为true时，在只有一个显示子菜单的情况下，该子菜单不会提示至父级
  meta.affix: 固定存在于标签栏，不可删除
  meta.tagIcon: 此值存在时，该菜单标签显示为该值对应的图标

  外部链接配置：
  meta.iframe: Boolean，为true时在iframe中加载外部URL，与标签页系统集成
  meta.blanck: Boolean，为true时在新窗口中打开外部URL（注意：拼写错误保持兼容性）
  meta.url: String，外部链接的URL地址（配合iframe或blanck使用）

  注意：iframe和blanck不能同时为true，iframe优先级更高

二级路由
  path: 相对一级路由的路径
  name: 必须有，对应路由name和页面组件name（PascalCase命名）
  hidden: Boolean，不显示在菜单栏
  component: 统一为'views/'下扁平管理（kebab-case命名）
  meta.title: 菜单栏和标签页的标题
  meta.icon: 不需要
  meta.noCache: 不缓存
  meta.cacheAs: 缓存组件名（用于临时tab页面缓存）

  外部链接配置（同一级路由）：
  meta.iframe: Boolean，为true时在iframe中加载外部URL
  meta.blanck: Boolean，为true时在新窗口中打开外部URL
  meta.url: String，外部链接的URL地址
*/
let router

const newRouter = [
  {
    path: 'source-manage',
    component: 'layout',
    redirect: '/',
    meta: { title: '数据管理', icon: 'list-noteven', notPromote: true },
    children: [
      {
        path: '/data-manage',
        name: 'DataManage',
        meta: { title: '数据库管理' },
        component: 'views/relation-source',
      },
      {
        path: '/knowledge-manage',
        name: 'KnowledgeManage',
        meta: { 
          title: '知识库管理',
          iframe: false,
          blanck: true,
          url: 'http://172.16.86.101:18888/datasets'
        },
      }
    ]
  },
  {
    path: '/dift-manage',
    component: 'layout',
    redirect: '/',
    meta: { title: 'dift管理', icon: 'el-icon-s-grid', notPromote: true },
    children: [
      {
        path: 'model-market',
        name: 'ModelMarket',
        meta: { 
          title: '模型市场',
          notPromote: false,
          blanck: true,
          iframe: false,
          url: 'http://172.16.86.101:18888/plugins?category=discover'
        }
      },
      {
        path: 'config-page',
        name: 'ConfigPage',
        meta: { 
          title: '控制台页面',
          notPromote: false,
          blanck: true,
          iframe: false,
          url: 'http://172.16.86.101:18888/apps'
        }
      },
      {
        path: 'workflow-manage',
        name: 'WorkflowManage',
        meta: { 
          title: '工作流管理',
          notPromote: false,
          blanck: true,
          iframe: false,
          url: 'http://172.16.86.101:18888/apps?category=workflow'
        }
      },
      {
        path: 'explore-apps',
        name: 'ExploreApps',
        meta: { 
          title: '探索应用',
          notPromote: false,
          blanck: true,
          iframe: false,
          url: 'http://172.16.86.101:18888/explore/apps'
        }
      },
      {
        path: 'tools-manage',
        name: 'ToolsManage',
        meta: { 
          title: '工具管理',
          notPromote: false,
          blanck: true,
          iframe: false,
          url: 'http://172.16.86.101:18888/tools?category=builtin'
        }
      },
    ]
  },
  {
    path: '/data-preview',
    component: 'layout',
    redirect: '/',
    meta: { title: '数据可视化', icon: 'connect-noteven', notPromote: true },
    children: [
      {
        path: 'data-table',
        name: 'DataTable',
        meta: { title: '数据报表' },
        component: 'views/user-behavior',
      },
      {
        path: 'data-statistics',
        name: 'DataStatistics',
        meta: { title: '统计可视化' },
        component: 'views/data-statistics',
      },
    ]
  },
  {
    path: '/system-manage',
    component: 'layout',
    redirect: '/',
    meta: { title: '系统管理', icon: 'setting-noteven', notPromote: true },
    children: [
      {
        path: 'user-manage',
        name: 'UserManage',
        meta: { title: '人员管理' },
        component: 'views/user-manage',
      },
      {
        path: 'role-permission',
        name: 'RolePermission',
        meta: { title: '角色权限' },
        component: 'views/role-permission',
      },
    ]
  },
  // 本地开发登录用
  {
    path: '/login',
    hidden: true,
    component: 'views/login/index.vue',
  },
]

const oldRouter = [
  {
    path: '/source-manage',
    component: 'layout',
    redirect: '/',
    meta: { title: '数据源管理', icon: 'list-noteven', notPromote: true },
    children: [
      {
        path: 'file-data-set',
        name: 'FileDataSet',
        meta: { title: '文件数据集' },
        component: 'views/file-data-set',
      },
      {
        path: 'data-set',
        name: 'DataSet',
        meta: { title: 'Excel数据集' },
        component: 'views/data-set',
      },
      {
        path: 'relation-source',
        name: 'RelationSource',
        meta: { title: '关系数据源管理' },
        component: 'views/relation-source',
      },
      {
        path: 'target-source',
        name: 'TargetSource',
        meta: { title: '目标数据源管理' },
        component: 'views/target-source',
      },

      {
        path: 'user-profile',
        name: 'UserProfile',
        meta: { title: '客户画像列表' },
        component: 'views/user-profile',
      },
      {
        path: 'user-behavior',
        name: 'UserBehavior',
        meta: { title: '客户行为列表' },
        component: 'views/user-behavior',
      },
    ]
  },
  {
    path: '/data-dispatch',
    component: 'layout',
    redirect: '/',
    meta: { title: '数据调度', icon: 'connect-noteven', notPromote: true },
    children: [
      
      {
        path: 'self-service-etl',
        name: 'SelfServiceEtl',
        meta: { title: '自助ETL' },
        component: 'views/self-service-etl',
      },
      {
        path: 'etl-log',
        name: 'EtlLog',
        meta: { title: '调度日志' },
        component: 'views/etl-log',
      },
      {
        path: 'execute-log/:logId/:overrideTitle',
        name: 'ExecuteLog',
        hidden: true,
        meta: { title: '${overrideTitle}' },
        component: 'views/execute-log',
      },
    ]
  },
  {
    path: '/model-manage',
    component: 'layout',
    redirect: '/',
    meta: { title: '模型管理', icon: 'connect-noteven', notPromote: true },
    children: [
      {
        path: 'model-list',
        name: 'ModelList',
        meta: { title: '模型列表' },
        component: 'views/model-list',
      },
      {
        path: 'label-model',
        name: 'LabelModel',
        meta: { title: '模型标签管理' },
        component: 'views/label-model',
      },
    ]
  },
  {
    path: '/annotation-manage',
    component: 'layout',
    redirect: '/',
    meta: { title: '标注管理', icon: 'connect-noteven', notPromote: true },
    children: [
      {
        path: 'annotation-team',
        name: 'AnnotationTeam',
        meta: { title: '标注团队' },
        component: 'views/annotation-team',
      },
      {
        path: 'annotation-workbench',
        name: 'AnnotationWorkbench',
        meta: { title: '标注工作台' },
        component: 'views/annotation-workbench',
      },
    ]
  },
  {
    path: '/etl-flow',
    name: 'EtlFlow',
    component: 'views/etl-flow',
    hidden: true,
    meta: { title: '新建ETL' },
  },
  {
    path: '/data-control',
    component: 'layout',
    redirect: '/',
    meta: { title: '数据治理', icon: 'cycle-noteven', notPromote: true },
    children: [
      {
        path: '/process-module',
        name: 'ProcessModule',
        meta: { title: '数据治理组件' },
        component: 'views/process-module',
      },
      {
        path: '/CSRC',
        component: 'router-view',
        name: 'Viewer',
        redirect: '/object-manage',
        meta: { title: '证监会', notPromote: true },
        children: [
          // {
          //   path: 'tag-config',
          //   name: 'TagConfig',
          //   meta: { title: '数据标签配置' },
          //   component: 'views/tag-config',
          // },
          {
            path: '/object-manage',
            name: 'ObjectManage',
            meta: { title: '诉求对象管理' },
            component: 'views/object-manage',
          },
          {
            path: '/pattern-template',
            name: 'PatternTemplate',
            meta: { title: '投资者句式模板' },
            component: 'views/pattern-template',
          },
          {
            path: '/penalty-decision',
            name: 'PenaltyDecision',
            meta: { title: '行政处罚决定书' },
            component: 'views/penalty-decision',
          },
          {
            path: '/invalid-config',
            name: 'InvalidConfig',
            meta: { title: '无效工单配置' },
            component: 'views/invalid-config',
          },
          {
            path: '/staff-config',
            name: 'StaffConfig',
            meta: { title: '证监人员信息配置' },
            component: 'views/staff-config',
          },
        ]
      },
      {
        path: '12345',
        component: 'router-view',
        name: 'Viewer',
        redirect: 'hotword-manage',
        meta: { title: '12345', notPromote: true },
        children: [
          {
            path: 'hotword-manage',
            name: 'HotwordManage',
            meta: { title: '热点事件关键词' },
            component: 'views/hotword-manage',
          },
          {
            path: 'multevent-manage',
            name: 'MulteventManage',
            meta: { title: '群发事件关键词' },
            component: 'views/multevent-manage',
          },
          {
            path: 'outburst-manage',
            name: 'OutburstManage',
            meta: { title: '突发事件关键词' },
            component: 'views/outburst-manage',
          },
          {
            path: 'hotword-explore',
            name: 'HotwordExplore',
            meta: { title: '新发热词' },
            component: 'views/hotword-explore',
          },
          // {
          //   path: 'high-incidence-manage',
          //   name: 'HighIncidenceManage',
          //   meta: { title: '高发事件预警管理' },
          //   component: 'views/high-incidence-manage',
          // },
          // {
          //   path: 'sensitive-manage',
          //   name: 'SensitiveManage',
          //   meta: { title: '敏感事件管理' },
          //   component: 'views/sensitive-manage',
          // },
          // {
          //   path: 'sign-manage',
          //   name: 'SignManage',
          //   meta: { title: '苗头事件管理' },
          //   component: 'views/sign-manage',
          // },
        ]
      },
    ]
  },
  {
    path: '/data-quality',
    component: 'layout',
    redirect: '/',
    meta: { title: '数据质量', icon: 'secure-noteven', notPromote: true },
    children: [
      {
        path: 'data-training',
        name: 'DataTraining',
        meta: { title: '数据训练' },
        component: 'views/data-training',
      },
      {
        path: 'training-mark/:trainDatasetId/:etlId/:overrideTitle',
        name: 'TrainingMark',
        hidden: true,
        meta: { title: '${overrideTitle}' },
        component: 'views/training-mark',
      },
      {
        path: 'training-history/:trainDatasetId/:overrideTitle',
        name: 'TrainingHistory',
        hidden: true,
        meta: { title: '${overrideTitle}' },
        component: 'views/training-history',
      },
    ]
  },
  {
    path: '/cron-job',
    component: 'layout',
    redirect: '/',
    meta: { title: '定时任务', icon: 'crontab-noteven' },
    children: [
      {
        path: 'crontab',
        name: 'Crontab',
        meta: { title: '定时任务' },
        component: 'views/crontab',
      },
    ]
  },
  {
    path: '/statistic',
    component: 'layout',
    redirect: '/',
    meta: { title: '查询统计', icon: 'search-noteven', notPromote: true },
    children: [
      {
        path: 'data-query',
        name: 'DataQuery',
        meta: { title: '数据查询' },
        component: 'views/data-query',
      },
      {
        path: 'data-statistics',
        name: 'DataStatistics',
        meta: { title: '数据标签统计' },
        component: 'views/data-statistics',
      },
    ]
  },
  {
    path: '/system-manage',
    component: 'layout',
    redirect: '/',
    meta: { title: '系统管理', icon: 'setting-noteven', notPromote: true },
    children: [
      {
        path: 'user-manage',
        name: 'UserManage',
        meta: { title: '人员管理' },
        component: 'views/user-manage',
      },
      {
        path: 'role-permission',
        name: 'RolePermission',
        meta: { title: '角色权限' },
        component: 'views/role-permission',
      },
      {
        path: 'role-users',
        name: 'RoleUsers',
        hidden: true,
        meta: { title: '角色用户' },
        component: 'views/role-users',
      },
      {
        path: 'dict-config',
        name: 'DictConfig',
        meta: { title: '数据字典' },
        component: 'views/dict-config',
      },
      {
        path: 'export-record',
        name: 'ExportRecord',
        meta: { title: '导出记录' },
        component: 'views/export-record',
      },
    ]
  },
  // {
  //   path: '/test',
  //   component: 'layout',
  //   redirect: '/',
  //   meta: { title: '测试页面', icon: 'el-icon-s-grid', notPromote: true },
  //   children: [
  //     {
  //       path: 'test',
  //       name: 'Test',
  //       meta: { title: 'TEST' },
  //       component: 'views/test',
  //     },
  //     {
  //       path: 'lazy-load',
  //       name: 'LazyLoad',
  //       meta: { title: '懒加载' },
  //       component: 'views/lazy-load',
  //     },
  //     {
  //       path: 'virtual-list',
  //       name: 'VirtualList',
  //       meta: { title: '虚拟列表' },
  //       component: 'views/virtual-list',
  //     },
  //     {
  //       path: 'virtual-list-component',
  //       name: 'VirtualListComponent',
  //       meta: { title: '虚拟列表-组件' },
  //       component: 'views/virtual-list-component',
  //     },
  //     {
  //       path: 'virtual-list-component-plus',
  //       name: 'VirtualListComponentPlus',
  //       meta: { title: '虚拟列表-组件plus' },
  //       component: 'views/virtual-list-component-plus',
  //     },
  //   ]
  // },
  {
    path: '/model-presentation',
    name: 'ModelPresentation',
    meta: { title: '大模型演示', notPromote: true },
    component: 'views/model-presentation/index.vue',
  },
  {
    path: '/model-config',
    name: 'ModelConfig',
    meta: { 
      title: '大模型配置界面',
      icon: 'external-link',
      notPromote: false,
      blanck: true,
      iframe: false,
      url: 'http://172.16.86.101:18888/apps'
    },
  },
  // 本地开发登录用
  {
    path: '/login',
    hidden: true,
    component: 'views/login/index.vue',
  },
]

router = newRouter

export default router