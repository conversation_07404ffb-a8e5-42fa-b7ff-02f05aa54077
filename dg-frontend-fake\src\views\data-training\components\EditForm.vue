<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="editForm"
      :rules="rules"
      label-position="right"
      label-width="90px">
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="任务名称"
            prop="trainName">
            <el-input
              v-model="editForm.trainName"
              v-trim
              clearable
              placeholder="请输入任务名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="自助 ETL"
            prop="etlId">
            <el-select
              v-model="editForm.etlId"
              @change="handleEtlChange"
              filterable
              placeholder="请选择自助ETL">
              <el-option
                v-for="item in etlList"
                :label="item.ETL_NAME"
                :value="item.ETL_ID"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="训练模式"
            prop="trainMode">
            <el-radio-group v-model="editForm.trainMode">
              <el-radio label="1">动态</el-radio>
              <el-radio label="2">随机</el-radio>
              <el-radio label="3">自定义</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="训练团队"
            prop="teamId">
            <el-select
              v-model="editForm.teamId"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in teamList"
                :label="item.TEAM_NAME"
                :value="item.TEAM_ID"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="editForm.trainMode === '2'">
        <el-col :span="12">
          <el-form-item
            label="抽取数量"
            prop="limit">
            <el-input-number
              v-model="editForm.limit"
              controls-position="right"
              :min="1"
              :max="10000"
              placeholder="请输入"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="训练备注"
            prop="trainMemo">
            <el-input
              v-model="editForm.trainMemo"
              v-trim
              type="textarea"
              placeholder="请输入"
              maxlength="200"
              show-word-limit
              clearable
              :autosize="{ minRows: 2, maxRows: 10 }"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <section
        class="section-wrapper y-container no-padding"
        style="flex: 0; margin-bottom: 24px; height: max-content">
        <div class="y-bar">
          <h4 class="y-title">可查询字段</h4>
        </div>
        <div class="section-header">
          <span>序号</span>
          <span>字段名称</span>
          <span>字段备注</span>
          <span>字段类型</span>
          <span>数值范围</span>
        </div>
        <div
          class="section-item"
          v-for="(item, idx) in queryList">
          <span>{{ idx | formatFigure }}</span>
          <el-select
            v-model="item.column"
            placeholder="请选择字段名称"
            :disabled="lock">
            <el-option
              v-for="(item, idx) in queryList"
              :key="item.column"
              :value="item.column"></el-option>
          </el-select>
          <el-input
            v-model="item.columnLabel"
            v-trim
            placeholder="请输入字段备注"
            clearable
            readonly></el-input>
          <el-select
            :value="item.columnType"
            @change="changeColumnType(item)"
            placeholder="请选择字段类型"
            :disabled="lock">
            <el-option
              v-for="(item, key) in columnTypeList"
              :label="item"
              :value="Number(key)"></el-option>
          </el-select>
          <div class="wrapper">
            <el-radio-group
              :value="item.valueType"
              @input="changeValueType($event, item, idx)"
              :disabled="item.columnType != 2 && item.columnType != 5">
              <el-radio label="1">指定值</el-radio>
              <el-radio label="2">范围值</el-radio>
            </el-radio-group>
            <el-input
              v-if="item.valueType === '1'"
              v-model="queryArray[idx]"
              v-trim
              clearable
              placeholder="请输入数值"></el-input>
            <el-date-picker
              v-else-if="item.valueType === '2' && item.columnType == 5"
              v-model="queryArray[idx]"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
            <div
              v-else-if="item.valueType === '2' && item.columnType == 2"
              class="input-group">
              <el-input
                v-model="queryArray[idx][0]"
                v-trim
                clearable
                placeholder="请输入数值"></el-input>
              <span style="padding: 0 8px">-</span>
              <el-input
                v-model="queryArray[idx][1]"
                v-trim
                clearable
                placeholder="请输入数值"></el-input>
            </div>
          </div>
        </div>
      </section> -->
      <section
        v-if="editForm.trainMode === '3'"
        class="section-wrapper y-container no-padding">
        <div class="y-bar">
          <h4 class="y-title">数据预览</h4>
          <el-input
            v-model="formData.key"
            v-trim
            placeholder="请输入关键词进行搜索"
            size="small"
            clearable
            style="width: 220px"></el-input>
          <el-button
            v-debounce="fetchData"
            class="mini"
            type="primary">
            <i class="el-icon-search"></i>
            搜索
          </el-button>
        </div>
        <div class="y-container--tight no-padding">
          <el-table
            ref="table"
            :data="dataList"
            v-loading="tableLoading"
            v-reset-scroll="'div.el-table__body-wrapper'"
            @select="handleSelect"
            @select-all="handleSelectAll"
            stripe
            height="100%"
            fit
            style="width: 100%">
            <template>
              <el-table-column
                type="selection"
                width="50">
              </el-table-column>
              <el-table-column
                type="index"
                label="序号"
                width="50">
              </el-table-column>
              <template v-if="columnList">
                <el-table-column
                  v-for="(value, key) in columnList"
                  :key="key"
                  :prop="key"
                  align="center"
                  min-width="132"
                  show-overflow-tooltip>
                  <template
                    slot="header"
                    slot-scope="scope">
                    <overflow-text
                      :max="8"
                      :content="value"></overflow-text>
                  </template>
                </el-table-column>
              </template>
              <el-table-column :width="columnList ? '0' : undefined"></el-table-column>
            </template>
            <el-empty
              slot="empty"
              :image="require('@/assets/images/no-info.png')"
              description="暂无信息"></el-empty>
          </el-table>
        </div>
        <div class="y-bar">
          <p class="selection-tip">
            已选 <span class="selection-count">{{ addDataTraining.length }}</span> 条数据
          </p>
          <pagination
            :current-page.sync="formData.pageIndex"
            :page-size.sync="formData.pageSize"
            :total="total"
            @page="fetchData"></pagination>
        </div>
      </section>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >创建</el-button
      >
    </div>
  </div>
</template>

<script>
import { addTraining, getQueryList, getColumnList, getDataList, getEtlList } from '@/api/data-training'
import { getList } from '@/api/annotation-team'
import { VALUE_TYPES as columnTypeList } from '@/utils/constant'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      lock: true,
      etl: null,
      editForm: {
        trainName: null,
        etlId: null,
        trainMode: '1',
        teamId: null,
        limit: 0,
        trainMemo: '',
      },
      rules: {
        trainName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        etlId: [{ required: true, message: '请选择自助ETL', trigger: 'change' }],
        trainMode: [{ required: true, message: '请选择训练模式', trigger: 'change' }],
        limit: [{ required: true, message: '请输入训练数据量', trigger: 'blur' }],
      },
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        key: '',
      },
      total: 0,
      queryList: [],
      etlList: [],
      teamList: [],
      queryArray: [], //查询条件
      addDataTraining: [], //选择列表的row_id列表
      dataList: [],
      columnList: null,
      tableLoading: false,
      columnTypeList,
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      this.editForm = {
        trainName: null,
        etlId: null,
        trainMode: '1',
        teamId: null,
        limit: 1,
        trainMemo: '',
      }
      this.formData = {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        key: '',
      }
      this.total = 0
      this.queryList = []
      this.etlList = []
      this.queryArray = []
      this.addDataTraining = []
      this.dataList = []
      this.getEtlList()
      this.getTeamList()
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        if (this.editForm.trainMode === '3' && this.addDataTraining.length === 0) {
          this.$alert('请选中数据进行创建', '提示', {
            confirmButtonText: '确定',
            type: 'warning',
          })
          return false
        }

        const payload = {
          ...this.editForm,
          addDataTraining: this.addDataTraining,
        }

        const [err, res] = await addTraining(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.initPage()
              this.$emit('update-data')
            },
          })
        }
      })
    },
    async getEtlList() {
      const [err, res] = await getEtlList()
      if (res) {
        this.etlList = res.data || []
      }
    },
    async getTeamList() {
      const payload = {
        pageSize: 999,
        pageIndex: 1,
        pageType: 3,
      }
      const [err, res] = await getList(payload)
      if (res) {
        this.teamList = res.data || []
      }
    },
    async getQueryList() {
      const payload = {
        ...this.editForm,
      }
      delete payload.trainName

      const [err, res] = await getQueryList(payload)
      if (res && Array.isArray(res.data)) {
        this.queryList = res.data.map((item) => {
          item.valueType = '1'
          return item
        })
        this.queryArray = new Array(this.queryList.length).fill(null)
      }
    },
    async fetchData() {
      if (!this.editForm.etlId) {
        this.$alert('请先选择自助ETL', '提示', {
          confirmButtonText: '确定',
          type: 'warning',
        })
        return false
      }

      await this.getColumnList()
      await this.getDataList()
    },
    async getColumnList() {
      const [err, res] = await getColumnList({ etlId: this.editForm.etlId })
      if (res) {
        this.columnList = res.data
        this.$nextTick(() => {
          this.$refs.table.doLayout()
        })
      }
    },

    async getDataList() {
      let queryArray = this.formatQueryArray()
      if (!queryArray) {
        return false
      }

      const payload = {
        ...this.formData,
        ...this.editForm,
        queryArray: JSON.stringify(queryArray),
      }

      delete payload.trainName

      const [err, res] = await getDataList(payload)
      if (res) {
        this.dataList = res.data
        this.total = res.totalRow

        // 反显选中的row
        if (this.addDataTraining.length > 0) {
          this.addDataTraining.forEach((item) => {
            let idx = this.dataList.findIndex((i) => i.ROW_ID === item)
            if (idx !== -1) {
              this.$nextTick(() => {
                this.$refs.table.toggleRowSelection(this.dataList[idx], true)
              })
            }
          })
        }
      }
    },
    changeValueType(value, item, idx) {
      if (value == 2) {
        this.$set(this.queryArray, idx, ['', ''])
      } else {
        this.$set(this.queryArray, idx, '')
      }
      this.queryList[idx].valueType = value
    },
    handleSelect(selection, row) {
      if (selection.length > 0) {
        this.addRow(row)
      } else {
        this.removeRow(row)
      }
    },
    handleSelectAll(selection) {
      if (selection.length > 0) {
        selection.forEach((row) => {
          this.addRow(row)
        })
      } else {
        this.dataList.forEach((row) => {
          this.removeRow(row)
        })
      }
    },
    addRow(row) {
      if (this.addDataTraining.includes(row.ROW_ID)) {
        return false
      } else {
        this.addDataTraining.push(row.ROW_ID)
      }
    },
    removeRow(row) {
      if (this.addDataTraining.includes(row.ROW_ID)) {
        this.addDataTraining.splice(this.addDataTraining.indexOf(row.ROW_ID), 1)
      } else {
        return false
      }
    },
    formatQueryArray() {
      let flag = true
      const queryArray = this.queryArray.map((item, idx) => {
        const query = this.queryList[idx]
        if (!this.checkValue(item) || (Array.isArray(item) && item?.every((item) => !this.checkValue(item)))) {
          return null
        } else if (this.queryList[idx].valueType == 1) {
          return {
            type: '1',
            value1: item,
            queryColumn: query.column,
          }
        } else {
          if (this.checkValue(item[0]) && this.checkValue(item[1])) {
            return {
              type: '2',
              value1: item[0],
              value2: item[1],
              queryColumn: query.column,
            }
          } else {
            flag = false
            return null
          }
        }
      })
      if (flag) {
        return queryArray.filter((item) => this.checkValue(item))
      } else {
        this.$alert('范围值请填写完整', '提示', {
          confirmButtonText: '确定',
          type: 'warning',
        })
        return false
      }
    },
    checkValue(value) {
      return value || value === 0
    },
    handleEtlChange(etlId) {
      let etl = this.etlList.find((item) => item.ETL_ID === etlId)
      if (etl) this.editForm.etlVersion = etl.VER
      this.getQueryList()
      this.dataList = []
      this.columnList = null
    },
  },
  filters: {
    formatFigure(figure) {
      if (String(figure)?.length == 1) {
        return '0' + figure
      } else {
        return figure
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;
  min-width: 700px;

  .section-wrapper {
    border-radius: 4px;
    border: 1px solid $borderColor;

    .y-title {
      font-size: 16px;
      font-weight: normal;
      line-height: 24px;
    }

    .section-header,
    .section-item,
    .wrapper {
      display: grid;
      grid-template-columns: 30px 2fr 1fr 1fr 3fr;
      grid-auto-rows: auto;
      align-items: center;
      gap: 0 32px;
      width: 100%;
    }

    .section-header {
      padding: 12px 24px;
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      color: $txtColor-light;
      background-color: transparentize($themeColor, 0.95);
    }

    .section-item {
      padding: 8px 16px 8px 24px;

      + .section-item {
        border-top: 1px solid $borderColor;
      }

      .wrapper {
        grid-template-columns: 2fr 3fr;
        gap: 0 16px;

        .input-group {
          display: flex;
          align-items: center;
        }
      }

      .el-radio-group {
        @include flex-row;
      }

      .el-date-editor--daterange.el-input__inner {
        width: 100%;
      }
    }
  }

  .selection-tip {
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    color: $txtColor-light;

    .selection-count {
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
      color: $themeColor;
    }
  }

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }
}
</style>
