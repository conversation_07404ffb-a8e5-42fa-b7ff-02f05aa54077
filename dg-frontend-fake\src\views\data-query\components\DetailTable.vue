<template>
  <div class="detail-table y-container--tight no-padding">
    <div class="y-container--tight">
      <div
        v-if="groupedShowList?.length > 0"
        class="table-data y-table el-table">
        <table>
          <col width="15%" />
          <col width="35%" />
          <col width="15%" />
          <col width="35%" />
          <tr v-for="group in groupedShowList">
            <template v-for="(item, idx) in group">
              <th class="el-table__cell">
                <div class="cell">
                  <overflow-text
                    :max="10"
                    :content="dict[item?.[0]] || item?.[0]"></overflow-text>
                </div>
              </th>
              <td
                class="el-table__cell"
                v-if="group.length % 2 === 0">
                <div class="cell">
                  <overflow-text
                    :max="120"
                    :content="item?.[1]"></overflow-text>
                </div>
              </td>
              <td
                class="el-table__cell"
                colspan="3"
                v-else>
                <div class="cell">
                  <overflow-text
                    :max="300"
                    :content="item?.[1]"></overflow-text>
                </div>
              </td>
            </template>
          </tr>
        </table>
      </div>
    </div>
    <div class="footer y-bar">
      <el-button
        type="primary"
        size="small"
        @click="$emit('close-detail')"
        >确认</el-button
      >
    </div>
  </div>
</template>

<script>
import { getTagDict, getEtlDict, getTagDetail, getEtlDetail } from '@/api/data-query'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
    idData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      dataList: [],
      hideList: ['TRANSFER_ID', 'ROW_ID', 'ETL_ID', 'BATCH_ID', 'DATA_ROW_JSON'],
      dict: {},
    }
  },
  computed: {
    // 排除以及挑选
    pickedList() {
      const filteredList = Object.entries(this.dataList).filter((item) => !this.hideList.includes(item[0]) && this.dict[item[0]] !== undefined)
      const limit = 24
      // 原地排序优化，没写完
      // const slimit = 120
      // let p = filteredList.length - 1
      // for (let i = 0; i < p; i++) {
      //   let item = filteredList[i]
      //   let last = filteredList[p]
      //   if (item[1] && item[1].length > limit) {
      //     while (last[1].length > limit) {
      //       p--

      //       if (p === i + 1) {
      //         break
      //       }
      //     }
      //     let temp = filteredList[i]
      //     filteredList[i] = filteredList[p]
      //     filteredList[p] = temp
      //     p--
      //   }
      // }

      const stack = []
      filteredList.forEach((item, idx, arr) => {
        if (item[1] && item[1].length > limit) {
          stack.push(item)
          arr.splice(idx, 1)
        }
      })
      stack.sort((a, b) => a[1].length - b[1].length)
      return filteredList.concat(stack)
    },
    // dataList两个一组分的新数组
    groupedShowList() {
      return this.pickedList.reduce((acc, cur, idx) => {
        if (idx % 2 === 0) {
          acc.push([cur])
        } else {
          acc[acc.length - 1].push(cur)
        }
        return acc
      }, [])
    },
  },
  watch: {
    'data.ROW_ID': {
      handler() {
        this.initPage()
      },
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.dataList = []
      if (this.data && JSON.stringify(this.data) != '{}') {
        this.getDict()
        this.getDetail()
      }
    },
    async getDetail() {
      const payload = {
        rowId: this.data.ROW_ID,
        ...this.idData,
      }
      let action
      if (payload.etlId) {
        action = getEtlDetail
      } else if (payload.tagDsId) {
        action = getTagDetail
      }
      const [err, res] = await action(payload)
      if (res) {
        this.dataList = res.data || {}
      }
    },
    async getDict() {
      const payload = {
        ...this.idData,
      }
      let action
      if (payload.etlId) {
        action = getEtlDict
      } else if (payload.tagDsId) {
        action = getTagDict
      }
      const [err, res] = await action(payload)
      if (res) {
        this.dict = res.data || {}
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.detail-table::v-deep {
  > .y-container--tight {
    .y-table {
      flex: 0 0 max-content;

      th {
        background-color: transparentize($themeColor, 0.95);
      }
    }
  }

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }
}
</style>
