<template>
  <div class="detail-table">
    <!-- <div class="tip">
        <div class="y-bar">
          <h4 class="title">工单治理建议</h4>
        </div>
        <div class="content">
          <el-input
            ref="suggestion"
            type="textarea"
            :autosize="{ minRows: 4 }"
            maxlength="200"
            show-word-limit
            placeholder="请输入建议"
            clearable
            v-model="corrections.ORDER_CONTENT_AFFER"
            v-trim></el-input>
        </div>
      </div>
      <div class="header">
        <el-image
          :src="require('@/assets/images/training-card-icon.svg')"
          style="flex: 0 1 max-content; width: 60px; min-width: 60px; height: 60px"></el-image>
        <div class="wrapper">
          <overflow-text
            v-if="data.DG_OUTLINE"
            :max="60"
            :content="data.DG_OUTLINE"></overflow-text>
          <span>工单编号：{{ data.RECORD_ID }}</span>
        </div>
      </div>
      <div
        v-if="groupedShowList?.length > 0"
        class="table-data y-table el-table">
        <table>
          <col width="15%" />
          <col width="35%" />
          <col width="15%" />
          <col width="35%" />
          <tr v-for="group in groupedShowList">
            <template v-for="item in group">
              <th class="el-table__cell">
                <div class="cell">
                  <overflow-text
                    :max="8"
                    :content="item.columnLabel"></overflow-text>
                </div>
              </th>
              <td class="el-table__cell">
                <div class="cell">
                  <overflow-text
                    :max="120"
                    :content="item.content"></overflow-text>
                </div>
              </td>
            </template>
          </tr>
        </table>
      </div> -->
    <template v-if="markList?.length > 0">
      <h4 class="title-section">任务执行结果</h4>
      <div class="table-mark y-table el-table">
        <table>
          <col width="15%" />
          <col width="35%" />
          <col width="15%" />
          <col width="35%" />
          <tr v-for="item in markList">
            <th class="el-table__cell">
              <div class="cell">
                <overflow-text
                  :max="8"
                  :content="item.columnLabel"></overflow-text>
              </div>
            </th>
            <td class="el-table__cell">
              <div class="cell flex-row">
                <overflow-text
                  :max="120"
                  :content="item.content"></overflow-text>
                <el-tag
                  v-show="item.mark"
                  size="small"
                  type="primary">
                  <svg-icon icon="tags"></svg-icon>
                  标注
                </el-tag>
              </div>
            </td>
            <th class="el-table__cell">
              <div class="cell"><span>修正结果</span></div>
            </th>
            <td class="el-table__cell">
              <div class="cell">
                <el-input
                  v-model="corrections[item.column]"
                  v-trim
                  clearable
                  placeholder="请输入修正结果，输入即视为修正"></el-input>
              </div>
            </td>
          </tr>
        </table>
      </div>
    </template>
    <el-empty
      v-else
      slot="empty"
      :image="require('@/assets/images/no-info.png')"
      description="暂无信息"></el-empty>
  </div>
</template>

<script>
import { getMarkListHeaders, getMarkDetail, mark } from '@/api/data-training'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      contents: null,
      headers: null,
      showList: [],
      markList: [],
      corrections: {
        ORDER_CONTENT_AFFER: '',
      },
    }
  },
  computed: {
    // dataList两个一组分的新数组
    groupedShowList() {
      return this.showList.reduce((acc, cur, idx) => {
        if (idx % 2 === 0) {
          acc.push([cur])
        } else {
          acc[acc.length - 1].push(cur)
        }
        return acc
      }, [])
    },
  },
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    async initPage() {
      this.contents = null
      this.headers = null
      this.showList = []
      this.markList = []
      this.corrections = {
        ORDER_CONTENT_AFFER: '',
      }
      if (this.data && JSON.stringify(this.data) != '{}') {
        await this.getMarkListHeaders()
        await this.getDetail()
      }
    },
    async submitForm() {
      if (!this.data) {
        return false
      }
      const payload = {
        recordId: this.data.RECORD_ID,
        trainDatasetId: this.data.TRAIN_DATASET_ID,
        trainBatchId: this.data.TRAIN_BATCH_ID,
        resultJson: this.formatResultJson(),
      }

      const [err, res] = await mark(payload)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.$emit('close-detail')
            this.corrections = {
              ORDER_CONTENT_AFFER: '',
            }
            this.$emit('update-data')
          },
        })
      }
    },
    formatResultJson() {
      const res = { ...this.contents }
      for (const key in this.corrections) {
        if (key !== 'ORDER_CONTENT_AFFER') {
          res[key + '_NEW'] = this.corrections[key]
        } else {
          res[key] = this.corrections[key]
        }
      }
      return res
    },
    async getMarkListHeaders() {
      const [err, res] = await getMarkListHeaders(this.data.etlId)
      if (res) {
        this.headers = res.data
      }
    },
    async getDetail() {
      const payload = {
        recordId: this.data.RECORD_ID,
        etlId: this.data.etlId,
      }
      const [err, res] = await getMarkDetail(payload)
      if (res) {
        this.settleList(res?.data, res?.markColumns, res?.queryColumns)
        this.contents = res.data
        this.$set(this.corrections, 'ORDER_CONTENT_AFFER', res.data?.ORDER_CONTENT_AFFER)
      }
    },
    // 整理数据，
    // 原本是res.data包含所有，res.markColumns包含修改列表的置顶，res.queryColumns是所有修改列表项，包含res.markColumns
    // this.showList只包含最上面的展示列表
    // this.markList是修改列表，部分置顶带标记的（mark: true）
    settleList(contents, topList, queryList) {
      if (!contents) {
        return false
      }

      const getMap = (list, prop) => {
        return list?.reduce((acc, cur) => {
          let key = cur[prop]
          if (!acc[key]) {
            acc[key] = cur
          }
          return acc
        }, {})
      }

      const markListMap = getMap(topList, 'column')
      const queryListMap = getMap(queryList, 'column')

      // 展示列表
      this.showList = Object.entries(contents)
        .filter(([key, content]) => {
          if (queryListMap[key] || !this.headers[key]) return false
          return true
        })
        .map(([key, content]) => {
          return {
            column: key,
            columnLabel: this.headers[key],
            content,
          }
        })

      if (queryList?.length === 0) {
        return false
      }

      // 置顶修改列表
      this.markList = topList.map((item) => {
        item.mark = true
        return item
      })

      // 非置顶修改列表
      const restQueries = queryList.filter(({ column }) => {
        if (markListMap[column]) return false
        return true
      })

      if (restQueries.length > 0) {
        // 置顶+非置顶
        this.markList = this.markList.concat(restQueries)
      }

      this.markList = this.markList.map((item) => {
        item.content = contents[item.mapColumn] || contents[item.column]
        return item
      })

      // 回填以NEW结尾的字段
      for (const key in queryListMap) {
        let value = contents[key + '_NEW']
        if (value) {
          this.$set(this.corrections, key, value)
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.detail-table::v-deep {
  @include full;
  .tip {
    width: 100%;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    color: $txtColor-light;
    background: $bgColor-dark;

    > .y-bar {
      padding-left: 0px;

      .title {
        padding: 5px 24px;
        font-size: 14px;
        font-weight: bold;
        line-height: 22px;
        color: $themeColor;
        background: linear-gradient(90deg, transparentize($themeColor, 0.75) 0%, transparentize($themeColor, 1) 100%);
      }
    }

    > .content {
      padding: 16px 24px;
      width: 100%;

      .el-textarea__inner {
        background-color: $bgColor;
      }
    }
  }

  .header {
    @include flex-row;
    margin: 24px 0 16px;

    .wrapper {
      margin-left: 16px;

      .overflow-text > span,
      h3 {
        // @include text-overflow(1);
        margin-bottom: 4px;
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        color: $txtColor;
      }

      span {
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        color: $txtColor-light;
      }
    }
  }

  .y-table {
    flex: 0 0 max-content;
  }

  .title-section {
    padding-bottom: 16px;
    font-size: 16px;
    font-weight: normal;
    line-height: 24px;
    color: $txtColor;
  }
}
</style>
