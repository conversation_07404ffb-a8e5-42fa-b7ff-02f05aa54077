<template>
  <layout-menuless>
    <base-card
      id="etl-flow"
      class="y-page">
      <!-- 左侧 -->
      <Menu
        :menu-list="moduleList"
        @drag-item="drag($event)"></Menu>
      <!-- 右侧 -->
      <div class="draw-area">
        <!-- 头部 -->
        <div class="draw-area-header">
          <div class="draw-area-header-tool">
            <el-button
              size="small"
              class="mini"
              type="primary"
              :disabled="!editable"
              @click="submitData"
              plain
              icon="el-icon-check"
              >保存</el-button
            >
            <el-button
              size="small"
              class="mini"
              @click="flowSetting"
              type="primary"
              plain
              icon="el-icon-s-operation"
              >扩展配置</el-button
            >
            <el-button
              v-if="workingStatus === 1"
              plain
              size="small"
              class="mini"
              :disabled="!editable"
              @click="interuptFlow"
              type="danger"
              icon="el-icon-document-delete"
              >终止任务</el-button
            >
            <el-button
              v-else
              size="small"
              class="mini"
              :disabled="!editable"
              @click.stop="submitData('execute')"
              type="primary"
              icon="el-icon-document"
              >执行任务</el-button
            >
          </div>
          <template v-if="noEdit">
            <span class="draw-area-header-title">
              <span>{{ formData.etlName }}</span>
              <el-tag
                v-if="ver"
                type="info"
                :disable-transitions="true"
                style="margin-left: 4px"
                >{{ `Ver. ${ver}` }}</el-tag
              >
              <span style="margin-left: 16px">最大线程：{{ formData.maxThreadCount }}</span>
              <i
                v-if="editable"
                @click="noEdit = false"
                class="el-icon-edit"
                style="margin-left: 16px; font-size: 16px; color: #868686; cursor: pointer"></i>
            </span>
          </template>
          <span
            v-else
            class="draw-area-header-title">
            <span>名称：</span>
            <el-input
              v-model="formData.etlName"
              placeholder="请输入名称"
              clearable
              style="width: 180px"
              size="small"></el-input>
            <span style="margin-left: 10px">最大线程：</span>
            <el-input-number
              v-model="formData.maxThreadCount"
              :min="1"
              :max="50"
              size="small"></el-input-number>
            <i
              @click="noEdit = true"
              class="el-icon-check"
              style="margin-left: 16px; font-size: 16px; color: #868686; cursor: pointer"></i>
          </span>
        </div>
        <!-- 流程图 -->
        <div class="draw-area-flow">
          <div class="flow_region">
            <div
              id="flowWrap"
              ref="flowWrap"
              class="flow-wrap"
              @drop="drop($event)"
              @dragover="allowDrop($event)">
              <div id="flow">
                <div
                  v-show="auxiliaryLine.isShowXLine"
                  class="auxiliary-line-x"
                  :style="{ width: auxiliaryLinePos.width, top: auxiliaryLinePos.y + 'px', left: auxiliaryLinePos.offsetX + 'px' }"></div>
                <div
                  v-show="auxiliaryLine.isShowYLine"
                  class="auxiliary-line-y"
                  :style="{ height: auxiliaryLinePos.height, left: auxiliaryLinePos.x + 'px', top: auxiliaryLinePos.offsetY + 'px' }"></div>
                <FlowNode
                  v-for="item in data.nodeList"
                  :id="item.id"
                  :key="item.id"
                  :node="item"
                  :editable="editable"
                  @selectNode="selectNode"
                  @setNodeName="setNodeName"
                  @deleteNode="deleteNode"
                  @sclick="selectItem"
                  :runInfo="getRunInfo(item)"
                  @changeLineState="changeLineState"></FlowNode>
              </div>
            </div>
          </div>
        </div>
        <!-- tab -->
        <Tab
          ref="tab"
          :flowId="flowId"
          :previewCols="previewCols"
          :currentNode="currentNode"
          :nodeList="data.nodeList"
          @update-working-status="workingStatus = $event"></Tab>
      </div>
      <!-- modals -->
      <component
        v-if="flowJobAddVisible"
        :is="currentTemp"
        :editable="editable"
        @update="updateNodeData"
        :node="currentNode"
        :configuration="configuration"
        :data="currentData"
        :currentCols="currentCols"
        :sourceType="sourceType"
        :visible.sync="flowJobAddVisible" />
    </base-card>
  </layout-menuless>
</template>

<script>
const path = require('path')
import LayoutMenuless from '@/layout/LayoutMenuless'
import FlowNode from './components/NodeItem'
import Menu from './components/Menu'
import Tab from './components/Tab'
import jsPlumbControl from './core/jsPlumbControlMixin.js' // 节点控制
import dataControl from './core/dataControlMixin.js' // 配置数据控制
import { getAssemblyDict, interuptFlow } from '@/api/etl-flow'

// 引入所有modal
const modules = require.context('./modals', false, /\.vue$/)
const modals = Object.fromEntries(
  modules.keys().map((key) => {
    const module = modules(key)
    const name = path.basename(key, '.vue')
    return [name, module.default]
  })
)

export default {
  components: {
    LayoutMenuless,
    FlowNode,
    Menu,
    Tab,
    ...modals,
  },
  mixins: [jsPlumbControl, dataControl],
  data() {
    return {
      etlId: undefined,
      tempId: undefined, // 无etlId在执行时获得的临时id
      ver: undefined,
      editable: true, // 编辑锁
      flowJobAddVisible: false, // 配置弹窗开关
      workingStatus: undefined,
      currentNode: null,
      currentTemp: '',
      moduleDict: {}, // 数据治理组件(nodeType:6)字典

      // 名称线程数编辑
      noEdit: true,
      formData: {
        etlName: '未命名流程',
        maxThreadCount: 1,
      },
    }
  },
  computed: {
    // 数据源类型
    sourceType() {
      return this.sourceNode ? this.sourceNode.dsType : null // excel | ds | null
    },
    // 当前流程id
    flowId() {
      return this.etlId || this.tempId
    },
  },
  watch: {},
  created() {
    this.getDict()
  },
  beforeDestroy() {},
  methods: {
    async getDict() {
      //数据处理组件字典
      const [err, res] = await getAssemblyDict()
      if (res) {
        this.$set(this.moduleList, 3, {
          name: '数据治理组件',
          children: this.formatModuleList(res.data.children, 'children'),
        })
      }
    },
    // 递归格式化组件列表
    formatModuleList(data, prop) {
      if (Array.isArray(data)) {
        if (data.length === 0) {
          return []
        }
        if (prop === 'children') {
          return data.map((item) => this.formatModuleList(item))
        } else if (prop === 'module') {
          return data.map((i) => {
            this.moduleDict[i.dataComId] = i
            i.class = 'type3'
            i.name = i.dataComName
            i.nodeType = '6'
            return i
          })
        }
      } else if (typeof data === 'object') {
        for (const key of ['children', 'module']) {
          data[key] && (data[key] = this.formatModuleList(data[key], key))
        }
        return data
      } else {
        return false
      }
    },
    // 中断执行
    async interuptFlow() {
      //中断执行
      if (!this.flowId) {
        return false
      }

      try {
        await this.$confirm('流程运行即将强制停止，流程状态即将回滚至执行前状态，请确认是否继续执行？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        const [err, res] = await interuptFlow(this.flowId)
        if (res.state == 1) {
          this.$message.success(res.msg || '执行成功')
          this.workingStatus = 0
        }
      } catch (e) {
        return
      }
    },
    getRunInfo(data) {
      return this.$refs.tab?.getRunInfo(data)
    },
    flowSetting() {
      this.currentTemp = 'addFlowJobSetting'
      this.flowJobAddVisible = true
    },
    //获取节点预览数据
    getFlowNodeData() {
      let nodeId = this.currentNode.id
      if (!nodeId) {
        return false
      }
      this.$refs.tab?.getFlowNodeData(nodeId)
    },
  },
}
</script>

<style lang="scss">
#etl-flow {
  @include flex-row;

  #menu {
    flex: 0 0 288px;
  }

  .draw-area {
    @include flex-col;
    flex: 1 1 calc(100% - 288px);
    overflow: hidden;
    padding: 20px 0;
    height: 100%;
    background: #f3f4f8 url(@/assets/images/block.png) repeat;

    .draw-area-header {
      background-color: #fff;
      margin: 0 20px;
      padding: 10px 20px;
      width: calc(100% - 40px);
      box-shadow: 0px 5px 9px 0px rgba(0, 0, 0, 0.2);
      color: #333;
      .draw-area-header-title {
        line-height: 32px;
      }
      .draw-area-header-tool {
        float: right;
      }
    }

    .draw-area-flow {
      flex: 1 1 420px;
      width: 100%;
      position: relative;
      overflow: auto;
    }

    .flow_region {
      display: flex;
      width: 100%;
      height: 100%;
      margin: 0 auto;
      .nodes-wrap {
        width: 150px;
        height: 100%;
        border-right: 1px solid #ccc;
        .node-item2 {
          display: flex;
          height: 40px;
          width: 80%;
          margin: 5px auto;
          border: 1px solid #ccc;
          line-height: 40px;
          &:hover {
            cursor: grab;
          }
          &:active {
            cursor: grabbing;
          }
          .log {
            width: 40px;
            height: 40px;
          }
          .name {
            width: 0;
            flex-grow: 1;
          }
        }
      }
      .flow-wrap {
        height: 100%;
        position: relative;
        overflow: hidden;
        outline: none !important;
        flex-grow: 1;
        #flow {
          position: relative;
          width: 100%;
          height: 100%;
          .auxiliary-line-x {
            position: absolute;
            border: 0.5px dashed #0555ce;
            z-index: 9999;
          }
          .auxiliary-line-y {
            position: absolute;
            border: 0.5px dashed #0555ce;
            z-index: 9999;
          }
        }
      }
    }
  }

  .textoverflow {
    @include text-overflow(1);
  }

  .el-drawer {
    .footer {
      justify-content: flex-end;
      border-top: 1px solid $borderColor;
    }
  }
}

.tabs-full {
  @include full;
  display: flex;
  flex-direction: column;

  .el-tabs__content {
    flex: 1;
  }

  .el-tab-pane {
    @include full;
    overflow: auto;
  }
}

.jtk-connector.active {
  z-index: 9999;
  path {
    stroke: #150042;
    stroke-width: 1.5;
    animation: ring;
    animation-duration: 3s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    stroke-dasharray: 5;
  }
}

@keyframes ring {
  from {
    stroke-dashoffset: 50;
  }
  to {
    stroke-dashoffset: 0;
  }
}
</style>
