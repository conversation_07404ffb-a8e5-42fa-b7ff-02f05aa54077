import { get, post } from '@/http/request'
import { dataSet } from '@/api/PATH'

// 文件数据集
export function getList(formData) {
  return post('/dg-etl-mgr/webcall?action=fileDataSet.pageDataSet', { data: JSON.stringify(formData) })
}

export function addDataset(formData) {
  return post('/dg-etl-mgr/servlet/fileDataSet?action=uploadFileDataSet', formData, null, { 'Content-Type': 'multipart/form-data' }, { timeout: 1 * 60 * 1000 })
}

export function updateDataset(formData) {
  return post('/dg-etl-mgr/servlet/fileDataSet?action=updateFileDataSet', { data: JSON.stringify(formData) })
}

export function deleteDataset(id) {
  return post('/dg-etl-mgr/servlet/fileDataSet?action=removeFileDataSet', { data: JSON.stringify({ dataSetId: id }) })
}


export function getFileList(formData) {
  return post('/dg-etl-mgr/webcall?action=fileDataSet.pageDataItem', { data: JSON.stringify(formData) })
}

export function getFile(id) {
  return post('/dg-etl-mgr/servlet/fileDataItem?action=downloadOrPreviewFile', { data: JSON.stringify({ fileId: id }) })
}

export function deleteFile(id) {
  return post('/dg-etl-mgr/servlet/fileDataItem?action=deleteFile', { data: JSON.stringify({ fileId: id }) })
}

export function importFile(formData) {
  return post('/dg-etl-mgr/servlet/fileDataItem?action=updateFiles', formData, null, { 'Content-Type': 'multipart/form-data' }, { timeout: 1 * 60 * 1000 })
}
