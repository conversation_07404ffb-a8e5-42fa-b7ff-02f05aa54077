import { jsPlumb } from 'jsplumb'
import moduleList from './moduleList'
import { jsplumbSetting, jsplumbConnectOptions, jsplumbSourceOptions, jsplumbTargetOptions } from './jsplumbSetting'
import panzoom from 'panzoom'
import { generateId } from '@/utils'

// jsPlumb相关
export default {
  data() {
    return {
      jsPlumb: null,
      draggingItem: null,
      moduleList,
      data: {
        nodeList: [],
        lineList: [],
      },
      auxiliaryLine: { isShowXLine: false, isShowYLine: false }, //对齐辅助线是否显示
      auxiliaryLinePos: { width: '100%', height: '100%', offsetX: 0, offsetY: 0, x: 20, y: 20 },

      rectAngle: {
        px: '', //多选框绘制时的起始点横坐标
        py: '', //多选框绘制时的起始点纵坐标
        left: 0,
        top: 0,
        height: 0,
        width: 0,
      },
      snapshot: null, // 删除前的sortedNodes快照
    }
  },
  computed: {
    // 数据源节点
    sourceNode() {
      return this.data.nodeList.find(node => node.nodeType === '1')
    },
    // 目标源节点
    targetNode() {
      return this.data.nodeList.find(node => node.nodeType === '7')
    },
    // 按连线顺序node列表
    sortedNodes() {
      let list = []
      let nodes = this.data.nodeList
      let lines = this.data.lineList
      if (!this.sourceNode) {
        return list
      }
      let next = this.sourceNode
      list.push(next)
      let line = lines.find(line => line.from === next.id)
      while (line) {
        next = nodes.find(node => node.id === line.to)
        list.push(next)
        line = lines.find(line => line.from === next.id)
      }
      return list
    },
    // 流程连线（从数据源出发的）
    sortedLines() {
      let list = []
      let nodes = this.data.nodeList
      let lines = this.data.lineList
      if (!this.sourceNode) {
        return list
      }
      let next = this.sourceNode
      let line = lines.find(line => line.from === next.id)
      while (line) {
        list.push(line)
        next = nodes.find(node => node.id === line.to)
        line = lines.find(line => line.from === next.id)
      }
      return list
    },
  },
  methods: {
    initJsPlumb() {
      this.jsPlumb = jsPlumb.getInstance()
      this.jsPlumb.ready(() => {
        // 导入默认配置
        this.jsPlumb.importDefaults(jsplumbSetting)
        //完成连线前的校验
        this.jsPlumb.bind('beforeDrop', evt => {
          const { sourceId, targetId } = evt
          if (sourceId === this.targetNode?.id || targetId === this.sourceNode?.id) {
            this.$message.error('连接方式有误，请重新连接')
            return false
          }
          return true
        })
        //连线双击删除事件
        this.jsPlumb.bind('dblclick', (conn, originalEvent) => {
          console.log(conn, originalEvent)
          if (!this.editable) {
            return false
          }
          this.confirmDelLine(conn)
        })
        //断开连线后，维护本地数据
        this.jsPlumb.bind('connectionDetached', evt => {
          if (!this.editable) {
            return false
          }
          this.deleLine(evt)
        })
        this.loadEasyFlow()
        // 会使整个jsPlumb立即重绘。
        this.jsPlumb.setSuspendDrawing(false, true)
      })
      this.initPanZoom()
    },
    // 加载流程图
    loadEasyFlow() {
      // 初始化节点
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i]
        this.configNode(node)
      }

      // 初始化连线
      this.jsPlumb.unbind('connection') //取消连接事件
      for (let i = 0; i < this.data.lineList.length; i++) {
        let line = this.data.lineList[i]
        this.jsPlumb.connect(
          {
            source: line.from,
            target: line.to
          },
          jsplumbConnectOptions
        )
      }
      this.jsPlumb.bind('connection', evt => {
        if (!this.editable) {
          return false
        }
        const line = this.addLine(evt)
        if (this.sortedNodes.find(item => item.id === line.from || item.id === line.to)) {
          this.verifyNodeData()
        }
      })
    },
    initPanZoom() {
      const mainContainer = this.jsPlumb.getContainer()
      const mainContainerWrap = mainContainer.parentNode
      const pan = panzoom(mainContainer, {
        smoothScroll: false,
        bounds: true,
        // autocenter: true,
        zoomDoubleClickSpeed: 1,
        minZoom: 0.5,
        maxZoom: 2,
        //设置滚动缩放的组合键，默认不需要组合键
        beforeWheel: (e) => {
          // console.log(e)
          // let shouldIgnore = !e.ctrlKey
          // return shouldIgnore
        },
        beforeMouseDown: function (e) {
          // allow mouse-down panning only if altKey is down. Otherwise - ignore
          const shouldIgnore = e.ctrlKey
          return shouldIgnore
        }
      })
      this.jsPlumb.mainContainerWrap = mainContainerWrap
      this.jsPlumb.pan = pan
      // 缩放时设置jsPlumb的缩放比率
      pan.on('zoom', e => {
        const { x, y, scale } = e.getTransform()
        this.jsPlumb.setZoom(scale)
        //根据缩放比例，缩放对齐辅助线长度和位置
        this.auxiliaryLinePos.width = (1 / scale) * 100 + '%'
        this.auxiliaryLinePos.height = (1 / scale) * 100 + '%'
        this.auxiliaryLinePos.offsetX = -(x / scale)
        this.auxiliaryLinePos.offsetY = -(y / scale)
      })
      pan.on('panend', (e) => {
        const { x, y, scale } = e.getTransform()
        this.auxiliaryLinePos.width = (1 / scale) * 100 + '%'
        this.auxiliaryLinePos.height = (1 / scale) * 100 + '%'
        this.auxiliaryLinePos.offsetX = -(x / scale)
        this.auxiliaryLinePos.offsetY = -(y / scale)
      })

      // 平移时设置鼠标样式
      mainContainerWrap.style.cursor = 'grab'
      mainContainerWrap.addEventListener('mousedown', function wrapMousedown() {
        this.style.cursor = 'grabbing'
        mainContainerWrap.addEventListener('mouseout', function wrapMouseout() {
          this.style.cursor = 'grab'
        })
      })
      mainContainerWrap.addEventListener('mouseup', function wrapMouseup() {
        this.style.cursor = 'grab'
      })
    },
    configNode(node) {
      let sourceOpts = Object.assign(jsplumbSourceOptions, node.nodeType === '1' ? { isTarget: false } : {})
      let targetOpts = Object.assign(jsplumbTargetOptions, node.nodeType === '7' ? { isSource: false } : {})
      // 设置源点，可以拖出线连接其他节点
      this.jsPlumb.makeSource(node.id, sourceOpts)
      // 设置目标点，其他源点拖出的线可以连接该节点
      this.jsPlumb.makeTarget(node.id, targetOpts)
      this.jsPlumb.draggable(node.id, {
        grid: [5, 5], //节点移动最小距离
        drag: (params) => {
          this.alignForLine(node.id, params.pos)
        },
        start: () => { },
        stop: (params) => {
          this.auxiliaryLine.isShowXLine = false
          this.auxiliaryLine.isShowYLine = false
          this.changeNodePosition(node.id, params.pos)
        }
      })
    },
    //移动节点时，动态显示对齐线
    alignForLine(nodeId, position) {
      let showXLine = false, showYLine = false
      this.data.nodeList.forEach(el => {
        if (el.id !== nodeId && el.left === position[0] + 'px') {
          this.auxiliaryLinePos.x = position[0] + 50
          showYLine = true
        }
        if (el.id !== nodeId && el.top === position[1] + 'px') {
          this.auxiliaryLinePos.y = position[1] + 50
          showXLine = true
        }
      })
      this.auxiliaryLine.isShowYLine = showYLine
      this.auxiliaryLine.isShowXLine = showXLine
    },
    changeNodePosition(nodeId, pos) {
      let find = this.data.nodeList.find(n => nodeId == n.id)
      if (find) {
        find.left = pos[0] + 'px'
        find.top = pos[1] + 'px'
      }
    },
    drag(item) {
      this.draggingItem = { ...item }
    },
    drop(e) {
      if (!this.editable) {
        return false
      }

      if (!this.draggingItem) {
        return false
      }
      const containerRect = this.jsPlumb.getContainer().getBoundingClientRect()
      const scale = this.getScale()
      let left = (e.pageX - containerRect.left - 60) / scale
      let top = (e.pageY - containerRect.top - 20) / scale

      const temp = {
        ...this.draggingItem,
        id: generateId(8),
        top: (Math.round(top / 20)) * 20 + 'px',
        left: (Math.round(left / 20)) * 20 + 'px'
      }
      this.addNode(temp)

      this.draggingItem = null
    },
    addLine(line) {
      let from = line.source.id
      let to = line.target.id
      const lineData = {
        from: from,
        to: to,
        label: '连线名称',
        id: generateId(8),
        Remark: ''
      }
      this.data.lineList.push(lineData)
      return lineData
    },
    confirmDelLine(line) {
      this.$confirm('是否确认删除该连线?', '删除连线', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.jsPlumb.deleteConnection(line)
        })
        .catch(err => { })
    },
    deleLine(line) {
      this.data.lineList.forEach((item, index) => {
        if (item.from === line.sourceId && item.to === line.targetId) {
          this.data.lineList.splice(index, 1)
        }
      })
    },
    // dragover默认事件就是不触发drag事件，取消默认事件后，才会触发drag事件
    allowDrop(event) {
      event.preventDefault()
    },
    getScale() {
      let scale1
      if (this.jsPlumb.pan) {
        const { scale } = this.jsPlumb.pan.getTransform()
        scale1 = scale
      } else {
        const matrix = window.getComputedStyle(this.jsPlumb.getContainer()).transform
        scale1 = matrix.split(', ')[3] * 1
      }
      this.jsPlumb.setZoom(scale1)
      return scale1
    },
    // 添加新的节点
    addNode(node) {
      let find
      if (node.nodeType === '1' || node.nodeType === '7') {
        find = this.data.nodeList.find(item => item.nodeType === node.nodeType)
      }

      //已存在数据源或目标源
      if (find) {
        this.$message.warning('添加失败,该类型节点唯一')
        return false
      }

      this.data.nodeList.push(node)
      this.addNodeCallback && this.addNodeCallback(node)

      this.$nextTick(() => {
        this.configNode(node)
      })
    },
    setNodeName(nodeId, name) {
      this.data.nodeList.forEach((v) => {
        if (v.id === nodeId) {
          v.nodeName = name
        }
      })
    },
    //删除节点
    async deleteNode(node) {
      if (!this.editable) {
        return false
      }
      let curIdx = this.getCurIdx(node.id)
      let cols = []

      // 当前删除节点在连线中
      if (curIdx !== -1) {
        cols = this.getColumnsByNode(this.sortedNodes[curIdx])
      }

      try {
        if (cols?.length > 0 && ['1', '5', '6', '8', '9'].includes(node.nodeType) && this.sortedNodes.includes(node)) {
          await this.$confirm('此节点存在增量字段：' + cols.map(item => item.column).join(', ') + '，删除将清空后续流程的映射字段，是否继续？', '提示')
          this.snapshot = JSON.parse(JSON.stringify(this.sortedNodes))
        } else {
          await this.$confirm('是否删除此节点及配置信息？', '提示')
        }
      } catch (e) {
        return
      }

      return this.data.nodeList.some((v, index) => {
        if (v.id === node.id) {
          this.data.nodeList.splice(index, 1)
          this.jsPlumb.remove(v.id)

          this.deleteNodeCallback && this.deleteNodeCallback(node)

          // 清空后续节点中的当前删除节点增量column
          if (this.snapshot) {
            this.removeColumns(this.snapshot.slice(curIdx + 1), cols)
          }
          return true
        } else {
          return false
        }
      })
    },

    //更改连线状态
    changeLineState(nodeId, val) {
      let lines = this.jsPlumb.getAllConnections()
      lines.forEach(line => {
        if (line.targetId === nodeId || line.sourceId === nodeId) {
          if (val) {
            line.canvas.classList.add('active')
          } else {
            line.canvas.classList.remove('active')
          }
        }
      })
    },

    //初始化节点位置  （以便对齐,居中）
    fixNodesPosition() {
      if (this.data.nodeList && this.$refs.flowWrap) {
        const nodeWidth = 120
        const nodeHeight = 40
        let wrapInfo = this.$refs.flowWrap.getBoundingClientRect()
        let maxLeft = 0, minLeft = wrapInfo.width, maxTop = 0, minTop = wrapInfo.height
        let nodePoint = {
          left: 0,
          right: 0,
          top: 0,
          bottom: 0
        }
        let fixTop = 0, fixLeft = 0
        this.data.nodeList.forEach(el => {
          let top = Number(el.top.substring(0, el.top.length - 2))
          let left = Number(el.left.substring(0, el.left.length - 2))
          maxLeft = left > maxLeft ? left : maxLeft
          minLeft = left < minLeft ? left : minLeft
          maxTop = top > maxTop ? top : maxTop
          minTop = top < minTop ? top : minTop
        })
        nodePoint.left = minLeft
        nodePoint.right = wrapInfo.width - maxLeft - nodeWidth
        nodePoint.top = minTop
        nodePoint.bottom = wrapInfo.height - maxTop - nodeHeight

        fixTop = nodePoint.top !== nodePoint.bottom ? (nodePoint.bottom - nodePoint.top) / 2 : 0
        fixLeft = nodePoint.left !== nodePoint.right ? (nodePoint.right - nodePoint.left) / 2 : 0

        this.data.nodeList.map(el => {
          let top = Number(el.top.substring(0, el.top.length - 2)) + fixTop
          let left = Number(el.left.substring(0, el.left.length - 2)) + fixLeft
          el.top = (Math.round(top / 20)) * 20 + 'px'
          el.left = (Math.round(left / 20)) * 20 + 'px'
        })
      }
    },
    selectNode(item) {
      const map = {
        // 数据源
        1: 'sourceDsSetting',
        // 数据预处理组件
        2: 'filterRow',
        3: 'filterEmpty',
        4: 'filterReplaceVal',
        5: 'filterDerivedCol',
        8: 'convertFormat',
        9: 'dataSplit',
        10: 'exceptionHandle',
        11: 'dataFilter',
        12: 'dataDesensitize',
        // 数据治理组件-事务组件
        6: 'affairModule',
        // 目标源
        7: 'targetDsSetting',
      }

      this.currentNode = item
      console.log('点击节点', this.currentNode)
      if (item.nodeType !== '6' || (item.nodeType === '6' && item.modelType === '1')) {
        this.currentTemp = map[item.nodeType]
      } else {
        this.currentTemp = ''
      }
      if (!this.currentTemp) return
      this.flowJobAddVisible = true
    },
    selectItem(item) {
      console.log('selectItem', item)
      this.currentNode = item
      this.previewTableIndex = 1
      this.getFlowNodeData()
    },
  },
}