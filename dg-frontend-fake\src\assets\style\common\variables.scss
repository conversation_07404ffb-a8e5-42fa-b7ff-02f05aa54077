// 主题色 - 基于顶部导航栏渐变色的中间色
$themeColor: #6E65C6; // 渐变中间色
$themeColor-dark: #5A4FA3; //focus - 深色版本
$themeColor-light: #8A7FE1; //hover - 亮色版本

// 顶部导航栏渐变主题色
$primary-color-start: #667eea;
$primary-color-end: #764ba2;
$primary-gradient: linear-gradient(135deg, $primary-color-start 0%, $primary-color-end 100%);

// 背景颜色
$bgColor: #fff;
$bgColor-dark: #F2F4F7;

// 边框颜色
$borderColor: #E8E8E8;

// 字体颜色
$txtColor: #262626;
$txtColor-light: #868686;
$txtColor-slight: #c5c5c5;
$txtColor-reverse: #fff;

// 提示颜色
$color-success: #52C41A;
$color-warning: #FA9904;
$color-danger: #F03838;
$color-info: #909399;
$color-delay: #22B8CF;
