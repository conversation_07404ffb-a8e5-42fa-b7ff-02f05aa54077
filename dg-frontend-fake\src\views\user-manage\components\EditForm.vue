<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="100px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="用户名称"
            prop="USERNAME">
            <el-input
              v-model="formData.USERNAME"
              v-trim
              clearable
              placeholder="请输入用户名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="编号"
            prop="USER_ID">
            <el-input
              v-model="formData.USER_ID"
              v-trim
              :readonly="!!data"
              clearable
              placeholder="请输入编号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="登录账户"
            prop="USER_ACCT">
            <el-input
              v-model="formData.USER_ACCT"
              v-trim
              clearable
              placeholder="请输入登录账户"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="用户密码"
            prop="USER_PWD">
            <el-input
              v-model="formData.USER_PWD"
              v-trim
              clearable
              placeholder="请输入用户密码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="角色"
            prop="roleIds">
            <multi-switch
              v-model="formData.roleIds"
              :options="roleDict"
              multiple></multi-switch>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { getDetail, addUser, updateUser } from '@/api/user-manage'
import { getRoleDict } from '@/api/role'

export default {
  components: {
    'multi-switch': () => import('@/components/MultiSwitch'),
  },
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      formData: {
        USERNAME: null,
        USER_ID: null,
        USER_ACCT: null,
        USER_PWD: null,
        roleIds: [],
      },
      rules: {
        USERNAME: [{ required: true, message: '请输入用户名称', trigger: 'blur' }],
        // USER_ID: [{ required: true, message: '请输入编号', trigger: 'blur' }],
        USER_ACCT: [{ required: true, message: '请输入登录账户', trigger: 'blur' }],
        USER_PWD: [
          { required: true, message: '请输入用户密码', trigger: 'blur' },
          // { min: 8, message: '用户密码长度最少8个字符', trigger: 'blur' },
          // { pattern: /((\\d)|([a-z])|([A-Z]))+/, message: '用户密码必须包含数字和字母', trigger: 'blur' },
          // { pattern: /^(?![a-zA-z]+$)(?!\\d+$)(?![!@#$%^&*]+$)[a-zA-Z\\d!@#$%^&*]+$/, message: '用户密码必须包含数字、字母和特殊字符', trigger: 'blur' },
        ],
      },
      roleDict: [],
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data?.USER_ID) {
        this.getDetail(this.data.USER_ID)
      } else {
        this.formData = {
          USERNAME: null,
          USER_ID: null,
          USER_ACCT: null,
          USER_PWD: null,
          roleIds: [],
        }
      }
      this.getRoleDict()
    },
    async getDetail(id) {
      const [err, res] = await getDetail(id)
      if (res) {
        const data = res.data
        this.$set(this.formData, 'USERNAME', data?.USERNAME)
        this.$set(this.formData, 'USER_ID', data?.USER_ID)
        this.$set(this.formData, 'USER_ACCT', data?.USER_ACCT)
        this.$set(this.formData, 'USER_PWD', '******')
        this.$set(
          this.formData,
          'roleIds',
          res?.roles.map(({ ROLE_ID }) => ROLE_ID)
        )
      }
    },
    async getRoleDict() {
      const [err, res] = await getRoleDict()
      if (res) {
        this.roleDict = res.data
      }
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        let submitAction = addUser
        if (this.data) {
          submitAction = updateUser
        }

        let pwd = /^(\*)+$/.test(this.formData.USER_PWD) ? '' : this.formData.USER_PWD

        const payload = {
          'user.USERNAME': this.formData.USERNAME,
          'userLogin.USER_PWD': pwd,
          'userLogin.USER_ACCT': this.formData.USER_ACCT,
          'user.USER_ID': this.formData.USER_ID,
          roleIds: this.formData.roleIds.join(','),
        }

        const [err, res] = await submitAction(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
