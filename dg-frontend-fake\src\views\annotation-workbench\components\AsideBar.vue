<template>
  <div class="aside-bar">
    <div class="y-bar">
      <h2 class="y-title">记录列表</h2>
    </div>
    <div
      class="y-bar"
      style="padding-top: 0">
      <el-input
        v-model="keyword"
        v-trim
        placeholder="请输入关键字"
        size="small"
        clearable
        suffix-icon="el-icon-search"></el-input>
    </div>
    <class-menu
      v-loading="loading"
      :list="filteredList"
      :active-menu="activeMenu?.RECORD_ID"
      id-prop="RECORD_ID"
      gap="8px"
      @set-current="setCurrent($event)">
      <template v-slot="{ item }">
        <div class="menu-item">
          <p>
            <span>{{ item.TRAIN_NAME }}</span>
            <el-tag
              :type="getStateTag(item.TRAIN_STATE, 'type')"
              style="float: right; height: 26px"
              >{{ getStateTag(item.TRAIN_STATE, 'txt') }}</el-tag
            >
          </p>
          <p>
            <span>记录ID：</span><span>{{ item.RECORD_ID }}</span>
          </p>
          <p>
            <span>数据行ID：</span><span>{{ item.DATA_ROW_ID }}</span>
          </p>
        </div>
      </template>
    </class-menu>
  </div>
</template>

<script>
import { getList } from '@/api/annotation-workbench'
import ClassMenu from '@/components/ClassMenu'

export default {
  components: {
    ClassMenu,
  },
  data() {
    return {
      loading: false,
      activeMenu: null,
      keyword: '',
      menuList: [],
    }
  },
  computed: {
    filteredList() {
      if (this.keyword.trim()) {
        return this.menuList.filter((i) => i.TRAIN_NAME.includes(this.keyword.trim()))
      }
      return this.menuList
    },
  },
  watch: {},
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true

      const payload = {
        pageSize: 999,
        pageIndex: 1,
        pageType: 3,
      }
      const [err, res] = await getList(payload)
      if (res && res?.data) {
        this.menuList = (res.data || []).map((i, index) => ({ ...i, index }))
        this.setCurrent(this.menuList[0])
      }

      this.loading = false
    },
    setCurrent(item) {
      this.activeMenu = item
      this.$emit('set-current', this.activeMenu)
    },
    switchTo(index) {
      let find = this.menuList.find((i) => i.index === index)
      find && this.setCurrent(find)
    },
    getStateTag(state, flag) {
      const map = {
        txt: {
          1: '已完成',
          2: '待执行',
          3: '执行中',
          4: '已暂停',
        },
        type: {
          1: 'success',
          2: 'delay',
          3: 'primary',
          4: 'info',
        },
      }
      return map[flag][state]
    },
  },
}
</script>

<style lang="scss" scoped>
.aside-bar {
  @include flex-col;
  flex-shrink: 0;
  width: 288px;
  height: 100%;

  .y-title {
    line-height: 24px;
  }

  > .y-bar {
    padding: 16px 24px;
    padding-bottom: 8px;

    .svg-icon {
      font-size: 16px;
      color: $txtColor-light;
      cursor: pointer;

      &:hover {
        color: $themeColor;
      }
    }
  }

  .class-menu ::v-deep {
    .class-menu_item {
      border: 1px solid $borderColor;

      &.is-active,
      &:hover {
        border-color: $themeColor;
      }

      .menu-item {
        width: 100%;

        p + p {
          margin-top: 8px;
        }
      }
    }
  }
}
</style>
