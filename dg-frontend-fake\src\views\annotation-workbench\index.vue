<template>
  <base-card
    id="annotation-workbench"
    class="y-page">
    <aside-bar
      ref="asideBar"
      @set-current="setCurrent($event)"></aside-bar>
    <base-card>
      <div class="y-container--tight">
        <DetailTable
          ref="detailTable"
          :data="activeMenu"></DetailTable>
      </div>
      <div
        v-if="activeMenu"
        class="footer y-bar">
        <div style="flex: 1">
          <el-button
            type="primary"
            plain
            size="small"
            :disabled="$refs.asideBar?.menuList?.length === 0 || activeMenu?.index === 0"
            @click.native="switchMenu(-1)"
            >上一条</el-button
          >
          <el-button
            type="primary"
            plain
            size="small"
            :disabled="$refs.asideBar?.menuList?.length === 0 || activeMenu?.index === $refs.asideBar?.menuList?.length - 1"
            @click.native="switchMenu(1)"
            >下一条</el-button
          >
        </div>
        <el-button
          type="primary"
          plain
          size="small"
          :disabled="!activeMenu"
          @click.native="$refs.detailTable && $refs.detailTable.initPage()"
          >重置</el-button
        >
        <el-button
          v-debounce="() => $refs.detailTable && $refs.detailTable.submitForm()"
          type="primary"
          size="small"
          >确认</el-button
        >
      </div>
    </base-card>
  </base-card>
</template>

<script>
import AsideBar from './components/AsideBar.vue'
import DetailTable from '@/views/training-mark/components/DetailTable.vue'

export default {
  name: 'AnnotationWorkbench',
  components: {
    AsideBar,
    DetailTable,
  },
  props: {},
  data() {
    return {
      activeMenu: null,
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    setCurrent(menu) {
      this.activeMenu = menu && { ...menu, etlId: menu.ETL_ID }
    },
    switchMenu(n = 1) {
      this.$refs.asideBar?.switchTo?.(this.activeMenu?.index + n)
    },
  },
}
</script>

<style lang="scss">
#annotation-workbench {
  @include flex-row;
  background-color: transparent;

  > .aside-bar {
    margin-right: 16px;
    border-radius: 4px;
    background-color: $bgColor;
  }

  > .base-card {
    @import '@/assets/style/modules/drawer.scss';
    width: calc(100% - 304px);
    min-width: 685px;
    height: 100%; //to remove
  }
}
</style>
