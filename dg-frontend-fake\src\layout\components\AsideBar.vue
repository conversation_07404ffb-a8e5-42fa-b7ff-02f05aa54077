<template>
  <div class="y-layout_nav flex-col">
    <el-scrollbar>
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :collapse-transition="true"
        :router="false"
        @select="handleMenuSelect"
        @open="handleOpenMenuItem"
      >
        <MenuItem
          v-for="route in $store.getters.permissionRoutes"
          :item="route"
          :key="route.path"
          :base-path="route.path"
          :default-icon="true"
          :is-nest="true"></MenuItem>
      </el-menu>
    </el-scrollbar>
    <div class="bottom-bar">
      <svg-icon
        class="btn-collapse"
        :icon="isCollapse ? 'unfold' : 'fold'"
        @click="isCollapse = !isCollapse"></svg-icon>
    </div>
  </div>
</template>

<script>
import { debounce } from '@/utils'
import path from 'path'
import { isExternal } from '@/utils/validate'

export default {
  components: {
    MenuItem: () => import('./MenuItem.vue'),
  },
  data() {
    return {
      isCollapse: false,
    }
  },
  computed: {
    activeMenu() {
      const { meta, path } = this.$route
      if (meta?.activeMenu) {
        return meta.activeMenu
      } else {
        return path
      }
    },
  },
  created() {
    // this.resizeHandler = debounce(() => this.collapseHandler(), 500)
    window.addEventListener('resize', this.resizeHandler)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeHandler)
  },
  mounted() {
    const isMobile = this.isMobile()
    if (isMobile) {
      this.isCollapse = true
    }
  },
  methods: {
    isMobile() {
      const rect = document.body.getBoundingClientRect()
      return rect.width - 1 < 992
    },
    resizeHandler() {
      if (!document.hidden) {
        const isMobile = this.isMobile()
        if (isMobile) {
          this.isCollapse = true
        }
      }
    },
    handleOpenMenuItem(index) {
      console.log('index: ', index);
    },
    handleMenuSelect(index) {
      // 检查是否是当前路由，避免重复导航
      if (this.$route.path === index) {
        return
      }
      
      // 只有在不是外部链接的情况下才进行路由跳转
      const route = this.findRouteByPath(index)
      if (!route || !route.meta?.blanck) {
        this.$router.push(index).catch(err => {
          // 忽略导航重复错误
          if (err.name !== 'NavigationDuplicated') {
            console.error('路由导航错误:', err)
          }
        })
      }
    },
    resolvePath(routePath, basePath = '/') {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(basePath)) {
        return basePath
      }
      return path.resolve(basePath, routePath)
    },
    findRouteByPath(targetPath) {
      // 递归查找路由
      const findRoute = (routes, basePath = '/') => {
        for (const route of routes) {
          const fullPath = this.resolvePath(route.path, basePath)
          if (fullPath === targetPath) {
            return route
          }
          if (route.children) {
            const found = findRoute(route.children, fullPath)
            if (found) return found
          }
        }
        return null
      }
      return findRoute(this.$store.getters.permissionRoutes)
    }
  },
}
</script>

<style lang="scss">
.y-layout_nav {
  flex: 0;
  height: calc(100vh - 64px);
  background-color: $bgColor;
  box-shadow: 0px 3px 16px 0px rgba(38, 38, 38, 0.12);

  .el-scrollbar {
    flex: 1;
    overflow: hidden;

    .el-scrollbar__wrap {
      height: calc(100% + 16px);
    }
  }

  .el-menu {
    border: none;

    &.el-menu--collapse {
      .el-submenu__title {
        text-align: center;
      }
    }

    .el-menu-item,
    .el-submenu__title {
      font-size: 14px;
      line-height: 56px;
    }

    .sub-el-icon,
    .svg-icon {
      margin-right: 8px;
      text-align: center;
      font-size: 16px;
      vertical-align: -0.12em;
    }

    &.el-menu--collapse {
      .sub-el-icon,
      .svg-icon {
        margin-right: 0;
      }

      .el-menu-item,
      .el-submenu__title {
        text-align: center;
      }
    }

    .nest-menu {
      width: 100%;

      .el-menu-item {
        font-size: 14px;
        line-height: 48px;
      }
    }
  }

  .el-menu:not(.el-menu--collapse) {
    width: 264px;

    .el-menu:not(.el-menu--collapse) {
      width: 100%;
    }
  }

  .bottom-bar {
    padding: 0 22px;
    height: 40px;
    border-top: 1px solid $borderColor;

    .btn-collapse {
      height: 100%;
      vertical-align: middle;
      font-size: 20px;
      color: #868686;
      cursor: pointer;

      &:hover {
        color: $themeColor;
      }
    }
  }
}

.el-menu--popup {
  .svg-icon {
    display: none;
  }
}
</style>
