<template>
  <base-card
    id="data-training"
    class="y-page">
    <div class="y-bar">
      <h2 class="y-title">标注任务</h2>
      <span>执行状态</span>
      <el-select
        v-model="formData.trainState"
        placeholder="请选择执行状态"
        size="small"
        clearable
        style="width: 160px">
        <el-option
          label="已完成"
          value="1"></el-option>
        <el-option
          label="待执行"
          value="2"></el-option>
        <el-option
          label="执行中"
          value="3"></el-option>
      </el-select>
      <span>关键字</span>
      <el-input
        v-model="formData.key"
        v-trim
        size="small"
        style="width: 220px"
        clearable
        placeholder="请输入关键字进行搜索"></el-input>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
      <el-button
        class="mini"
        type="primary"
        @click.native="openEdit(null)">
        <svg-icon icon="add"></svg-icon>
        创建任务
      </el-button>
    </div>
    <empty-wrapper
      class="y-container--tight"
      :toggle="!trainingList || trainingList?.length === 0"
      v-loading="loading">
      <div class="training-wrapper y-card-wrapper y-container--tight no-padding">
        <training-card
          v-for="item in trainingList"
          :key="item.TRAIN_DATASET_ID"
          :data="item"
          @update-data="fetchData">
        </training-card>
      </div>
    </empty-wrapper>
    <el-drawer
      title="创建训练"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="1200">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getTrainingList } from '@/api/data-training'
import EditForm from './components/EditForm'
import TrainingCard from './components/TrainingCard'

export default {
  name: 'DataTraining',
  components: {
    TrainingCard,
    EditForm,
  },
  props: {},
  data() {
    return {
      loading: false,
      formData: {
        pageSize: 999,
        pageIndex: 1,
        pageType: 3,
        key: '',
        trainState: '',
      },
      trainingList: [],
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true
      const [err, res] = await getTrainingList(this.formData)
      if (res) {
        this.trainingList = res.data
      }
      this.loading = false
    },
    openEdit(data) {
      if (data) {
        this.editDrawerData = data
      } else {
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
  },
}
</script>

<style lang="scss">
#data-training {
  background-color: transparent;

  > .y-bar {
    justify-content: flex-start;
    background-color: $bgColor;
    border-radius: 4px;
  }

  .training-wrapper {
    margin-top: 16px;
    grid-auto-rows: 380px;
    grid-template-columns: repeat(auto-fill, minmax(408px, 1fr));
  }
}
</style>
