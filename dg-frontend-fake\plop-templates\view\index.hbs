{{#if template}}
<template>
  <base-card id="{{ kebabCase name }}" class="y-page"></base-card>
</template>
{{/if}}

{{#if script}}
<script>
export default {
  name: '{{ properCase name }}',
  components: {},
  props: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
}
</script>
{{/if}}

{{#if style}}
<style lang="scss">
#{{ kebabCase name }} { }
</style>
{{/if}}
