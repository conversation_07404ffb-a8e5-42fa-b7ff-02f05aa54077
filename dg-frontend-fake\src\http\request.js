import service from './axios.js'

export const get = (url, params, clearFn, headers, options) => {
  return new Promise((resolve) => {
    service({
      method: 'get',
      url,
      params,
      headers,
      ...options
    })
      .then((result) => {
        let res
        if (clearFn && typeof clearFn === 'function') {
          res = clearFn(result)
        } else {
          res = result
        }
        resolve([null, res])
      })
      .catch((err) => {
        resolve([err, undefined])
      })
  })
}

export const post = async (url, data, params, headers, options) => {
  return new Promise((resolve) => {
    service({
      method: 'post',
      url,
      data,
      params,
      headers: headers || { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' },
      ...options
    })
      .then((result) => {
        resolve([null, result])
      })
      .catch((err) => {
        resolve([err, undefined])
      })
  })
}