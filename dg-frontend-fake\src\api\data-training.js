import { get, post } from '@/http/request'
import { dataTraining, etl } from '@/api/PATH'

// 数据训练
export function getTrainingList(formData) {
  return post(dataTraining.trainingList, formData)
}

export function train(formData) {
  return post(dataTraining.train, { data: JSON.stringify(formData) })
}

export function pauseTrain(id) {
  return post('/dg-portal/servlet/dataTraining?action=trainPause', { data: { trainDatasetId: id } }, null, { 'Content-Type': 'application/json;charset=utf-8' })
}

export function completeTrain(id) {
  return post('/dg-portal/servlet/dataTraining?action=trainSucc', { data: { trainDatasetId: id } }, null, { 'Content-Type': 'application/json;charset=utf-8' })
}

export function addTraining(formData) {
  return post(dataTraining.addTraining, { data: JSON.stringify(formData) })
}

export function getEtlList() {
  return post(dataTraining.etlList)
}

export function getQueryList(formData) {
  return post(dataTraining.queryList, formData)
}

export function getColumnList(formData) {
  return post(dataTraining.columnList, formData)
}

export function getDataList(formData) {
  return post(dataTraining.dataList, formData)
}

// 标注
export function getMarkListHeaders(id) {
  return post(dataTraining.markListHeaders, { etlId: id })
}

export function getMarkList(formData) {
  return post(dataTraining.markList, formData)
}

export function getMarkDetail(formData) {
  return post(dataTraining.markDetail, formData)
}

export function mark(formData) {
  return post(dataTraining.mark, { data: JSON.stringify(formData) })
}

// 历史
export function getHistoryList(formData) {
  return post(dataTraining.historyList, formData)
}