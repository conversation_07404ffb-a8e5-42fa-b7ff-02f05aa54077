import { get, post } from '@/http/request'
import { etlLog, etl } from '@/api/PATH'

// etl日志
export function getTaskList(formData) {
  return post(etlLog.taskList, formData)
}

export function getExecuteList(formData) {
  return post(etlLog.executeList, formData)
}

// 旧导出接口
// export function exportList(formData) {
//   return get(etlLog.export, formData, null, null, { responseType: 'blob' })
// }

export function exportList(formData) {
  return post(etlLog.export, formData)
}

export function getEtlDict(formData) {
  return post(etlLog.dict, formData)
}

export function getDetailHead(id) {
  return post(etlLog.detailHead, { etlId: id })
}

export function getDetail(id) {
  return post(etlLog.detail, { dataRowId: id })
}