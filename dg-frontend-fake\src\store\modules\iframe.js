export default {
  namespaced: true,
  state: () => ({
    // 当前是否显示iframe
    showIframe: false,
    // 当前iframe的URL
    currentUrl: '',
    // iframe加载状态
    loading: false,
    // 当前iframe对应的路由信息
    currentIframeRoute: null
  }),
  mutations: {
    SET_SHOW_IFRAME(state, show) {
      state.showIframe = show
    },
    SET_CURRENT_URL(state, url) {
      state.currentUrl = url
    },
    SET_LOADING(state, loading) {
      state.loading = loading
    },
    SET_CURRENT_IFRAME_ROUTE(state, route) {
      state.currentIframeRoute = route
    }
  },
  actions: {
    // 显示iframe
    showIframe({ commit }, { url, route }) {
      commit('SET_LOADING', true)
      commit('SET_CURRENT_URL', url)
      commit('SET_CURRENT_IFRAME_ROUTE', route)
      commit('SET_SHOW_IFRAME', true)
    },
    // 隐藏iframe
    hideIframe({ commit }) {
      commit('SET_SHOW_IFRAME', false)
      commit('SET_CURRENT_URL', '')
      commit('SET_CURRENT_IFRAME_ROUTE', null)
      commit('SET_LOADING', false)
    },
    // iframe加载完成
    iframeLoaded({ commit }) {
      commit('SET_LOADING', false)
    }
  },
  getters: {
    showIframe: state => state.showIframe,
    currentUrl: state => state.currentUrl,
    loading: state => state.loading,
    currentIframeRoute: state => state.currentIframeRoute
  }
}
