import { get, post } from '@/http/request'
import { crontab } from '@/api/PATH'

// 定时任务
export function getList(formData) {
  return post(crontab.list, formData)
}

export function getDetail(id) {
  return post(crontab.detail, { id })
}

export function addTask(formData) {
  return post(crontab.add, { data: JSON.stringify(formData) })
}

export function updateTask(formData) {
  return post(crontab.update, { data: JSON.stringify(formData) })
}

export function deleteTask(formData) {
  return post(crontab.delete, { data: JSON.stringify(formData) })
}

export function executeTask(id) {
  return post(crontab.execute, { data: JSON.stringify({ id }) })
}