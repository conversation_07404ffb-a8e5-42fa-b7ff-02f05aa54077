<template>
  <div class="accredit-form y-container no-padding">
    <div class="y-container--tight">
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <el-table-column
          type="index"
          label="序号"
          width="100">
        </el-table-column>
        <el-table-column
          label="用户名称"
          prop="USERNAME">
        </el-table-column>
        <el-table-column
          label="用户编号"
          prop="USER_ID">
        </el-table-column>
        <el-table-column
          label="角色"
          prop="ROLES">
          <template v-slot="scope">
            <template v-if="scope.row.ROLES.trim()">
              <el-tag
                v-for="role in scope.row.ROLES.split(',')"
                size="small"
                type="primary"
                >{{ role }}</el-tag
              >
            </template>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="100">
          <template slot-scope="scope">
            <el-link
              v-debounce="{ evtHandler: () => accreditUser(scope.row) }"
              :key="scope.row.USER_ID"
              type="primary"
              :underline="false"
              >授权</el-link
            >
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
  </div>
</template>

<script>
import { getAccreditableList, accreditUser } from '@/api/role'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      tableLoading: false,
      total: 0,
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        roleId: '',
        userAcct: '',
      },
      header: [],
      tableData: [],
    }
  },
  computed: {},
  watch: {
    data: {
      handler(data) {
        if (data?.roleId) {
          this.initPage()
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    initPage() {
      if (this.data?.roleId) {
        this.$set(this.formData, 'roleId', this.data?.roleId)
      } else {
        this.formData = {
          pageIndex: 1,
          pageSize: 15,
          pageType: 3,
          roleId: '',
          userAcct: '',
        }
      }
      this.fetchData()
    },
    async fetchData() {
      this.tableLoading = true
      const [err, res] = await getAccreditableList(this.formData)
      if (res) {
        this.tableData = res.data
        this.total = res.totalRow
      }
      this.tableLoading = false
    },
    async accreditUser(data) {
      console.log(data)
      const payload = {
        roleId: this.data.roleId,
        userId: data.USER_ID,
      }
      const [err, res] = await accreditUser(payload)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.accredit-form {
  > .y-container--tight {
    padding-bottom: 0;
    border-bottom: 1px solid $borderColor;

    .el-table {
      .el-tag {
        + .el-tag {
          margin-left: 4px;
        }
      }

      &::before {
        content: none;
      }
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }
}
</style>
