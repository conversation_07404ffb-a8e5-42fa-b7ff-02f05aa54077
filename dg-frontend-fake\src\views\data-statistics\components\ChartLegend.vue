<template>
  <div
    class="chart-legend no-padding"
    :class="[scroll ? 'y-container--tight' : 'y-container']">
    <div
      class="chart-legend_item"
      v-for="(item, idx) in showList">
      <div
        :class="['icon', item.disactive ? 'is-disactive' : '']"
        :style="{ backgroundColor: getColor(idx, 'show') }"
        @click="disactiveItem(idx, 'show')"></div>
      <p class="label">{{ item.name }}</p>
      <p class="value">{{ item.value }}</p>
    </div>
    <el-popover
      placement="top"
      trigger="hover"
      popper-class="chart-legend">
      <div
        class="chart-legend_item"
        v-for="(item, idx) in overflowList">
        <div
          :class="['icon', item.disactive ? 'is-disactive' : '']"
          :style="{ backgroundColor: getColor(idx, 'overflow') }"
          @click="disactiveItem(idx, 'overflow')"></div>
        <p class="label">{{ item.name }}</p>
        <p class="value">{{ item.value }}</p>
      </div>
      <p
        v-show="overflowList?.length > 0"
        class="chart-legend_overflow"
        slot="reference">
        ...
      </p>
    </el-popover>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    max: {
      type: Number,
      default: 5,
    },
    color: {
      type: Array,
      default: () => ['#fff'],
    },
    scroll: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
  computed: {
    showList() {
      return this.data.slice(0, this.max)
    },
    overflowList() {
      return this.data.slice(this.max)
    },
  },
  methods: {
    disactiveItem(idx, flag) {
      if (flag === 'overflow') {
        idx = idx + this.max
      }
      this.$emit('disactive-item', idx)
    },
    getColor(idx, flag) {
      if (flag === 'show') {
        idx = idx % this.color.length
      } else if (flag === 'overflow') {
        idx = (idx + this.max) % this.color.length
      }
      return this.color[idx]
    },
  },
}
</script>

<style lang="scss" scoped>
.chart-legend {
  @include full;
  overflow: hidden;

  > * {
    width: 100%;
    min-height: 22px;
  }

  .chart-legend_item {
    @include flex-row;
    width: 100%;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    color: $txtColor;

    .icon {
      margin-right: 4px;
      width: 14px;
      height: 14px;
      border-radius: 2px;
      cursor: pointer;

      &.is-disactive {
        background-color: $txtColor-light !important;
      }
    }

    .label {
      @include text-overflow(1);
      flex: 1;
    }

    .value {
      margin-left: 8px;
      font-weight: 700;
    }

    + .chart-legend_item {
      margin-top: 4px;
    }
  }

  .el-popover__reference-wrapper {
    width: max-content;
  }

  .chart-legend_overflow {
    font-size: 14px;
    font-weight: 700;
    color: $txtColor;
    cursor: pointer;
  }
}
</style>
