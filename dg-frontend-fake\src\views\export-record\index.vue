<template>
  <base-card
    id="export-record"
    class="y-page">
    <div class="y-bar header">
      <h2 class="y-title">导出记录</h2>
    </div>
    <div class="y-bar search-bar">
      <span>文件名称</span>
      <el-input
        v-model="formData.fileName"
        v-trim
        placeholder="请输入文件名称"
        size="small"
        clearable
        style="width: 220px"></el-input>
      <span>导出状态</span>
      <el-select
        v-model="formData.status"
        placeholder="请选择导出状态"
        size="small"
        clearable
        style="width: 220px">
        <el-option
          v-for="(value, key) in statusList"
          :label="value"
          :value="key"></el-option>
      </el-select>
      <span>导出提交时间</span>
      <el-date-picker
        v-model="dateRange"
        type="datetimerange"
        size="small"
        value-format="yyyy-MM-dd HH:mm:ss"
        @change="dateChange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :default-time="['00:00:00', '23:59:59']"
        style="width: 340px">
      </el-date-picker>
      <el-button
        class="mini"
        type="primary"
        size="small"
        plain
        @click.native="resetSearch">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
    </div>
    <div class="y-container--tight">
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <el-table-column
          type="index"
          label="序号"
          width="50">
        </el-table-column>
        <el-table-column
          label="文件名称"
          prop="FILE_NAME"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="导出提交时间"
          prop="START_TIME"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="导出完成时间"
          prop="END_TIME"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="导出状态"
          prop="STATUS"
          show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag :type="getStatus(scope.row.STATUS, 'type')">{{ getStatus(scope.row.STATUS, 'label') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="120">
          <template slot-scope="scope">
            <template v-if="scope.row.STATUS === '1'">
              <el-link
                type="primary"
                :underline="false"
                @click.native="downloadExport(scope.row)"
                >下载</el-link
              >
              <el-link
                type="danger"
                :underline="false"
                @click.native="deleteExport(scope.row)"
                >删除</el-link
              >
            </template>
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
  </base-card>
</template>

<script>
import { getList, downloadExport, deleteExport } from '@/api/export-record'
import { downloadFile } from '@/utils'

export default {
  name: 'ExportRecord',
  components: {},
  props: {},
  data() {
    return {
      tableData: [],
      tableLoading: false,
      total: 0,
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        fileName: '',
        startTime: '',
        endTime: '',
        status: '',
      },
      dateRange: [],
      statusList: {
        0: '未完成',
        1: '已完成',
        2: '失败',
      },
    }
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.tableLoading = true
      const [err, res] = await getList(this.formData)
      if (res) {
        this.tableData = res.data
        this.total = res.totalRow
      }
      this.tableLoading = false
    },
    async downloadExport(row) {
      downloadFile('/dg-portal/servlet/etlTask?action=DownloadEtlTaskExecute&id=' + row.ID)
      // const [err, res] = await downloadZip(row.ID)

      // if (!err && res) {
      //   const url = window.URL.createObjectURL(res)
      //   downloadFile(url, false, () => {
      //     this.$message({
      //       message: '下载成功',
      //       type: 'success',
      //       duration: 800,
      //       onClose: () => {},
      //     })
      //   })
      // }
    },
    async deleteExport(row) {
      try {
        await this.$confirm('此操作将永久删除选中记录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const [err, res] = await deleteExport(row.ID)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    dateChange() {
      if (!this.dateRange || this.dateRange?.length === 0) {
        this.formData.startTime = ''
        this.formData.endTime = ''
        return false
      }
      const [begin, end] = this.dateRange
      this.formData.startTime = begin
      this.formData.endTime = end
    },
    getStatus(status, flag) {
      const map = {
        type: {
          0: 'primary',
          1: 'success',
          2: 'danger',
        },
        label: this.statusList,
      }

      return map[flag][status]
    },
    resetSearch() {
      this.dateRange = []
      this.formData = {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        fileName: '',
        startTime: '',
        endTime: '',
        status: '',
      }
      this.fetchData()
    },
  },
}
</script>

<style lang="scss">
#export-record {
  > .header {
    border-bottom: 1px solid $borderColor;
  }

  > .search-bar {
    padding-top: 16px;
    justify-content: flex-start;
  }

  > .y-container--tight {
    padding-bottom: 0;
    border-bottom: 1px solid $borderColor;

    .el-table::before {
      content: none;
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }
}
</style>
