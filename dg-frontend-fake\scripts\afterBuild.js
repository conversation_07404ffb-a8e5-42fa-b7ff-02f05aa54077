const fs = require('node:fs/promises')

const distMap = {
  portal: {
    src: './build/dist',
    dest: '../dg-portal/src/main/webapp/pages/dist',
  },
  login: {
    src: './build/login',
    dest: '../dg-portal/src/main/webapp/login',
  },
  model: {
    src: './build/model',
    dest: '../dg-portal/src/main/webapp/model',
  },
}

let src, dest
start()

async function start() {
  const keys = Object.keys(distMap).map(i => '--' + i)
  let param = process.argv[2]
  if (param && keys.includes(param)) {
    let key = param.slice(2)
    key && ({ src, dest } = distMap[key])
    
    try {
      let res = await fs.access('../dg-portal/src/main/webapp/pages', fs.constants.W_OK)
      updateDist(process.argv[2])
      console.log('dist已更新至：' + dest + '\n可以直接提交！')
    } catch (err) {
      console.log(err)
    }
  } else {
    console.log('no target')
  }
}

async function updateDist() {
  try {
    // TODO: 如果没有dir创建一个
    await deleteFormerDist()
    await copyNewDist()
  } catch (err) {
    console.log(err)
  }
}

function deleteFormerDist() {
  return fs.rm(dest, { recursive: true })
}

function copyNewDist() {
  return fs.cp(src, dest, { recursive: true })
}