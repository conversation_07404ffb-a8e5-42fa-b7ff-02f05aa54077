<template>
  <el-drawer
    :size="1000"
    v-bind="$attrs"
    v-on="$listeners"
    title="关系目标源节点配置">
    <div class="y-container no-padding">
      <div class="y-container--tight no-padding">
        <el-form
          :disabled="!editable"
          ref="elForm"
          :model="formData"
          :rules="rules"
          class="y-container--tight"
          style="flex: 0 0 max-content; padding-top: 24px; padding-bottom: 0"
          size="medium"
          label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item
                label="选择目标源"
                prop="dsId">
                <el-select
                  filterable
                  @change="onDsIdChange"
                  v-model="formData.dsId"
                  placeholder="请选择"
                  :style="{ width: '100%' }">
                  <el-option
                    v-for="(item, index) in dsList"
                    :key="index"
                    :label="item.dsName"
                    :value="item.dsId"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="选择表"
                prop="dataResourceObj">
                <el-select
                  filterable
                  @change="onDataResourceObjChange"
                  v-model="formData.dataResourceObj"
                  placeholder="请选择"
                  :style="{ width: '100%' }">
                  <el-option
                    v-for="(item, index) in tableList"
                    v-show="item.checkBox"
                    :key="item.tableName"
                    :label="item.tableName"
                    :value="item.tableName"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item
                label="数据写入模式"
                prop="writeMode">
                <el-radio
                  v-model="formData.writeMode"
                  label="0"
                  >追加</el-radio
                >
                <el-radio
                  v-model="formData.writeMode"
                  label="1"
                  >更新</el-radio
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-form
          :disabled="!editable"
          class="y-container--tight no-padding">
          <el-table
            ref="multipleTable"
            :data="formData.columns"
            style="width: 100%"
            :height="'100%'"
            @selection-change="handleSelectionChange">
            <el-table-column
              type="selection"
              label="映射"
              width="40">
              <template slot-scope="scope">
                <el-checkbox
                  v-model="scope.row.show"
                  :disabled="scope.row.disableCoumn"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column
              label="序号"
              type="index"
              width="50">
            </el-table-column>
            <el-table-column
              prop="column"
              label="字段名称"
              width="120"
              show-overflow-tooltip>
            </el-table-column>
            <el-table-column
              prop="columnType"
              label="字段类型"
              width="80"
              show-overflow-tooltip>
              <template slot-scope="scope">
                {{ VALUE_TYPES[scope.row.columnType] }}
              </template>
            </el-table-column>

            <el-table-column label="字段备注">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.columnLabel"
                  :value="scope.row.columnLabel"
                  :placeholder="scope.row.columnLabel"></el-input>
              </template>
            </el-table-column>

            <el-table-column label="映射字段">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.mapColumn"
                  placeholder="请选择"
                  clearable>
                  <el-option
                    v-for="(item, key) in currentCols"
                    :key="key"
                    :value="item.column">
                    {{ item.column }} ({{ item.columnLabel }})
                  </el-option>
                </el-select>
              </template>
            </el-table-column>

            <el-table-column
              prop="queryColumn"
              label="用于查询"
              width="80"
              show-overflow-tooltip>
              <template slot-scope="scope">
                <el-checkbox v-model="scope.row.queryColumn"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column
              label="重点标注"
              prop="markColumn"
              width="80"
              show-overflow-tooltip>
              <template slot-scope="scope">
                <el-checkbox v-model="scope.row.markColumn"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column
              v-if="formData.writeMode === '1'"
              label="更新依据列"
              prop="updateBasis"
              width="100"
              show-overflow-tooltip>
              <template slot-scope="scope">
                <el-radio
                  class="no-label"
                  :value="scope.row.updateBasis"
                  @change="handleUpdateBasisChange(scope.$index)"
                  :label="true"></el-radio>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </div>
      <div class="footer y-bar">
        <el-button
          type="primary"
          plain
          size="small"
          @click.native="close"
          >取消</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="handelConfirm"
          >保存</el-button
        >
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getTargetDsDict, getDsTagTableConfig, getSysDsTableList, getSysDsHeadConfig } from '@/api/etl-flow'
import { VALUE_TYPES } from '@/utils/constant'

export default {
  inheritAttrs: false,
  components: {},
  props: ['node', 'currentCols', 'data', 'editable'],
  data() {
    return {
      formData: {
        dsId: undefined,
        dsType: 1,
        dataResourceObj: undefined,
        sysDsName: undefined,
        writeMode: '0',
        columns: [],
      },
      rules: {},
      dsList: [], // 目标源字典
      tableList: [], // 表字典
      VALUE_TYPES,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.init()
  },
  methods: {
    init() {
      if (this.data) {
        this.formData = JSON.parse(JSON.stringify(this.data))
        if (this.formData.dsId) {
          this.getDsTable(this.formData.dsId)
        }
      }
      this.getTargetDsDict()
    },
    getTargetDsDict() {
      getTargetDsDict().then(([err, res]) => {
        this.dsList = res.data
      })
    },
    onDataResourceObjChange(dataResourceObj) {
      console.log('onDataResourceObjChange', dataResourceObj)
      if (dataResourceObj) {
        let data = { dsId: this.formData.dsId, tableName: this.formData.dataResourceObj }
        getDsTagTableConfig(data).then(([err, res]) => {
          this.$set(this.formData, 'columns', res.data)
        })
      }
    },
    onDsIdChange(dsId) {
      console.log('onDsIdChange', dsId)
      if (dsId) {
        let item = this.dsList.find((item) => item.dsId == dsId)
        if (item) {
          this.formData.dsType = item.dsType
          this.formData.sysDsName = item.sysDsName
          this.getDsTable(dsId)
          this.$set(this.formData, 'dataResourceObj', undefined)
          this.$set(this.formData, 'columns', [])
        }
      }
    },
    handleSelectionChange(selection) {
      console.log('选中', selection)
      this.formData.columns.forEach((item) => {
        if (selection.includes(item) && !item.disableCoumn) {
          item.show = true
        } else {
          item.show = false
        }
      })
    },
    getDsTable(dsId) {
      let data = { dsId: dsId, dsType: 'tar' }
      getSysDsTableList(data).then(([err, res]) => {
        this.tableList = res.data
      })
    },
    getDsTagTableConfig() {
      let data = { dsId: this.formData.dsId, dataResourceObj: this.formData.dataResourceObj, dsType: 'tar' }
      getSysDsHeadConfig(data).then(([err, res]) => {
        this.$set(this.formData, 'columns', res.data)
      })
    },
    close() {
      this.$emit('update:visible', false)
    },
    handelConfirm() {
      // 选中行校验
      let selection = this.formData.columns.filter((item) => item.show)
      if (selection.length <= 0) {
        this.$message.warning('请选中字段进行保存')
        return false
      }
      if (selection.some((item) => !item.mapColumn)) {
        this.$message.warning('选中字段需选择字段映射')
        return false
      }
      if (this.formData.writeMode === '1' && selection.every((item) => !item.updateBasis)) {
        this.$message.warning('更新模式需要选择更新依据列')
        return false
      }

      this.$emit('update', this.node, this.formData)
      this.close()
    },
    handleUpdateBasisChange(idx) {
      for (var i = 0; i < this.formData.columns.length; i++) {
        this.$set(this.formData.columns[i], 'updateBasis', i === idx)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.el-radio::v-deep {
  &.no-label {
    .el-radio__label {
      display: none;
    }
  }
}
</style>
