<template>
  <div class="edit-form y-container--tight no-padding">
    <!-- 编辑字典 -->
    <el-form
      v-if="flag === 'dict'"
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="100px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="名称"
            prop="NAME">
            <el-input
              v-model="formData.NAME"
              v-trim
              clearable
              placeholder="请输入名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="编号"
            prop="CODE">
            <el-input
              v-model="formData.CODE"
              v-trim
              clearable
              placeholder="请输入编号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="排序"
            prop="SORT_NUM">
            <el-input-number
              v-model="formData.SORT_NUM"
              controls-position="right"
              :min="1"
              :max="10000"
              placeholder="请输入序号"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="是否启用"
            prop="ENABLE_STATUS">
            <el-switch
              v-model="formData.ENABLE_STATUS"
              active-color="#52C41A"
              active-value="01"
              inactive-value="00">
            </el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="备注"
            prop="BAKUP">
            <el-input
              type="textarea"
              v-model="formData.BAKUP"
              v-trim
              :rows="4"
              maxlength="200"
              show-word-limit
              clearable
              placeholder="请输入备注"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 编辑字典项 -->
    <el-form
      v-else-if="flag === 'dictItem'"
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="100px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="名称"
            prop="NAME">
            <el-input
              v-model="formData.NAME"
              v-trim
              clearable
              placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="编号"
            prop="CODE">
            <el-input
              v-model="formData.CODE"
              v-trim
              clearable
              placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="序号"
            prop="SORT_NUM">
            <el-input-number
              v-model="formData.SORT_NUM"
              controls-position="right"
              :min="1"
              :max="10000"
              placeholder="请输入序号"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="是否启用"
            prop="ENABLE_STATUS">
            <el-switch
              v-model="formData.ENABLE_STATUS"
              active-color="#52C41A"
              active-value="01"
              inactive-value="00">
            </el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="备注"
            prop="BAKUP">
            <el-input
              type="textarea"
              :rows="4"
              maxlength="200"
              show-word-limit
              clearable
              v-model="formData.BAKUP"
              v-trim
              placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { addDict, updateDict, addDictItem, updateDictItem } from '@/api/dict-config'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
    flag: {
      type: String,
    },
    activeMenu: {
      type: String,
    },
  },
  data() {
    return {
      formData: {
        NAME: '',
        CODE: '',
        SORT_NUM: '',
        ENABLE_STATUS: '01',
        BAKUP: '',
      },
      rules: {
        NAME: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        CODE: [{ required: true, message: '请输入编号', trigger: 'blur' }],
        SORT_NUM: [{ required: true, message: '请输入序号', trigger: 'blur' }],
        ENABLE_STATUS: [{ required: true, message: '请选择是否启用', trigger: 'blur' }],
      },
      contentBefore: null,
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data) {
        const { ID, NAME, CODE, SORT_NUM, ENABLE_STATUS, BAKUP } = this.data
        Object.assign(this.formData, { ID, NAME, CODE, SORT_NUM, ENABLE_STATUS, BAKUP })
        this.contentBefore = { ...this.data }
      } else {
        this.formData = {
          NAME: '',
          CODE: '',
          SORT_NUM: '',
          ENABLE_STATUS: '01',
          BAKUP: '',
        }
      }
      // console.log('initPage', this.formData)
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const payload = {
          ...this.formData,
        }

        let submitAction
        if (this.data) {
          // 编辑
          if (this.flag === 'dict') {
            submitAction = updateDict
          } else {
            submitAction = updateDictItem
            payload.dictGroupId = this.data.DICT_GROUP_ID
            payload.contentBefore = this.contentBefore
          }
        } else {
          // 新增
          if (this.flag === 'dict') {
            submitAction = addDict
          } else {
            submitAction = addDictItem
            payload.dictGroupId = this.activeMenu
          }
        }

        const [err, res] = await submitAction(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              let data = this.flag === 'dict' ? this.formData.CODE : null
              this.$refs.editForm.resetFields()
              this.contentBefore = null
              this.$emit('update-data', data)
            },
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
