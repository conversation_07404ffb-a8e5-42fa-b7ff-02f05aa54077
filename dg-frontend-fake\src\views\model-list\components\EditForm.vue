<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="模型名称"
            prop="name">
            <el-input
              v-model="formData.name"
              v-trim
              placeholder="请输入"
              maxlength="30"
              clearable
              show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="模型类型"
            prop="type">
            <el-select
              v-model="formData.type"
              placeholder="请选择">
              <el-option
                v-for="(label, key) in modelTypeDict"
                :label="label"
                :value="key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="模型标签"
            prop="label">
            <el-cascader
              v-model="formData.labelType"
              :options="labelTypeList"
              placeholder="请选择"
              style="width: 100%"
              :show-all-levels="false"
              :props="cascaderProps"
              @change="handleChange"></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label-width="20px">
            <el-select
              v-model="formData.label"
              placeholder="请选择">
              <el-option
                v-for="item in labelList"
                :label="item.WORD_NAME"
                :value="item.WORD_NAME"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="24">
          <el-form-item
            label="模型路径"
            prop="sys_url">
            <el-input
              v-model="formData.sys_url"
              v-trim
              placeholder="请输入"
              clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="说明"
            prop="desc">
            <el-input
              v-model="formData.desc"
              v-trim
              type="textarea"
              placeholder="请输入说明"
              maxlength="200"
              show-word-limit
              clearable
              :autosize="{ minRows: 5, maxRows: 10 }"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { addModel, updateModel } from '@/api/model-list'
import { getClassTree, getList } from '@/api/hotword-manage'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      formData: {
        name: '',
        type: '',
        labelType: '',
        label: '',
        desc: '',
        sys_url: '',
      },
      rules: {
        name: [{ required: true, message: '请输入模型名称', trigger: 'blur' }],
        type: [{ required: true, message: '请选择模型类型', trigger: 'change' }],
        // label: [{ required: true, message: '请选择模型标签', trigger: 'change' }],
        // sys_url: [{ required: true, message: '请输入模型路径', trigger: 'blur' }],
      },
      cascaderProps: {
        value: 'id',
        label: 'name',
        emitPath: false,
        checkStrictly: true,
      },
      modelTypeDict: {
        1: '共有模型',
        2: '私有模型',
      },
      labelTypeList: [],
      labelList: [],
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
    this.getLabelTypeList()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data && JSON.stringify(this.data) != '{}') {
        const { ID, NAME, VERSION, SOURCE, STATUS, TYPE, LABEL_TYPE, LABEL, DESC, SYS_URL } = this.data
        this.formData = {
          id: ID,
          name: NAME,
          type: TYPE,
          labelType: LABEL_TYPE,
          label: LABEL,
          desc: DESC,
          sys_url: SYS_URL,
        }
        this.getLabelList()
      } else {
        this.formData = {
          name: '',
          type: '',
          labelType: '',
          label: '',
          desc: '',
          sys_url: '',
        }
      }
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }
        let api = addModel
        const payload = {
          ...this.formData,
        }
        if (payload.id) {
          api = updateModel
        }

        const [err, res] = await api(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
    async getLabelTypeList() {
      const payload = { moduleType: '6' }
      const [err, res] = await getClassTree(payload)
      if (res) {
        this.labelTypeList = res.data?.children || []
      }
    },
    async getLabelList() {
      let labelType = this.formData.labelType
      if (!labelType) {
        return false
      }
      const payload = {
        typeId: labelType,
        moduleType: '6',
      }

      const [err, res] = await getList(payload)
      if (res) {
        this.labelList = res.data || []
      }
    },
    handleChange() {
      this.getLabelList()
      this.formData.label = ''
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form::v-deep {
  position: relative;

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
