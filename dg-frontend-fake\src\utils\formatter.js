import path from 'path-browserify'

/**
 * @description: 将路由对象/数组中的component（如果为路径string）替换为懒加载component对象
 * @param {*} routes
 * @return {*} routes
 * @author: xz
 */
export function formatRoutes(routes) {
  if (!routes) {
    return false
  }
  if (Array.isArray(routes) && routes.length !== 0) {
    return routes.map(route => formatRoutes(route))
  } else if (typeof routes === 'object') {
    if (typeof routes.component === 'string') {
      let compStr = routes.component
      if (compStr === 'router-view') {
        compStr = 'views/view'
      }
      routes.component = loadComponent(compStr)
    }
    if (routes.children && routes.children?.length !== 0) {
      routes.children = formatRoutes(routes.children)
    }
    return routes
  } else {
    return routes
  }
}

// 路由懒加载
export const loadComponent = (path) => {
  if (process.env.NODE_ENV === 'production') {
    return () => import(`@/${path}`)
  } else {
    return require(`@/${path}`).default
  }
}

export function formatDate(date) {
  if (!date) {
    return false
  }
  date = new Date(date)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}-${month}-${day}`
}

// 格式化URL
export function formatUrl(url) {
  if (url) {
    return path.join(process.env.VUE_APP_BASE_API, url)
  } else {
    return ''
  }
}

// 将数字每三位加<,>
export function formatNumberWithComma(num, separator = ',') {
  if (!num) {
    return '0'
  }
  const x = String(num).split('.')
  let x1 = x[0]
  const x2 = x.length > 1 ? '.' + x[1] : ''
  const rgx = /(\d+)(\d{3})/
  if (separator && typeof separator !== 'number') {
    while (rgx.test(x1)) {
      x1 = x1.replace(rgx, '$1' + separator + '$2')
    }
  }
  return x1 + x2
}