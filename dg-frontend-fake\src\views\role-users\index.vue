<template>
  <base-card
    id="role-users"
    class="y-page">
    <div class="y-bar header">
      <h2 class="y-title">{{ roleDict ? `角色用户（${roleDict[formData.roleId]}）` : '角色用户' }}</h2>
      <el-button
        class="mini"
        type="primary"
        @click.native="openAccredit({ roleId: formData.roleId })">
        <svg-icon icon="add"></svg-icon>
        授权用户
      </el-button>
    </div>
    <div class="y-container--tight">
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <el-table-column
          type="index"
          label="序号"
          width="100">
        </el-table-column>
        <el-table-column
          label="用户名称"
          prop="USERNAME">
        </el-table-column>
        <el-table-column
          label="用户编号"
          prop="USER_ID"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="角色"
          prop="ROLES">
          <template v-slot="scope">
            <template v-if="scope.row.ROLES.trim()">
              <el-tag
                v-for="role in scope.row.ROLES.split(',')"
                size="small"
                type="primary"
                >{{ role }}</el-tag
              >
            </template>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="100">
          <template slot-scope="scope">
            <el-link
              v-debounce="{ evtHandler: () => disaccreditUser(scope.row) }"
              :key="scope.row.USER_ID"
              type="primary"
              :underline="false"
              >取消授权</el-link
            >
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
    <el-drawer
      title="授权用户"
      :visible.sync="accreditDrawerShow"
      direction="rtl"
      :size="680">
      <accredit-form
        ref="accreditForm"
        :data="accreditDrawerData"
        @close-accredit="closeAccredit"
        @update-data="fetchData"></accredit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getAccreditList, disaccreditUser, getRoleDict } from '@/api/role'
import AccreditForm from './components/AccreditForm'

export default {
  name: 'RoleUsers',
  components: {
    AccreditForm,
  },
  props: {},
  data() {
    return {
      tableData: [],
      tableLoading: false,
      total: 0,
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        roleId: null,
      },
      roleDict: null,
      accreditDrawerShow: false,
      accreditDrawerData: null,
    }
  },
  computed: {},
  watch: {
    $route: {
      handler(route) {
        if (route.path === '/system-manage/role-users') {
          Object.assign(this.formData, route.query)
        }
      },
      deep: true,
      immediate: true,
    },
    'formData.roleId': {
      handler(roleId) {
        if (roleId) {
          this.fetchData()
        }
      },
      immediate: true,
    },
    accreditDrawerShow(flag) {
      if (!flag) {
        this.fetchData()
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    async fetchData() {
      this.tableLoading = true
      const [err, res] = await getAccreditList(this.formData)
      if (res) {
        this.tableData = res.data
        this.total = res.totalRow
      }
      this.getRoleDict()
      this.tableLoading = false
    },
    async disaccreditUser(data) {
      const payload = {
        roleId: data.ROLE_ID,
        userId: data.USER_ID,
      }
      const [err, res] = await disaccreditUser(payload)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    async getRoleDict() {
      const [err, res] = await getRoleDict()
      if (res) {
        this.roleDict = res.data
      }
    },
    openAccredit(data) {
      if (data) {
        this.accreditDrawerData = data
      } else {
        this.accreditDrawerData = null
      }
      this.accreditDrawerShow = true
    },
    closeAccredit() {
      this.accreditDrawerShow = false
    },
  },
}
</script>

<style lang="scss">
#role-users {
  > .header {
    border-bottom: 1px solid $borderColor;
  }

  > .y-container--tight {
    padding-top: 16px;
    padding-bottom: 0;
    border-bottom: 1px solid $borderColor;

    .el-table {
      .el-tag {
        + .el-tag {
          margin-left: 4px;
        }
      }

      &::before {
        content: none;
      }
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }
}
</style>
