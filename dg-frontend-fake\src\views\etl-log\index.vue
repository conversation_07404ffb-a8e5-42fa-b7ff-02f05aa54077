<template>
  <base-card
    id="etl-log"
    class="y-page">
    <div class="y-bar">
      <h2 class="y-title">调度日志</h2>
      <!-- <span>ETL调度名称</span>
      <el-select
        v-model="formData.status"
        placeholder="请选择DB/DBMS/SQL"
        clearable
        size="small"
        style="width: 160px">
        <el-option
          :label="1"
          :value="1"></el-option>
      </el-select> -->
      <span>调度日期</span>
      <el-date-picker
        v-model="dateRange"
        type="datetimerange"
        size="small"
        value-format="yyyy-MM-dd HH:mm:ss"
        @change="dateChange"
        :picker-options="pickerOptions"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :default-time="['00:00:00', '23:59:59']"
        style="width: 340px">
      </el-date-picker>
      <span>调度任务</span>
      <el-select
        v-model="formData.etlId"
        size="small"
        filterable
        placeholder="请选择"
        clearable
        style="width: 220px">
        <el-option
          v-for="(label, key) in etlDict"
          :key="key"
          :label="label"
          :value="key">
        </el-option>
      </el-select>
      <span>关键字</span>
      <el-input
        v-model="formData.key"
        v-trim
        size="small"
        style="width: 220px"
        clearable
        placeholder="请输入关键字进行搜索"></el-input>
      <el-button
        class="mini"
        type="primary"
        size="small"
        plain
        @click.native="resetSearch">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
    </div>
    <empty-wrapper
      class="y-container--tight"
      :toggle="!taskList || taskList?.length === 0"
      v-loading="loading">
      <div
        class="task-wrapper y-card-wrapper y-container--tight no-padding"
        v-reset-scroll>
        <config-card
          v-for="item in taskList"
          :key="item.LOG_ID"
          :data="item">
          <template #left>
            <div class="title-wrapper">
              <h5 class="title">
                {{ item.ETL_NAME }}
              </h5>
              <span
                class="taggy info"
                style="margin-left: 4px"
                >{{ `Ver. ${item.VER || '-'}` }}</span
              >
              <span
                :class="['taggy', getTag(item.ETL_STATUS, 'class')]"
                style="margin-left: 8px"
                >{{ getTag(item.ETL_STATUS, 'label') }}</span
              >
              <span
                :class="['taggy', item.RUN_TYPE == 2 ? 'primary' : 'delay']"
                style="margin-left: 4px"
                >{{ item.RUN_TYPE == 2 ? '定时调度运行' : '手工运行' }}</span
              >
            </div>
            <p class="desc">
              <span class="prefix">开始时间：</span><span>{{ item.BEGIN_TIME }}</span
              ><span
                class="prefix"
                style="margin-left: 24px"
                >完成时间：</span
              ><span>{{ item.END_TIME }}</span>
            </p>
            <div class="tags">
              <div class="tag">
                执行记录数：<span class="primary">{{ item.SRC_DATA_COUNT }}</span>
              </div>
              <div class="tag">
                成功记录数：<span class="success">{{ item.SYNC_SUCC_COUNT }}</span>
              </div>
              <div class="tag">
                失败记录数：<span class="danger">{{ item.SYNC_FAIL_COUNT }}</span>
              </div>
            </div>
          </template>
          <template #right>
            <div>
              <el-link
                type="primary"
                :underline="false"
                @click.native="
                  $router.push({
                    name: 'ExecuteLog',
                    params: { logId: item.LOG_ID, overrideTitle: item.ETL_NAME + '执行记录' },
                  })
                "
                >查看执行记录</el-link
              >
              <span class="sep"></span>
              <el-link
                type="primary"
                :underline="false"
                @click.native="
                  $router.push({
                    name: 'ExecuteLog',
                    params: { logId: item.LOG_ID, overrideTitle: item.ETL_NAME + '执行记录' },
                    query: { etlResult: '1' },
                  })
                "
                >查看成功记录</el-link
              >
              <span class="sep"></span>
              <el-link
                type="primary"
                :underline="false"
                @click.native="
                  $router.push({
                    name: 'ExecuteLog',
                    params: { logId: item.LOG_ID, overrideTitle: item.ETL_NAME + '执行记录' },
                    query: { etlResult: '0' },
                  })
                "
                >查看失败记录</el-link
              >
            </div>
            <etl-version-selector :etl-id="item.ETL_ID"></etl-version-selector>
          </template>
        </config-card>
      </div>
    </empty-wrapper>
    <div class="footer">
      <pagination
        :current-page.sync="formData.pageIndex"
        :page-size.sync="formData.pageSize"
        :total="total"
        @page="fetchData"></pagination>
    </div>
  </base-card>
</template>

<script>
import { getTaskList, getEtlDict, getEtlVerList } from '@/api/etl-log'
import ConfigCard from '@/components/ConfigCard'
import EtlVersionSelector from '@/components/EtlVersionSelector'

export default {
  name: 'EtlLog',
  components: {
    ConfigCard,
    EtlVersionSelector,
  },
  props: {},
  data() {
    return {
      total: 0,
      loading: false,
      dateRange: [],
      formData: {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
        key: '',
        etlId: '',
        startTime: '',
        endTime: '',
      },
      etlDict: null,
      taskList: [],
      pickerOptions: {
        // 禁止选中今日之后的日期
        disabledDate(time) {
          const now = new Date()
          const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
          return time.getTime() > todayEnd.getTime()
        },
      },
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getEtlDict()
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true

      const [err, res] = await getTaskList(this.formData)
      if (res) {
        this.taskList = res.data
        this.total = res.totalRow
      }

      this.loading = false
    },
    async getEtlDict() {
      const [err, res] = await getEtlDict()
      if (res) {
        this.etlDict = res.data
      }
    },
    dateChange() {
      if (!this.dateRange || this.dateRange?.length === 0) {
        this.formData.startTime = ''
        this.formData.endTime = ''
        return false
      }
      const [begin, end] = this.dateRange
      this.formData.startTime = begin
      this.formData.endTime = end
    },
    resetSearch() {
      this.dateRange = []
      this.formData = {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
        key: '',
        etlId: '',
        startTime: '',
        endTime: '',
      }
      this.fetchData()
    },
    getTag(status, flag) {
      const map = {
        class: {
          0: 'primary',
          1: 'success',
          2: 'danger',
        },
        label: {
          0: '执行中',
          1: '已完成',
          2: '强制中断',
        },
      }

      return map[flag][status]
    },
  },
}
</script>

<style lang="scss">
#etl-log {
  @import '@/assets/style/modules/config-card-page-with-footer.scss';
  background-color: transparent;
}
</style>
