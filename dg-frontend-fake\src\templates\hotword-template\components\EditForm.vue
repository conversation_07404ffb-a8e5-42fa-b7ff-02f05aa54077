<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="150px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            :label="name"
            prop="wordName">
            <el-input
              v-model="formData.wordName"
              v-trim
              clearable
              placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            :label="name + '类别'"
            prop="typeId">
            <el-select
              v-model="formData.typeId"
              placeholder="请选择">
              <el-option
                v-for="item in parentClass?.children"
                :label="item.TYPE_NAME"
                :value="item.TYPE_ID"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { getClassTree, updateHotword, addHotword } from '@/api/hotword-manage'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
    activeMenu: {
      type: Object,
      default: () => {},
    },
    name: { type: String },
    moduleType: { type: String },
  },
  data() {
    return {
      formData: {
        wordName: null,
        typeId: null,
      },
      rules: {
        wordName: [{ required: true, message: '请输入' + this.name, trigger: 'blur' }],
        typeId: [{ required: true, message: '请选择类别', trigger: 'blur' }],
      },
      parentClass: {},
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
    activeMenu: {
      handler(activeMenu) {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data) {
        this.formData = {
          wordName: this.data.WORD_NAME,
          typeId: this.data.TYPE_ID,
          wordId: this.data.ID,
          oldTypeId: this.data.TYPE_ID,
        }
      } else {
        this.formData = {
          wordName: null,
          typeId: this.activeMenu.P_TYPE_ID === '0' ? null : this.activeMenu.TYPE_ID,
        }
      }
      this.getClassTree()
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const payload = { 
          ...this.formData,
          moduleType: this.moduleType,
        }
        let subimtAction = addHotword
        if (this.data) {
          subimtAction = updateHotword
        }

        const [err, res] = await subimtAction(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
    async getClassTree() {
      const id = this.activeMenu[this.activeMenu.P_TYPE_ID === '0' ? 'TYPE_ID' : 'P_TYPE_ID']
      if (id === this.parentClass.TYPE_ID) {
        return false
      }
      const payload = {
        typeId: id,
        moduleType: this.moduleType,
      }
      const [err, res] = await getClassTree(payload)
      if (res) {
        this.parentClass = res.data
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
