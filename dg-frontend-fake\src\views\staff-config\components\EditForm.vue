<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="90px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="人员姓名"
            prop="infoName">
            <el-input
              v-model="formData.infoName"
              v-trim
              clearable
              placeholder="请输入人员姓名"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="所属机构"
            prop="orgId">
            <el-cascader
              ref="cascader"
              v-model="formData.orgId"
              :options="treeData"
              placeholder="请选择所属机构"
              :show-all-levels="false"
              :props="cascaderProps"
              @change="handleChange"
              style="width: 100%"></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="人员职位"
            prop="infoJob">
            <el-input
              v-model="formData.infoJob"
              v-trim
              clearable
              placeholder="请输入人员职位"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="身份证号码"
            prop="infoIdcard">
            <el-input
              v-model="formData.infoIdcard"
              v-trim
              clearable
              placeholder="请输入身份证号码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="手机号"
            prop="infoTel">
            <el-input
              v-model="formData.infoTel"
              v-trim
              clearable
              placeholder="请输入手机号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { getClassTree, addStaff, updateStaff } from '@/api/csrc-info'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
    activeMenu: {
      type: [String, Number],
      default() {
        return null
      },
    },
  },
  data() {
    const verifyIdcard = (rule, value, callback) => {
      if (
        !value ||
        value.includes('*') ||
        /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/.test(
          value
        )
      ) {
        callback()
      } else {
        callback(new Error('请输入正确的格式'))
      }
    }

    const verifyTel = (rule, value, callback) => {
      if (!value || value.includes('*') || /^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$/.test(value)) {
        callback()
      } else {
        callback(new Error('请输入正确的格式'))
      }
    }

    return {
      treeData: [],
      formData: {
        infoName: null,
        orgId: null,
        infoJob: null,
        infoIdcard: null,
        infoTel: null,
      },
      rules: {
        infoName: [{ required: true, message: '请输入人员姓名', trigger: 'blur' }],
        orgId: [{ required: true, message: '请选择所属机构', trigger: 'change' }],
        // infoJob: [{ required: true, message: '请输入人员职位', trigger: 'blur' }],
        infoIdcard: [
          // { required: true, message: '请输入身份证号码', trigger: 'blur' },
          { validator: verifyIdcard, trigger: 'change' },
        ],
        infoTel: [
          // { required: true, message: '请输入手机号', trigger: 'blur' },
          { validator: verifyTel, trigger: 'change' },
        ],
      },
      cascaderProps: {
        value: 'id',
        label: 'name',
        emitPath: false,
        checkStrictly: true,
      },
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
    activeMenu: {
      handler() {
        this.initPage()
      },
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data && JSON.stringify(this.data) != '{}') {
        this.$set(this.formData, 'infoName', this.data?.INFO_NAME)
        this.$set(this.formData, 'orgId', this.data?.ORG_ID)
        this.$set(this.formData, 'infoJob', this.data?.INFO_JOB)
        this.$set(this.formData, 'infoIdcard', this.data?.INFO_IDCARD)
        this.$set(this.formData, 'infoTel', this.data?.INFO_TEL)
      } else {
        this.formData = {
          infoName: null,
          orgId: this.activeMenu,
          infoJob: null,
          infoIdcard: null,
          infoTel: null,
        }
      }
      this.initTree()
    },
    async initTree(id) {
      const [err, res] = await getClassTree(id || '')
      if (res) {
        this.treeData = [res.data]
      }
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const payload = { ...this.formData }
        let submitAction = addStaff
        // 有id为更新，无id为新增
        if (this.data?.INFO_ID) {
          submitAction = updateStaff
          payload.infoId = this.data?.INFO_ID
          payload.contentBefore = this.data
        }

        const [err, res] = await submitAction(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
    handleChange() {
      this.$refs.cascader.dropDownVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
