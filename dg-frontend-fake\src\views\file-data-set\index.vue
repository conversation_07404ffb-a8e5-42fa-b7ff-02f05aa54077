<template>
  <base-card
    id="file-data-set"
    class="y-page">
    <div class="y-bar header">
      <h2 class="y-title">文件数据集</h2>
      <el-button
        class="mini"
        type="primary"
        @click.native="openEdit(null)">
        <svg-icon icon="add"></svg-icon>
        新增数据集
      </el-button>
    </div>
    <div class="y-bar search-bar">
      <!-- <span>日期</span>
      <el-date-picker
        v-model="dateRange"
        type="datetimerange"
        size="small"
        value-format="yyyy-MM-dd HH:mm:ss"
        @change="dateChange"
        :picker-options="pickerOptions"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :default-time="['00:00:00', '23:59:59']"
        style="width: 340px">
      </el-date-picker> -->
      <span>关键字</span>
      <el-input
        v-model="formData.keyword"
        v-trim
        placeholder="请输入关键词进行搜索"
        size="small"
        clearable
        style="width: 220px"></el-input>
      <el-button
        v-debounce="{ evtHandler: fetchData }"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
    </div>
    <div class="y-container--tight">
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <el-table-column
          type="index"
          label="序号"
          width="50">
        </el-table-column>
        <el-table-column
          label="数据集名称"
          prop="NAME"
          min-width="150"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="说明"
          prop="DESC"
          min-width="300"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="文件数量"
          prop="FILE_NUM"
          width="100"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="创建人"
          prop="CREATOR"
          width="120"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="创建时间"
          prop="CREATE_TIME"
          width="180"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="操作"
          width="200">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :underline="false"
              @click.native="getDetail(scope.row, 'detail')"
              >查看详情</el-link
            >
            <el-link
              type="primary"
              :underline="false"
              @click.native="openEdit(scope.row)"
              >编辑</el-link
            >
            <el-link
              type="danger"
              :underline="false"
              @click.native="deleteDataset(scope.row)"
              >删除</el-link
            >
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="800">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
    <el-drawer
      title="详细信息"
      :visible.sync="detailVisible"
      direction="rtl"
      :size="800">
      <detail-table
        :data="detailData"
        @update-data="fetchData"></detail-table>
    </el-drawer>
  </base-card>
</template>

<script>
import { getList, deleteDataset } from '@/api/file-data-set'
import EditForm from './components/EditForm'
import DetailTable from './components/DetailTable'

export default {
  name: 'FileDataSet',
  components: {
    EditForm,
    DetailTable,
  },
  props: {},
  data() {
    return {
      tableData: [],
      tableLoading: false,
      total: 0,
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        keyword: '',
        beginDate: '',
        endDate: '',
      },
      dateRange: [],
      pickerOptions: {
        // 禁止选中今日之后的日期
        disabledDate(time) {
          const now = new Date()
          const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
          return time.getTime() > todayEnd.getTime()
        },
      },
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
      detailVisible: false,
      detailData: null,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.tableLoading = true
      const [err, res] = await getList(this.formData)
      if (res) {
        this.tableData = res.data
        this.total = res.totalRow
      }
      this.tableLoading = false
    },
    async deleteDataset(data) {
      try {
        await this.$confirm('此操作将永久删除选中数据集, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const [err, res] = await deleteDataset(data.ID)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    dateChange() {
      if (!this.dateRange || this.dateRange?.length === 0) {
        this.formData.beginDate = ''
        this.formData.endDate = ''
        return false
      }
      const [begin, end] = this.dateRange
      this.formData.beginDate = begin
      this.formData.endDate = end
    },
    getDetail(data) {
      this.detailData = { ...data }
      this.detailVisible = true
    },
    openEdit(data) {
      if (data) {
        this.editDrawerTitle = '编辑数据集'
        this.editDrawerData = data
      } else {
        this.editDrawerTitle = '新增数据集'
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
  },
}
</script>

<style lang="scss">
#file-data-set {
  > .header {
    border-bottom: 1px solid $borderColor;
  }

  > .search-bar {
    padding-top: 16px;
    justify-content: flex-start;
  }

  > .y-container--tight {
    padding-bottom: 0;
    border-bottom: 1px solid $borderColor;

    .el-table::before {
      content: none;
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }

  .el-dialog__wrapper {
    &.no-padding {
      .el-dialog__body {
        padding: 0;
      }
    }
  }
}
</style>
