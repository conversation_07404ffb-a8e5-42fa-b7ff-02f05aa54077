<template>
  <div
    class="area-chart y-container no-padding"
    ref="chart-container"></div>
</template>

<script>
import chartMixins from '@/mixins/chartMixins.js'
import chinaMap from '@/assets/data/chinaMap.json'
import provinceData from '@/assets/data/provinceData.json'

export default {
  components: {},
  mixins: [chartMixins],
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    areaMap: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {}
  },
  computed: {
    dataList() {
      return this.data.map((item) => {
        return {
          name: this.areaMap[item.PROVINCE],
          value: item.PROVINCE_TOTAL,
          code: item.PROVINCE,
        }
      })
    },
    option() {
      return {
        tooltip: {},
        visualMap: {
          type: 'continuous',
          min: 0,
          max: 1000,
          left: '5%',
          top: 'bottom',
          itemHeight: 200,
          text: ['1000', '0'],
          hoverLink: false,
          calculable: true,
          orient: 'vertical',
          inRange: {
            color: ['#e9f2ff', '#81A9E6'],
            symbolSize: [this.nowSize(10), this.nowSize(10)],
          },
          outOfRange: {
            color: '#eee',
          },
        },
        series: [
          {
            name: '标签数量',
            type: 'map',
            mapType: 'chinaMap',
            selectedMode: 'single',
            coordinateSystem: 'geo',
            zoom: 1,
            data: this.dataList,
            hoverAnimation: true,
            top: '20%',
            left: '15%',
            right: '15%',
            label: {
              normal: {
                show: false,
                fontSize: this.nowSize(16),
                color: '#000',
              },
              emphasis: {
                show: true,
                formatter: '{b}',
                position: 'inside',
              },
            },
            emphasis: {
              itemStyle: {
                areaColor: '#0555CE',
              },
            },
            tooltip: {
              show: true,
              trigger: 'item',
              formatter: (params) => {
                const { name, value } = params
                return `${name}: ${value || 0}`
              },
              textStyle: {
                color: '#000',
                fontSize: this.nowSize(18),
                lineHeight: this.nowSize(18),
              },
            },
            itemStyle: {
              normal: {
                borderColor: '#fff',
                borderWidth: this.nowSize(1),
              },
            },
            zlevel: 1,
          },
        ],
      }
    },
  },
  beforeMount() {
    this.$echarts.registerMap('chinaMap', chinaMap)
    this.$nextTick(() => {
      this.chart.on('selectchanged', this.handleSelect)
    })
  },
  beforeDestroy() {
    this.chart.off('selectchanged', this.handleSelect)
  },
  methods: {
    handleSelect(param) {
      let code
      if (param.selected[0]) {
        let idx = param.selected[0].dataIndex[0]
        code = this.dataList[idx]?.code
      }
      this.$emit('area-change', code)
    },
  },
}
</script>

<style lang="scss">
.area-chart {
}
</style>
