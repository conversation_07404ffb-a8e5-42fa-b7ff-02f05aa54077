<template>
  <div
    id="menu"
    class="flex">
    <!-- <div class="menu-title">自动ETL工具栏</div> -->
    <div class="flex-item">
      <el-menu
        :default-openeds="defaultOpeneds"
        :border="0">
        <el-submenu
          v-for="(menu, idx) in filteredList"
          :index="String(idx)"
          :key="menu.name">
          <template slot="title">
            <img
              :src="require(`@/assets/images/i${idx + 1}.png`)"
              height="20"
              style="margin-right: 10px" />
            <span style="font-size: 16px; font-weight: bold">{{ menu.name }}</span>
            <template v-if="idx === 3">
              <el-input
                class="filter-input"
                @click.native.stop="() => {}"
                v-show="filters[idx]"
                v-model="keywords[idx]"
                size="medium"
                placeholder="搜索组件分类"></el-input>
              <i
                class="el-icon-search filter-icon"
                @click.stop="toggleFilter(idx)"></i>
            </template>
          </template>
          <menu-item
            :menu="menu"
            :classify="idx + 1"
            :pidx="String(idx)"
            v-on="$listeners"></menu-item>
        </el-submenu>
      </el-menu>
    </div>
  </div>
</template>

<script>
import MenuItem from './MenuItem.vue'
export default {
  components: {
    MenuItem,
  },
  props: {
    menuList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // 解决打开菜单总是回归默认问题
      defaultOpeneds: ['0', '1', '2', '3'],
      keywords: [],
      filters: [],
    }
  },
  watch: {
    'menuList.length': {
      handler(l) {
        this.keywords = new Array(l).fill('')
        this.filters = new Array(l).fill('')
      },
      immediate: true,
    },
  },
  computed: {
    filteredList() {
      return this.menuList.map((menu, idx) => {
        if (!this.keywords[idx].trim() || !this.filters[idx]) {
          return menu
        } else {
          return {
            name: menu.name,
            module: this.filterTree(menu, this.keywords[idx]),
          }
        }
      })
    },
  },
  created() {},
  methods: {
    toggleFilter(idx) {
      this.$set(this.filters, idx, !this.filters[idx])
    },
    flatTree(data, list = []) {
      if (data?.module?.length > 0) {
        list = list.concat(data.module)
      }
      if (data?.children?.length > 0) {
        list = list.concat(data.children.map((child) => this.flatTree(child, list)).flat())
      }
      return list
    },
    filterTree(data, word) {
      return this.flatTree(data).filter((item) => item.name.includes(word.trim()))
    },
  },
}
</script>

<style lang="scss" scoped>
#menu {
  width: 288px;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #eee;
  overflow: auto;
}

.menu-title {
  color: #333;
  font-size: 20px;
  padding: 10px 20px;
  border-bottom: 1px solid #eee;
}

.el-menu {
  border-right: 0;
}

.el-submenu::v-deep {
  > .el-submenu__title {
    position: relative;
    border-top: 1px solid #eee;
    border-color: #eee;
  }

  &.is-opened {
    > .el-submenu__title {
      border-bottom: 1px solid #eee;
    }
  }

  &:nth-child(1) {
    > .el-submenu__title {
      border-top: none;
    }
  }

  &:last-child {
    > .el-submenu__title {
      border-bottom: 1px solid #eee;
    }
  }
}

.filter-input {
  position: absolute;
  top: 0;
  left: 0;
  padding: 0 20px;
  z-index: 9;
}

.filter-icon {
  position: absolute;
  top: 20px;
  right: 30px;
  color: #868686;
  z-index: 10;
}
</style>
