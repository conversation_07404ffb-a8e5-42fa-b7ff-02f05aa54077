<template>
  <base-card
    id="target-source"
    class="y-page">
    <div class="y-bar">
      <h2 class="y-title">目标数据源管理</h2>
      <span>数据类型</span>
      <!-- <el-select
        v-model="formData.TAG_DS_TYPE"
        placeholder="请选择数据类型"
        clearable
        size="small"
        style="width: 160px">
        <el-option
          label="数据库"
          value="ds"></el-option>
        <el-option
          label="EXCEL"
          value="excel"></el-option>
        <el-option
          label="全部"
          value=""></el-option>
      </el-select> -->
      <span>关键字</span>
      <el-input
        v-model="formData.TAG_DS_NAME"
        v-trim
        size="small"
        style="width: 220px"
        clearable
        placeholder="请输入关键字进行搜索"></el-input>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
      <el-button
        class="mini"
        type="primary"
        @click.native="openEdit(null)">
        <svg-icon icon="add"></svg-icon>
        新建目标数据源
      </el-button>
    </div>
    <empty-wrapper
      class="y-container--tight"
      :toggle="!sourceList || sourceList?.length === 0"
      v-loading="loading">
      <div class="template-item-wrapper y-card-wrapper y-container--tight no-padding">
        <config-card
          v-for="item in sourceList"
          :key="item.TAG_DS_ID"
          :data="item">
          <template #left>
            <h5 class="title">
              {{ item.TAG_DS_NAME }}
            </h5>
            <p class="desc"><span class="prefix">系统数据源名称：</span>{{ item.SYS_DS_NAME }}</p>
            <div class="tags">
              <div class="tag">创建时间：{{ item.CREATE_TIME }}</div>
            </div>
          </template>
          <template #right>
            <div class="switch">
              <span class="prefix">启用状态：</span>
              <el-switch
                v-model="item.TAG_DS_STATE"
                @change="handleSwitch(item)"
                active-color="#52C41A"
                active-value="1"
                inactive-value="0">
              </el-switch>
            </div>
            <div class="btn-set">
              <el-button
                class="mini"
                type="danger"
                plain
                size="small"
                @click.native="deleteSource(item)"
                >删除</el-button
              >
              <el-button
                class="mini"
                type="primary"
                plain
                size="small"
                @click.native="openEdit(item)"
                >编辑</el-button
              >
              <el-button
                v-if="item.TAG_DS_TYPE !== 'excel'"
                v-debounce="{ evtHandler: () => testSource(item) }"
                class="mini"
                type="primary"
                size="small"
                >链接测试</el-button
              >
            </div>
          </template>
        </config-card>
      </div>
    </empty-wrapper>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getList, testSource, updateSource, deleteSource } from '@/api/source'
import EditForm from './components/EditForm.vue'
import ConfigCard from '@/components/ConfigCard'

export default {
  name: 'TargetSource',
  components: {
    ConfigCard,
    EditForm,
  },
  props: {},
  data() {
    return {
      loading: false,
      formData: {
        pageIndex: 1,
        pageSize: 999,
        TAG_DS_NAME: '',
        TAG_DS_TYPE: '',
        SYS_DS_NAME: '',
      },
      sourceList: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true

      const [err, res] = await getList(this.formData, 'tag')
      if (res && Array.isArray(res?.data)) {
        this.sourceList = res.data
      }

      this.loading = false
    },
    openEdit(data) {
      if (data) {
        this.editDrawerData = data
        this.editDrawerTitle = '编辑目标数据源'
      } else {
        this.editDrawerData = null
        this.editDrawerTitle = '新建目标数据源'
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    async handleSwitch(data) {
      const payload = { ...data }
      payload.id = data.TAG_DS_ID
      delete payload.TAG_DS_ID

      const [err, res] = await updateSource(payload, 'tag')
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {},
        })
      } else {
        this.fetchData()
      }
    },
    async testSource(data) {
      const [err, res] = await testSource(data.SYS_DS_NAME, 'tag')
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {},
        })
      }
    },
    async deleteSource(data) {
      try {
        await this.$confirm('此操作将永久删除选中数据源, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }
      const [err, res] = await deleteSource(data.TAG_DS_ID, 'tag')
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
  },
}
</script>

<style lang="scss">
#target-source {
  @import '@/assets/style/modules/config-card-page.scss';
  background-color: transparent;
}
</style>
