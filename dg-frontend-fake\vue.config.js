const { defineConfig } = require('@vue/cli-service')
const path = require('path')
const HappyPack = require('happypack');
const os = require('os');
const happyThreadPool = HappyPack.ThreadPool({ size: os.cpus().length });

function resolve(dir) {
  return path.join(__dirname, dir)
}

const appMap = {
  portal: {
    outputDir: 'build/dist',
  },
  login: {
    outputDir: 'build/login',
  },
  model: {
    outputDir: 'build/model',
  }
}

let argv = process.argv
let app
let nameIndex = argv.findIndex(i => i === '--name')
if(nameIndex !== -1) {
  app = argv[nameIndex + 1]
} else {
  app = 'portal'
}

console.log('build ' + app)

module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: './',
  outputDir: appMap[app].outputDir,
  // 关闭生产环境sourceMap
  productionSourceMap: false,
  devServer: {
    open: true,
    hot: true,
    port: 8081,
    proxy: {
      // '/apps': {
      //   target: 'http://172.16.86.101:18888/',
      //   changeOrigin: true,
      //   ws: false,
      // },
      '/': {
        // target: 'http://172.16.65.12:19059/',
        target: 'http://172.16.86.101:19059/',
        changeOrigin: true,
        ws: false,
      },
    },
  },
  css: {
    loaderOptions: {
      scss: {
        // 变量注入
        // 防止重复注入
        additionalData: (content, loaderContext) => {
          const { resourcePath } = loaderContext;
          if (/variables\.scss/.test(resourcePath) || /mixin\.scss/.test(resourcePath)) {
            return content
          }
          return '@import "@/assets/style/common/variables.scss";' + '@import "@/assets/style/common/mixin.scss";' + content
        },
        sassOptions: { outputStyle: 'expanded' } // fix: 解决 element-ui 图标 icon 偶现乱码问题
      }
    }
  },
  configureWebpack: {
    resolve: {
      fallback: {
        path: require.resolve("path-browserify")
      },
    },
    plugins: [
      new HappyPack({
        id: 'happybabel',
        loaders: ['babel-loader'],
        threadPool: happyThreadPool
      })
    ]
  },
  chainWebpack(config) {
    if (app !== 'portal') {
      // 排除
      config.externals({
        'echarts': 'echarts',
      })
    }

    if (process.env.NODE_ENV === 'production') {
      // 去除debugger, console
      config.optimization.minimizer('terser').tap(args => {
        args.forEach(item => {
          if (item.hasOwnProperty('terserOptions')) {
            Object.assign(item['terserOptions'].compress, {
              drop_debugger: true,
              drop_console: true,
              pure_funcs: ['console.log']
            })
          }
          item['terserOptions']['format'] = {
            comments: false
          }
        })
        return args
      })
    }

    config
      .plugin('preload')
      .use('@vue/preload-webpack-plugin')
      .tap(() => [
        {
          rel: 'preload',
          // to ignore runtime.js
          fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
          include: 'initial'
        }
      ])
    // 删除prefetch
    config.plugins.delete('prefetch')

    // svg
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
  }
})
