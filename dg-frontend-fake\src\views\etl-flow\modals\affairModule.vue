<template>
  <el-drawer
    :size="680"
    v-bind="$attrs"
    v-on="$listeners"
    :title="(node.name || '事务处理组件') + '节点配置'">
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight"
        ref="editForm"
        :model="formData"
        :rules="rules"
        label-position="right"
        label-width="120px"
        size="medium"
        :disabled="!editable">
        <el-row
          v-for="({ KEY_NAME, KEY_LABEL, KEY_TYPE, DICT_NAME, DEFAULT_VALUE }, idx) in node.dgDataComConfigKeys"
          :key="KEY_NAME">
          <el-col :span="24">
            <el-form-item
              :label="KEY_LABEL"
              :prop="KEY_NAME">
              <el-input
                v-if="KEY_TYPE === '1'"
                v-model="formData[KEY_NAME]"
                placeholder="请输入" />
              <el-input-number
                v-else-if="KEY_TYPE === '2'"
                v-model="formData[KEY_NAME]"
                controls-position="right"
                :min="0"
                placeholder="请输入"></el-input-number>
              <el-select
                v-else-if="KEY_TYPE === '3'"
                v-model="formData[KEY_NAME]"
                placeholder="请选择">
                <el-option
                  v-for="(item, key) in currentCols"
                  :key="key"
                  :value="item.column">
                  {{ item.column }} ({{ item.columnLabel }})
                </el-option>
              </el-select>
              <el-select
                v-else-if="KEY_TYPE === '4'"
                v-model="formData[KEY_NAME]"
                placeholder="请选择">
                <el-option
                  v-for="item in dictList[DICT_NAME]"
                  :label="item.NAME"
                  :value="item.CODE"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="descHTML">
          <el-col :span="24">
            <el-form-item label="说明：">
              <pre
                v-html="descHTML"
                class="config-desc"></pre>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="footer y-bar">
        <el-button
          type="primary"
          plain
          size="small"
          @click.native="close"
          >取消</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="handelConfirm"
          >保存</el-button
        >
      </div>
    </div>
  </el-drawer>
</template>
<script>
import { getDictItemList } from '@/api/dict-config'
import { extractParamsFromStr } from '@/utils'

export default {
  inheritAttrs: false,
  components: {},
  props: ['node', 'data', 'currentCols', 'editable'],
  data() {
    return {
      formData: {},
      rules: {},
      paramList: [],
      descHTML: '',
      dictList: {},
    }
  },
  computed: {},
  watch: {
    formConfig: {
      handler() {
        this.getParams()
        this.getParamHTML()
      },
      deep: true,
    },
    formData: {
      handler() {
        this.getParamHTML()
      },
      deep: true,
    },
  },
  created() {
    console.log(this.node)
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data) {
        this.formData = JSON.parse(JSON.stringify(this.data))
        for (const key in this.formData) {
          let find = this.node.dgDataComConfigKeys.find((item) => item.KEY_NAME === key)
          if (!find) {
            delete this.formData[key]
          }
        }
      } else {
        this.formData = {}
        this.node.dgDataComConfigKeys.forEach(({ KEY_NAME, DEFAULT_VALUE }) => {
          this.$set(this.formData, KEY_NAME, DEFAULT_VALUE || '')
        })
      }

      this.node.dgDataComConfigKeys.forEach(({ KEY_NAME, KEY_LABEL, KEY_TYPE, DICT_NAME, DEFAULT_VALUE }) => {
        // 动态设置rule
        let message = `请${KEY_TYPE === '3' ? '选择' : '输入'}${KEY_LABEL}`
        let trigger = KEY_TYPE === '3' ? 'change' : 'blur'
        this.$set(this.rules, KEY_NAME, [{ required: true, message, trigger }])

        // 获取字典数据
        if (KEY_TYPE === '4') {
          this.getDictItem(DICT_NAME)
        }
      })

      this.getParams()
      this.getParamHTML()
    },
    close() {
      this.$emit('update:visible', false)
    },
    getParams() {
      this.paramList = extractParamsFromStr(this.node.configDesc).map((item) => item[0])
    },
    getParamHTML() {
      let descHTML = this.node.configDesc || ''
      this.paramList.forEach((param) => {
        let prop = param.slice(2, -1)
        if (prop.trim()) {
          let value = this.formData[prop] || ''
          let find = this.node.dgDataComConfigKeys.find((item) => item.KEY_NAME === prop)
          // 字典类型显示label(value)
          if(find.KEY_TYPE === '4' && this.dictList[find.DICT_NAME]) {
            let item = this.dictList[find.DICT_NAME].find(item => item.CODE === value)
            item && (value = `${item.NAME}(${item.CODE})`)
          }
          descHTML = descHTML.replaceAll(param, `<span class="param">${value}</span>`)
        } else {
          descHTML = descHTML.replaceAll(param, '')
        }
      })
      this.descHTML = descHTML
    },
    async getDictItem(dict) {
      const payload = {
        pageSize: 999,
        pageIndex: 1,
        pageType: 3,
        dictGroupId: dict,
      }
      const [err, res] = await getDictItemList(payload)
      if (res) {
        this.$set(this.dictList, dict, res.data || [])
        this.getParamHTML()
      }
    },
    handelConfirm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }
        this.$emit('update', this.node, this.formData)
        this.close()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.config-desc ::v-deep {
  color: $txtColor-light;
  white-space: break-spaces;
  .param {
    margin: 0 2px;
    color: $color-warning;
    font-weight: bold;
  }
}
</style>
