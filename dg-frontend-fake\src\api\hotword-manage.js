import { get, post } from '@/http/request'
import { hotwordManage } from '@/api/PATH'

// 热词管理

// 分类
export function getClassTree(formData) {
  return post(hotwordManage.classTree, { data: JSON.stringify(formData) })
}

export function addClass(formData) {
  return post(hotwordManage.addClass, { data: JSON.stringify(formData) })
}

export function updateClass(formData) {
  return post(hotwordManage.updateClass, { data: JSON.stringify(formData) })
}

export function deleteClass(formData) {
  return post(hotwordManage.deleteClass, { data: JSON.stringify(formData) })
}

// 组件
export function getList(formData) {
  return post(hotwordManage.list, { data: JSON.stringify(formData) })
}

export function addHotword(formData) {
  return post(hotwordManage.addHotword, { data: JSON.stringify(formData) })
}

export function updateHotword(formData) {
  return post(hotwordManage.updateHotword, { data: JSON.stringify(formData) })
}

export function deleteHotword(formData) {
  return post(hotwordManage.deleteHotword, { data: JSON.stringify(formData) })
}

export function importHotword(id, formData) {
  return post(hotwordManage.importHotword, formData, { typeId: id, moduleType: formData.moduleType }, { 'Content-Type': 'multipart/form-data' })
}

export function exportHotword(formData) {
  return post(hotwordManage.exportHotword, null, formData)
}

export function generateModel(formData) {
  return post(hotwordManage.generateModel, null, formData)
}
