import { get, post } from '@/http/request'
import { patternTemplate } from '@/api/PATH'

// 投资者句式模板
export function getList(formData) {
  return post(patternTemplate.list, formData)
}

export function getDetail(id) {
  return post(patternTemplate.detail, { investorId: id })
}

export function addPattern(formData) {
  return post(patternTemplate.add, { data: JSON.stringify(formData) })
}

export function updatePattern(formData) {
  return post(patternTemplate.update, { data: JSON.stringify(formData) })
}

export function deletePattern(formData) {
  return post(patternTemplate.delete, { data: JSON.stringify(formData) })
}