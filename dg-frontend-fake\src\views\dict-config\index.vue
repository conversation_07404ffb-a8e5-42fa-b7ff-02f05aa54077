<template>
  <base-card
    id="dict-config"
    class="y-page">
    <div class="y-container no-padding">
      <div class="header y-bar">
        <h2 class="y-title">{{ $t('数据字典') }}</h2>
        <span>{{ $t('关键字') }}</span>
        <el-input
          v-model="formData.keyword"
          v-trim
          size="small"
          style="width: 220px"
          :placeholder="'请输入名称关键字搜索'"
          clearable></el-input>
        <el-button
          v-debounce="{ evtHandler: () => fetchData(null) }"
          class="mini"
          type="primary"
          icon="el-icon-search"
          >{{ $t('搜索') }}</el-button
        >
        <el-button
          class="mini"
          type="primary"
          icon="el-icon-plus"
          @click.native="openEdit(null, 'dict')"
          >{{ $t('新增') }}</el-button
        >
        <el-button
          v-debounce="{ evtHandler: syncRedis }"
          class="mini"
          type="primary"
          icon="el-icon-refresh"
          >{{ $t('同步') }}</el-button
        >
      </div>
      <div class="y-container--tight">
        <el-table
          :data="tableData"
          v-loading="tableLoading"
          v-reset-scroll="'div.el-table__body-wrapper'"
          highlight-current-row
          @row-click="handleRowClick"
          stripe
          height="100%"
          fit
          ref="table"
          style="width: 100%">
          <el-table-column
            type="index"
            label="序号"
            width="50">
          </el-table-column>
          <el-table-column
            :label="$t('数据项名称')"
            prop="NAME"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            :label="$t('编号')"
            prop="CODE"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            :label="$t('创建人')"
            prop="CREATE_ACC"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            :label="$t('修改人')"
            prop="UPDATE_ACC"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            :label="$t('是否启用')"
            prop="ENABLE_STATUS">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.ENABLE_STATUS"
                active-color="#0555CE"
                active-value="01"
                inactive-value="00"
                @change="handleSwitch(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="100"
            fixed="right">
            <template slot-scope="scope">
              <el-link
                type="primary"
                :underline="false"
                @click.native="openEdit(scope.row, 'dict')"
                >编辑</el-link
              >
              <el-link
                type="danger"
                :underline="false"
                @click.native="deleteDict(scope.row)"
                >删除</el-link
              >
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/no-info.png')"
            description="暂无信息"></el-empty>
        </el-table>
      </div>
      <pagination
        :current-page.sync="formData.pageIndex"
        :page-size.sync="formData.pageSize"
        :total="total"
        @page="fetchData"></pagination>
    </div>
    <aside-panel
      ref="asidePanel"
      :active-menu="activeMenu"
      @open-edit="openEdit($event, 'dictItem')"></aside-panel>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        :flag="editDrawerFlag"
        :active-menu="activeMenu"
        @close-edit="closeEdit"
        @update-data="handleUpdate"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getDictList, getDictDetail, updateDict, deleteDict, syncRedis } from '@/api/dict-config'
import AsidePanel from './components/AsidePanel'
import EditForm from './components/EditForm'

export default {
  name: 'DictConfig',
  components: {
    AsidePanel,
    EditForm,
  },
  data() {
    return {
      tableLoading: false,
      activeMenu: '',
      total: 0,
      formData: {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
        keyword: '',
      },
      tableData: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
      editDrawerFlag: '',
    }
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData(flag) {
      if (flag) {
        this.$refs.asidePanel?.fetchData()
        return
      }
      this.tableLoading = true
      const [err, res] = await getDictList(this.formData)
      if (res) {
        this.tableData = res.data
        this.total = res.totalRow
        this.$nextTick(() => {
          this.$refs.table.doLayout()
        })

        this.setActive()
      }
      this.tableLoading = false
    },
    handleUpdate(menu) {
      // 在修改字典ID或者新增字典时，主动选中该字典
      if (menu) {
        this.activeMenu = menu
      }
      this.fetchData(!menu)
    },
    async deleteDict(data) {
      try {
        await this.$confirm('此操作将永久删除选中字典, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const [err, res] = await deleteDict(data.ID)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    async syncRedis() {
      const [err, res] = await syncRedis()
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {},
        })
      }
    },
    async handleSwitch(data) {
      const { ID, NAME, CODE, SORT_NUM, ENABLE_STATUS, BAKUP } = data
      const payload = { ID, NAME, CODE, SORT_NUM, ENABLE_STATUS, BAKUP }

      const [err, res] = await updateDict(payload)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {},
        })
      } else {
        this.fetchData()
      }
    },
    openEdit(data, flag) {
      const titleMap = {
        dict: ['新增字典分类', '编辑字典分类'],
        dictItem: ['新增字典项', '编辑字典项'],
      }
      if (data) {
        this.editDrawerTitle = titleMap[flag][1]
        this.editDrawerData = data
      } else {
        this.editDrawerTitle = titleMap[flag][0]
        this.editDrawerData = null
      }
      this.editDrawerFlag = flag
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    handleRowClick(row) {
      this.activeMenu = row.CODE
    },
    setActive() {
      if (this.activeMenu) {
        let find = this.tableData.find((item) => item.CODE === this.activeMenu)
        if (find) {
          this.$nextTick(() => {
            this.$refs.table.setCurrentRow(find)
          })
        }
      } else {
        if (this.tableData[0]) {
          this.activeMenu = this.tableData[0].CODE
          this.$nextTick(() => {
            this.$refs.table.setCurrentRow(this.tableData[0])
          })
        }
      }
    },
  },
}
</script>

<style lang="scss">
#dict-config {
  @include flex-row;
  background-color: transparent;

  .header {
    justify-content: flex-start;
    border-bottom: 1px solid $borderColor;
  }

  > .y-container {
    background-color: $bgColor;
    border-radius: 4px;
    overflow: hidden;

    &:nth-child(1) {
      flex: 2;
      margin-right: 16px;
    }

    &:nth-child(2) {
      flex: 1;
    }

    > .y-container--tight {
      padding-top: 16px;
      padding-bottom: 0;
      border-bottom: 1px solid $borderColor;

      .el-table {
        &::before {
          content: none;
        }

        tr.current-row > td.el-table__cell {
          border: 1px solid $themeColor;
          border-left: none;
          border-right: none;

          &:first-child {
            border-left: 1px solid $themeColor;
          }

          &:last-child {
            border-right: 3px solid $themeColor;
          }
        }

        .el-link {
          + .el-dropdown {
            margin-left: 16px;
            color: $themeColor;
            cursor: pointer;
          }
        }
      }
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }
}
</style>
