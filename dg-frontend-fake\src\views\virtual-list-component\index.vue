<template>
  <base-card
    id="virtual-list-component"
    class="y-page">
    <aside-bar
      ref="asideBar"
      @set-current="setCurrent($event)"></aside-bar>
    <div class="y-container no-padding">
      <div class="y-bar">
        <h2 class="y-title">诉求对象列表</h2>
        <span>关键字</span>
        <el-input
          v-model="keyword"
          v-trim
          size="small"
          style="width: 220px"
          clearable
          placeholder="请输入关键字进行搜索"></el-input>
        <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
        <el-button
          class="mini"
          type="primary"
          @click="downloadTemplate">
          <i class="el-icon-download"></i>
          下载模板
        </el-button>
        <el-upload
          ref="upload"
          action=""
          accept=".xlsx, .xls"
          :auto-upload="false"
          :show-file-list="false"
          :multiple="false"
          :on-change="importObject">
          <el-button
            class="mini"
            type="primary"
            plain
            style="margin-left: 8px">
            <svg-icon icon="enter"></svg-icon>
            批量导入
          </el-button>
        </el-upload>
        <!-- <el-button
          class="mini"
          type="primary">
          <svg-icon icon="robot-plus"></svg-icon>
          智能生成
        </el-button> -->
      </div>
      <el-empty
        v-if="!objectList || objectList?.length === 0"
        :image="require('@/assets/images/no-info.png')"
        description="暂无信息"></el-empty>
      <virtual-list
        v-else
        class="object-item-wrapper"
        :list="objectList"
        :key-name="'APPEAL_INFO_ID'"
        :item-height="186"
        :gap="16"
        layout="grid"
        :gridStyle="{
          'grid-auto-rows': '186px',
          'grid-template-columns': 'repeat(auto-fill, minmax(416px, 1fr))',
        }">
        <template v-slot:item="{ data }">
          <object-card
            :data="data"
            :active-menu="activeMenu"
            @edit-object="openEdit(data)"
            @delete-object="deleteObject(data)"></object-card>
        </template>
      </virtual-list>
    </div>
    <el-drawer
      title="编辑诉求对象"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getObjectList, importObject, deleteObject } from '@/api/object-manage'

export default {
  name: 'VirtualListComponent',
  components: {
    'aside-bar': () => import('./components/AsideBar'),
    'object-card': () => import('./components/ObjectCard'),
    'edit-form': () => import('./components/EditForm'),
    'virtual-list': () => import('@/components/VirtualList'),
  },
  props: {},
  data() {
    return {
      activeMenu: null,
      keyword: '',
      formData: {
        pageSize: 9999,
        pageIndex: 1,
        pageType: 3,
      },
      objectList: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {},
  watch: {
    activeMenu: {
      handler(menu) {
        if (menu || menu === 0) {
          this.formData = {
            pageSize: 9999,
            pageIndex: 1,
            pageType: 3,
          }
          this.fetchData()
        }
      },
      deep: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    async fetchData() {
      let loadingInstance = this.$loading({ target: '.object-item-wrapper' })

      const payload = {
        appealName: this.keyword,
        appealType: this.activeMenu[0],
      }
      Object.assign(payload, this.formData)
      const [err, res] = await getObjectList(payload)
      if (res) {
        this.objectList = res.data
      }

      this.$nextTick(() => {
        loadingInstance.close()
      })
    },
    async importObject(file, fileList) {
      if (
        file.raw.type !== 'application/vnd.ms-excel' &&
        file.raw.type !== 'application/x-excel' &&
        file.raw.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ) {
        this.$message({
          message: '文件类型不正确，请重新上传',
          type: 'error',
          duration: 800,
        })
        return false
      }

      const payload = {
        appealType: this.activeMenu?.[0],
        file: file.raw,
      }

      const [err, res] = await importObject(payload)
      if (!err && res?.msg.includes('成功')) {
        this.$message({
          message: res.msg,
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      } else if (!err) {
        this.$message({
          message: res.msg,
          type: 'error',
          duration: 800,
          onClose: () => {},
        })
      }
    },
    async deleteObject(data) {
      try {
        await this.$confirm('此操作将永久删除选中诉求对象, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const payload = {
        appealId: data.APPEAL_INFO_ID,
        contentBefore: data,
      }
      const [err, res] = await deleteObject(payload)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    openEdit(data) {
      if (data) {
        this.editDrawerData = data
      } else {
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    downloadTemplate() {
      window.open(process.env.VUE_APP_BASE_API + '/dg-portal/template/template_appeal_info.xlsx', '_blank')
    },
    setCurrent(menu) {
      this.activeMenu = menu
    },
  },
}
</script>

<style lang="scss">
#virtual-list-component {
  @include flex-row;
  background-color: transparent;

  > .aside-bar {
    margin-right: 16px;
    border-radius: 4px;
    background-color: $bgColor;
  }

  > .y-container {
    width: calc(100% - 304px);
    min-width: 685px;

    .y-bar {
      justify-content: flex-start;
      background-color: $bgColor;
      border-radius: 4px;
    }
  }

  .object-item-wrapper {
    @include full;
    margin-top: 16px;
  }
}
</style>
