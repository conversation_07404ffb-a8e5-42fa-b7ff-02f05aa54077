# 项目概要

AI中台

### 主要依赖

- vue@2.7.16
- vue/cli@5.0.8
- element-ui:@2.15.13
- sass@1.26.2
- axios@1.4.0

### 启动

```bash
#dev-server启动
vue serve

#打包portal
npm run build

#打包login
npm run build:login

#快速构建页面/组件/模块
npm run new

```

### 请求封装

1. 直接调$axios

```js
this.$axios(...)
```

2. 使用封装接口

```js
const [err, res] = await api(params)
// 或者
api(params).then(([err, res]) => {})
```

### 待办事项

20231027

- [x] 请求统一加编码格式
- [x] 数据处理组件-新增两个字段：serviceMaxLine，gwKeyName
- [x] 重测有 bug 的接口
- [x] Excel 数据集接口对接
- [x] 王丹开发接口删除编辑加 contentBefore
- [x] textarea 设置大小

20231030

- [x] 数据集删除、查看不行
- [x] 数据集日期过滤测试
- [x] 目标数据源删除貌似和数据集删除耦合了
- [x] 检查诉求对象 BUG 改了没

20231204

- [x] bug：标注列表数据为空塌陷
- [x] 标注列表 NEW 字段显示
- [x] 行政处罚决定书页面
- [x] 新增单个诉求对象的接口对接
- [x] 新增单个人员的接口

20231206

- [x] etl 执行记录标题问题
- [x] etl 新增没有 etlId
- [x] 新增返回临时 etlId 用于轮询
- [x] etl 流程加字段校验
- [x] excel 字段后续流程取填写的
- [x] 新增提交入参去掉 node
- [x] sql 入参错误
- [x] 新增页面直接执行也要轮询
- [x] 执行日志加分页
- [x] 执行日志加筛选项
- [x] 按天执行也有批次处理数据量
- [x] etl 列表分页

20231207

- [x] 定时任务去掉执行前后
- [x] 调度配置样式优化
- [x] 数据源展示去掉增量同步字段
- [x] 每层组件都要接收到之前新增的字段
- [x] etl 数据预览优化
- [x] 避免每层组件 column 叠加后有重复

20231208

- [x] 列表翻页滚动条回滚
- [x] 拖组件图标组件没变
- [x] 字典维护页面，优先级低
- [x] 执行记录详情，优先级低
- [x] 目标数据源配置表
- [x] 查询统计页面

20231208

- [x] 数据集确定接口反馈+超时 3min
- [x] 工单详情行数优化
- [x] 目标数据源配置表区分新增修改（接口、入参）
- [x] 重新打包 login
- [x] 刘稳接口修改
- [x] ETL 流程 excel 字段获取接口修改

20231213

- [x] ETL 流程可编辑开关
- [x] 数据查询 query 样式优化以及筛选项修改
- [x] 训练标注动态查询
- [x] 改模板 url
- [x] 导出逻辑

20231213
- [x] 修改密码校验

20231219
- [x] ETL中断执行
- [x] ETL映射检查

20231225
- [x] 所有页面的favicon
- [x] 字典同步缓存

20231229
- [x] 回填后选项没出来

20240305
优先级最低
- [x] 替换Pagination
- [x] 替换EmptyWrapper
- [ ] 页面内的按需引入替换

20240313
- [x] 数据处理组件-记录模型的版本ID
- [x] 数据处理组件页面，服务名不展示、换成版本号
- [x] 左侧菜单需要根据行业做分类展示
- [x] 本地开发登录优化
- [x] 事务组件
- [x] 组件侧边栏筛选

20240314
- [x] ETL预览列表，关系目标源取column, 其他取mapColumn
- [ ] 数据治理页面动态菜单

20240315
- [ ] 执行记录详情少字段（取消）
- [x] 数据查询详情少字段名
- [x] 数据查询动态表头

20240326
- [x] 字典列表修改ID后，丢失选中
- [ ] 字典项列表新增后再新增，新增表单里填的是当前字典的数据（不复现）

20240410
- [x] 流程外组件拉至标准线，会闪回原位bug