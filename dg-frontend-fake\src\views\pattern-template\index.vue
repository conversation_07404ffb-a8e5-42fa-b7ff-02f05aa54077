<template>
  <base-card
    id="pattern-template"
    class="y-page">
    <div class="y-bar">
      <h2 class="y-title">投资者句式模板配置</h2>
      <span>启用状态</span>
      <el-select
        v-model="formData.isUsed"
        placeholder="请选择启用状态"
        clearable
        size="small"
        style="width: 160px">
        <el-option
          label="启用"
          value="1"></el-option>
        <el-option
          label="停用"
          value="0"></el-option>
        <el-option
          label="全部"
          value=""></el-option>
      </el-select>
      <span>关键字</span>
      <el-input
        v-model="formData.key"
        v-trim
        size="small"
        style="width: 220px"
        clearable
        placeholder="请输入关键字进行搜索"></el-input>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
      <el-button
        class="mini"
        type="primary"
        @click.native="openEdit(null)">
        <svg-icon icon="add"></svg-icon>
        添加句式模板
      </el-button>
    </div>
    <empty-wrapper
      class="y-container--tight"
      :toggle="!patternList || patternList?.length === 0"
      v-loading="loading">
      <div class="template-item-wrapper y-card-wrapper y-container--tight no-padding">
        <config-card
          v-for="item in patternList"
          :key="item.INVESTOR_ID"
          :data="item">
          <template #left>
            <el-tooltip
              effect="dark"
              :content="item.TEMPLATE"
              placement="top">
              <h5 class="title">
                {{ item.TEMPLATE }}
              </h5>
            </el-tooltip>
            <p class="desc">
              <span class="prefix">配置说明：</span
              >指用于获取投资者沟通和报告数据的标准化句式结构，以确保信息的清晰、一致和易于识别。这些句式模板提供了一种框架，辅助系统获取工单数据中的投资者信息。
            </p>
            <div class="tags">
              <div class="tag">更新时间：{{ item.UPDATE_TIME }}</div>
            </div>
          </template>
          <template #right>
            <div class="switch">
              <span class="prefix">启用状态：</span>
              <el-switch
                v-model="item.IS_USED"
                active-color="#52C41A"
                active-value="1"
                inactive-value="0"
                @change="handleSwitch(item)">
              </el-switch>
            </div>
            <div class="btn-set">
              <el-button
                class="mini"
                type="danger"
                plain
                size="small"
                @click.native="deletePattern(item)"
                >删除</el-button
              >
              <el-button
                class="mini"
                type="primary"
                size="small"
                @click.native="openEdit(item)"
                >配置</el-button
              >
            </div>
          </template>
        </config-card>
      </div>
    </empty-wrapper>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getList, updatePattern, deletePattern } from '@/api/pattern-template'
import EditForm from './components/EditForm.vue'
import ConfigCard from '@/components/ConfigCard'

export default {
  name: 'PatternTemplate',
  components: {
    ConfigCard,
    EditForm,
  },
  props: {},
  data() {
    return {
      loading: false,
      formData: {
        pageSize: 999,
        pageIndex: 1,
        pageType: 3,
        key: '',
        isUsed: '',
      },
      patternList: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true

      const [err, res] = await getList(this.formData)
      if (res) {
        this.patternList = res.data
      }

      this.loading = false
    },
    async deletePattern(data) {
      try {
        await this.$confirm('此操作将永久删除选中模板, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const payload = {
        investorId: data.INVESTOR_ID,
        contentBefore: data,
      }
      const [err, res] = await deletePattern(payload)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    async handleSwitch(data) {
      const payload = {
        investorId: data.INVESTOR_ID,
        template: data.TEMPLATE,
        isUsed: data.IS_USED,
      }

      const [err, res] = await updatePattern(payload)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {},
        })
      } else {
        this.fetchData()
      }
    },
    openEdit(data) {
      if (data) {
        this.editDrawerTitle = '配置句式模板'
        this.editDrawerData = data
      } else {
        this.editDrawerTitle = '添加句式模板'
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
  },
}
</script>

<style lang="scss">
#pattern-template {
  @import '@/assets/style/modules/config-card-page.scss';
  background-color: transparent;
}
</style>
