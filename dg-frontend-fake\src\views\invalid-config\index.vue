<template>
  <base-card
    id="invalid-config"
    class="y-page">
    <aside-bar
      ref="asideBar"
      @set-current="setCurrent($event)"></aside-bar>
    <div class="y-container no-padding">
      <div class="y-bar">
        <h2 class="y-title">投诉类答复样例</h2>
        <span>关键字</span>
        <el-input
          v-model="keyword"
          v-trim
          size="small"
          style="width: 220px"
          clearable
          placeholder="请输入关键字进行搜索"></el-input>
        <el-button
          v-debounce="fetchData"
          class="mini"
          type="primary">
          <i class="el-icon-search"></i>
          搜索
        </el-button>
        <el-button
          class="mini"
          type="primary"
          @click.native="openEdit(null)">
          <svg-icon icon="add"></svg-icon>
          添加样例
        </el-button>
      </div>
      <empty-wrapper
        class="y-container--tight"
        :toggle="!answerList || answerList?.length === 0"
        v-loading="loading">
        <div class="answer-item-wrapper y-container--tight no-padding">
          <div
            class="answer-item"
            v-for="item in answerList"
            :key="item.ANSWER_ID">
            <h5 class="name">{{ item.ANSWER_NAME }}</h5>
            <p class="content">{{ item.ANSWER_CONTENT }}</p>
            <div class="btn-set">
              <el-button
                class="mini"
                type="danger"
                plain
                @click.native="deleteAnswer(item)">
                删除
              </el-button>
              <el-button
                class="mini"
                type="primary"
                plain
                @click.native="openEdit(item)">
                编辑
              </el-button>
            </div>
          </div>
        </div>
      </empty-wrapper>
    </div>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        :activeMenu="activeMenu"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getAnswerList, deleteAnswer } from '@/api/invalid-config'
import AsideBar from './components/AsideBar'
import EditForm from './components/EditForm'

export default {
  name: 'InvalidConfig',
  components: {
    AsideBar,
    EditForm,
  },
  props: {},
  data() {
    return {
      loading: false,
      activeMenu: null,
      keyword: '',
      answerList: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {},
  watch: {
    activeMenu(menu) {
      if (menu || menu === 0) {
        this.fetchData()
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true

      const payload = {
        answerName: this.keyword,
        typeId: this.activeMenu,
      }

      const [err, res] = await getAnswerList(payload)
      if (res) {
        this.answerList = res.data
      }

      this.loading = false
    },
    async deleteAnswer(data) {
      try {
        await this.$confirm('此操作将永久删除选中样例, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }
      const [err, res] = await deleteAnswer(data.ANSWER_ID)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    setCurrent(item) {
      this.activeMenu = item
    },
    openEdit(data) {
      if (data) {
        this.editDrawerTitle = '配置样例'
        this.editDrawerData = data
      } else {
        this.editDrawerTitle = '添加样例'
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
  },
}
</script>

<style lang="scss">
#invalid-config {
  @include flex-row;

  > .aside-bar {
    border-right: 1px solid $borderColor;
  }

  > .y-container {
    width: calc(100% - 288px);

    .y-bar {
      justify-content: flex-start;
      border-bottom: 1px solid $borderColor;
    }
  }

  .answer-item-wrapper {
    .answer-item {
      position: relative;
      margin: 16px 24px;
      margin-bottom: 0;
      padding-bottom: 16px;
      width: calc(100% - 48px);
      border-bottom: 1px solid $borderColor;

      .name {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        color: $txtColor;
      }

      .content {
        @include text-overflow(3);
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        color: $txtColor-light;
      }

      .btn-set {
        position: absolute;
        top: 0;
        right: 0;
      }
    }
  }
}
</style>
