<template>
  <div
    class="node-item"
    ref="node"
    :class="nodeClass"
    :style="flowNodeContainer"
    v-click-outside="setNotActive"
    @click="setActive"
    @mouseenter="showAnchor"
    @mouseleave="hideAnchor"
    @dblclick.prevent="editNode"
    @contextmenu.prevent="onContextmenu">
    <!-- <div class="log-wrap">
      <img :src="node.logImg" alt="">
    </div> -->
    <div
      class="node-count"
      v-if="runInfo">
      <span style="color: #0555ce">{{ runInfo.loadCount || 0 }}</span> / <span style="color: #52c41a">{{ runInfo.succCount || 0 }}</span> /
      <span style="color: #f03838">{{ runInfo.failCount || 0 }}</span>
    </div>
    <div class="nodeName">{{ node.name }}</div>
    <!--连线用--//触发连线的区域-->
    <div
      class="node-anchor anchor-top"
      v-show="mouseEnter"></div>
    <div
      class="node-anchor anchor-right"
      v-show="mouseEnter"></div>
    <div
      class="node-anchor anchor-bottom"
      v-show="mouseEnter"></div>
    <div
      class="node-anchor anchor-left"
      v-show="mouseEnter"></div>
  </div>
</template>

<script>
let clock
import ClickOutside from 'vue-click-outside'
export default {
  name: 'nodeItem',
  props: {
    node: Object,
    runInfo: Object,
    editable: Boolean,
  },
  directives: {
    ClickOutside,
  },
  computed: {
    nodeClass() {
      return {
        active: this.isActive || this.isSelected,
        [this.classMap[this.node.nodeType]]: true,
        [this.node.modelType === '1' ? 'affair' : 'process']: this.node.nodeType === '6',
      }
    },
    // 节点容器样式
    flowNodeContainer: {
      get() {
        return {
          top: this.node.top,
          left: this.node.left,
        }
      },
    },
  },
  data() {
    return {
      mouseEnter: false,
      isActive: false,
      isSelected: false,
      classMap: {
        // 数据源
        1: 'type1',
        // 数据预处理组件
        2: 'type2',
        3: 'type2',
        4: 'type2',
        5: 'type2',
        8: 'type2',
        9: 'type2',
        10: 'type2',
        11: 'type2',
        12: 'type2',
        // 数据治理组件-事务组件
        6: 'type3',
        // 目标源
        7: 'type4',
      },
    }
  },
  methods: {
    showAnchor() {
      this.mouseEnter = true
    },
    hideAnchor() {
      this.mouseEnter = false
    },
    onContextmenu() {
      if (!this.editable) {
        return false
      }
      this.$contextmenu({
        items: [
          {
            label: '删除',
            disabled: false,
            icon: '',
            onClick: () => {
              this.deleteNode()
            },
          },
        ],
        event,
        customClass: 'custom-class',
        zIndex: 9999,
        minWidth: 180,
      })
    },
    setActive() {
      clock && clearTimeout(clock)
      clock = setTimeout(() => {
        this.$emit('sclick', this.node)
      }, 300)
      if (window.event.ctrlKey) {
        this.isSelected = !this.isSelected
        return false
      }
      this.isActive = true
      this.isSelected = false
      setTimeout(() => {
        this.$emit('changeLineState', this.node.id, true)
      }, 0)
    },
    setNotActive() {
      if (!window.event.ctrlKey) {
        this.isSelected = false
      }
      if (!this.isActive) {
        return
      }
      this.$emit('changeLineState', this.node.id, false)
      this.isActive = false
    },
    editNode() {
      console.log(this.node)
      this.$emit('selectNode', this.node)
      return
    },
    deleteNode() {
      this.$emit('deleteNode', this.node)
    },
  },
}
</script>

<style lang="scss" scoped>
$nodeSize: 20px;

.node-count {
  position: absolute;
  z-index: 2;
  top: 0;
  left: 50%;
  padding: 2px 5px;
  margin-top: -10px;
  width: max-content;
  border: 1px solid #ddd;
  border-radius: 2px;
  background: #fff;
  transform: translateX(-50%);
}

.node-item::v-deep {
  position: absolute;
  display: flex;
  box-sizing: border-box;
  justify-content: center;
  align-items: center;
  padding: 60px 4px 0;
  width: 100px;
  height: 100px;
  font-size: 12px;
  line-height: 14px;
  color: #666;
  text-align: center;
  border: 2px solid #d1dbe7;
  border-radius: 15px;
  background-color: #e8edf9;
  background-repeat: no-repeat;
  background-position: center 4px;
  background-size: 80px;
  cursor: move;
  z-index: 9995;

  &.active {
    color: #0555ce;
  }

  &.type1 {
    background-image: url(@/assets/images/t1.svg);
  }

  &.type2 {
    background-image: url(@/assets/images/t2.svg);
  }

  &.type3 {
    &.process {
      background-image: url(@/assets/images/t3-1.svg);
    }

    &.affair {
      background-image: url(@/assets/images/t3-2.svg);
    }
  }

  &.type4 {
    background-image: url(@/assets/images/t4.svg);
  }

  &:hover {
    z-index: 9998;
  }

  .log-wrap {
    width: 40px;
    height: 40px;
    position: absolute;
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
    border-right: 1px solid #b7b6b6;
  }
  .nodeName {
    flex-grow: 1;
    width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .node-anchor {
    display: flex;
    position: absolute;
    width: $nodeSize;
    height: $nodeSize;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    cursor: crosshair;
    z-index: 9999;
    background: -webkit-radial-gradient(sandybrown 10%, white 30%, #9a54ff 60%);
  }
  .anchor-top {
    top: calc((#{$nodeSize} / 2) * -1);
    left: 50%;
    margin-left: calc((#{$nodeSize} / 2) * -1);
  }
  .anchor-right {
    top: 50%;
    right: calc((#{$nodeSize} / 2) * -1);
    margin-top: calc((#{$nodeSize} / 2) * -1);
  }
  .anchor-bottom {
    bottom: calc((#{$nodeSize} / 2) * -1);
    left: 50%;
    margin-left: calc((#{$nodeSize} / 2) * -1);
  }
  .anchor-left {
    top: 50%;
    left: calc((#{$nodeSize} / 2) * -1);
    margin-top: calc((#{$nodeSize} / 2) * -1);
  }
}
.active {
  border: 2px solid $themeColor;
  box-shadow: 0px 5px 9px 0px rgba(0, 0, 0, 0.5);
}
</style>
