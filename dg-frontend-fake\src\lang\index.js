import Vue from 'vue'
import VueI18n from 'vue-i18n'
import elementEN from 'element-ui/lib/locale/lang/en'
import elementZH from 'element-ui/lib/locale/lang/zh-CN'
// 引入自定义中文包
import customZH from './zh-CN'
// 引入自定义英文包
import customEN from './en-US'

Vue.use(VueI18n)

// 创建国际化插件的实例
export default new VueI18n({
    // 指定语言类型
    locale: localStorage.getItem('language') || 'zh',
    messages: {
        en: {
            ...elementEN, // 将饿了么的英文语言包引入
            ...customEN   // 将自定义英文包加入
        },
        zh: {
            ...elementZH, // 将饿了么的中文语言包引入
            ...customZH   // 将自定义中文包加入
        }
    },
    silentTranslationWarn: true
})