<template>
  <div
    id="model-presentation"
    class="y-container no-padding">
    <div class="header y-title">工单诉求解析助手(基于Deepseek)</div>
    <base-card class="y-page y-container--tight">
      <div
        class="y-container"
        style="padding-top: 0">
        <div class="y-bar step-bar">
          <div class="step-bar_item y-title">1.诉求选取</div>
          <i class="el-icon-right"></i>
          <div class="step-bar_item y-title">2.请输入诉求</div>
          <i class="el-icon-right"></i>
          <div class="step-bar_item y-title">解析结果</div>
        </div>
        <div
          class="card-list-container y-container--tight no-padding"
          style="overflow: visible">
          <el-card>
            <div
              slot="header"
              class="clearfix">
              <el-button
                @click="() => prompt(1)"
                :loading="promptLoading"
                type="primary"
                size="mini"
                style="float: right"
                >内容提取</el-button
              >
            </div>
            <el-input
              type="textarea"
              placeholder="请输入"
              v-model="textarea"
              resize="none"
              style="height: 100%">
            </el-input>
          </el-card>
          <el-card class="main-content-card">
            <template>
              <div
                class="y-bar"
                style="padding: 0">
                <h4 class="y-title">标题</h4>
                <!-- <el-button
                  @click="prompt"
                  :disabled="step !== 2"
                  :loading="promptLoading && step === 2"
                  type="primary"
                  size="mini"
                  >关键字解析</el-button
                > -->
              </div>

              <el-divider></el-divider>
              <div class="content">{{ data.title || '待提取' }}</div>
              <h4
                class="y-title"
                style="margin-top: 64px">
                内容
              </h4>
              <el-divider></el-divider>
              <div class="content">{{ data.summary || '待提取' }}</div>
            </template>
          </el-card>
          <el-card>
            <div class="card-grid">
              <div
                class="card-grid-item"
                style="grid-area: a">
                <h4 class="y-title">关键词解析：</h4>
                {{ data.keyword }}
              </div>
              <div
                class="card-grid-item"
                style="grid-area: b">
                <h4 class="y-title">地址解析：</h4>

                <div v-if="data.address">
                  {{ data.address.location }}
                  <data class="flex-wrap-item">
                    <span class="content">省：{{ data.address.province }}</span>
                    <span class="content">市：{{ data.address.city }}</span>
                    <span class="content">区：{{ data.address.district }}</span>
                    <span class="content">街道：{{ data.address.street }}</span>
                  </data>
                  <data class="flex-wrap-item">
                    <span class="content">经度：{{ data.address.lon }}</span>
                    <span class="content">纬度：{{ data.address.lat }}</span>
                  </data>
                </div>
              </div>
              <div
                class="card-grid-item"
                style="grid-area: c">
                <h4 class="y-title">时间解析：</h4>
                <div class="content">{{ data.time }}</div>
              </div>
              <div
                class="card-grid-item"
                style="grid-area: d">
                <h4 class="y-title">工单类型解析：</h4>
                <div class="content">{{ data.type }}</div>
              </div>
              <div
                class="card-grid-item"
                style="grid-area: e">
                <h4 class="y-title">诉求归口解析：</h4>
                <div class="content">{{ data.question }}</div>
              </div>
              <div
                class="card-grid-item"
                style="grid-area: f">
                <h4 class="y-title">涉事主体解析：</h4>
                <div class="content">{{ data.subject }}</div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
      <!-- <div class="y-container">
        <div class="y-bar">
          <div class="y-title">任务工单列表</div>
          <el-button
            @click="fetchData"
            type="primary"
            size="small"
            >随机抽取</el-button
          >
        </div>
        <div
          class="y-container no-padding"
          style="flex: 0 0 400px">
          <el-table
            :data="tableData"
            v-loading="tableLoading"
            v-reset-scroll="'div.el-table__body-wrapper'"
            stripe
            height="100%"
            fit
            ref="table"
            style="width: 100%">
            <el-table-column
              prop="id"
              label="事务编号"
              width="200"
              show-overflow-tooltip>
            </el-table-column>
            <el-table-column
              prop="title"
              label="标题"
              width="280"
              show-overflow-tooltip>
            </el-table-column>
            <el-table-column
              prop="content"
              label="内容"
              min-width="400"
              show-overflow-tooltip>
            </el-table-column>
            <el-table-column
              prop="time"
              label="登记时间"
              width="180"
              show-overflow-tooltip>
            </el-table-column>
            <el-table-column
              label="操作"
              width="100"
              fixed="right">
              <template slot-scope="scope">
                <el-link
                  type="primary"
                  :underline="false"
                  @click="textarea = scope.row.content"
                  >内容</el-link
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <pagination
          :current-page.sync="formData.pageIndex"
          :page-size.sync="formData.pageSize"
          :total="total"
          @page="fetchData"
          style="align-self: flex-end"></pagination>
      </div> -->
    </base-card>
  </div>
</template>

<script>
import { prompt, getOrderList } from '@/api/model-presentation'
export default {
  name: 'ModelPresentation',
  components: {},
  props: {},
  data() {
    return {
      step: 1,
      promptLoading: false,
      tableLoading: false,
      textarea: '',
      data: {},
      formData: {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
      },
      tableData: [],
      total: 0,
    }
  },
  watch: {},
  created() {
    // this.fetchData()
  },
  mounted() {},
  methods: {
    async prompt(flag = 0) {
      // if (flag === 1) {
      //   this.step = 1
      //   this.data = {}
      // }
      this.promptLoading = true
      const payload = {
        content: this.textarea,
        // step: String(this.step),
      }
      const loading = this.$loading({
        lock: true,
        text: '正在提取...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)',
      })
      const [err, res] = await prompt(payload)
      if (res) {
        // this.step++ && (this.step = this.step > 2 ? 1 : this.step)
        Object.assign(this.data, res.data || {})
        this.$message.success('提取成功')
      }
      this.promptLoading = false
      loading.close()
    },
    async fetchData() {
      this.tableLoading = true
      const [err, res] = await getOrderList(this.formData)
      if (res?.data) {
        this.tableData = res.data.map((item) => ({
          id: item.ORDER_ID,
          title: item.REQUEST_TOPIC,
          content: item.REQUEST_CONTENT,
          time: item.CREATE_DATE,
        }))
        this.total = res.data.total
      }
      this.tableLoading = false
    },
  },
}
</script>

<style lang="scss">
#model-presentation {
  .header {
    @include flex-center;
    padding: 0 16px;
    height: 56px;
    width: 100%;
    font-size: 22px;
    background: linear-gradient(135deg, #1890ff, #0555ce);
    color: $txtColor-reverse;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
      transform: translateX(-100%);
      animation: shimmer 3s infinite;
    }
  }

  .step-bar {
    margin: 16px 0 24px;

    .step-bar_item {
      flex: 0 0 240px;
      padding: 10px 24px;
      box-shadow: 0 8px 20px rgba(5, 85, 206, 0.35);
      border-radius: 12px;
      text-align: center;
      font-size: 20px;
      background: linear-gradient(135deg, #1890ff, #0555ce);
      color: $txtColor-reverse;
      font-weight: 700;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 24px rgba(5, 85, 206, 0.2);
      }
    }

    .el-icon-right {
      color: $themeColor;
      font-size: 30px;
      font-weight: 600;
      animation: bounce 2s infinite;
    }
  }

  .card-list-container {
    flex-direction: row;
    .el-card {
      flex: 1;
      height: 100%;
      transition: all 0.3s ease;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      }

      + .el-card {
        margin-left: 16px;
      }

      &:nth-child(1) {
        .el-card__body {
          height: calc(100% - 76px);
        }
      }

      &:nth-child(2) {
        .el-card__body {
          height: 100%;
        }
      }

      &:nth-child(3) {
        .el-card__body {
          height: 100%;
        }
      }
    }

    .card-grid {
      display: grid;
      grid-auto-columns: 1fr 1fr;
      grid-template-areas:
        'a a'
        'b b'
        'c d'
        'e f';
      gap: 16px;
      width: 100%;
      height: 100%;

      .card-grid-item {
        @include flex-row;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 16px;
        border-radius: 12px;
        background: linear-gradient(135deg, rgba(24, 144, 255, 0.05), rgba(5, 85, 206, 0.02));
        border: 1px solid rgba(5, 85, 206, 0.08);
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(135deg, rgba(24, 144, 255, 0.08), rgba(5, 85, 206, 0.04));
          transform: translateY(-2px);
        }
      }

      .y-title {
        font-size: 18px;
      }

      .content {
        font-size: 16px;
      }
    }

    .main-content-card {
      .y-title {
        font-size: 20px;
      }

      .content {
        font-size: 16px;
      }
    }

    .el-textarea .el-textarea__inner {
      height: 100%;
      font-size: 16px;
      color: $txtColor;
      border-radius: 8px;
      border: 1px solid rgba(5, 85, 206, 0.1);
      background: linear-gradient(to right, rgba(24, 144, 255, 0.02), rgba(5, 85, 206, 0.01));
      transition: all 0.3s ease;

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }

    .flex-wrap-item {
      @include flex-row;
      flex-wrap: wrap;
    }

    .el-button {
      padding: 8px 16px;
      font-size: 14px;
      background: linear-gradient(135deg, #1890ff, #0555ce);
      border-radius: 4px;
    }
  }
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(6px);
  }
}
</style>
