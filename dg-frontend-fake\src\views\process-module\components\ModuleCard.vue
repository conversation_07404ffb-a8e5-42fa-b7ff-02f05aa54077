<template>
  <base-card class="module-card" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
    <div class="info-wrapper">
      <div class="title-section">
        <h5 class="title">{{ data.DATA_COM_NAME }}</h5>
        <div :class="['tag', data.MODEL_TYPE === '1' ? 'affair' : 'process']">
          <span class="tag-text">{{ moduleTypeList[data.MODEL_TYPE] }}</span>
          <div class="tag-shine"></div>
        </div>
        <div class="title-underline"></div>
      </div>

      <div class="info-grid">
        <div class="model-id-item">
          <span class="info-label">模型版本ID</span>
          <span class="info-value">{{ data.MODEL_ID }}</span>
        </div>

        <div class="info-item desc-item">
          <span class="info-label">说明</span>
          <el-popover
            placement="top"
            width="360"
            trigger="hover"
            :content="data.SERVICE_DESC">
            <span slot="reference" class="info-value desc-text">{{ data.SERVICE_DESC }}</span>
          </el-popover>
        </div>
      </div>
    </div>

    <!-- 浮动操作按钮 -->
    <div class="floating-actions">
      <div class="action-btn delete-btn" @click="$emit('delete-module')" title="删除">
        <i class="el-icon-delete"></i>
      </div>
      <div class="action-btn edit-btn" @click="$emit('edit-module')" title="编辑">
        <i class="el-icon-edit"></i>
      </div>
    </div>
  </base-card>
</template>

<script>
import { MODULE_TYPE_LIST } from '@/utils/constant'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      moduleTypeList: MODULE_TYPE_LIST,
      isHovered: false,
    }
  },
  computed: {},
  methods: {
    handleMouseEnter() {
      this.isHovered = true
    },
    handleMouseLeave() {
      this.isHovered = false
    },
  },
}
</script>

<style lang="scss" scoped>
.module-card {
  @include full;
  @include flex-col;
  position: relative;
  padding: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform-style: preserve-3d;
  min-height: auto; // 移除固定高度限制

  // 基础阴影
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.08);

  // 悬停效果
  &:hover {
    transform: translateY(-5px) scale(1.01);
    box-shadow:
      0 15px 30px rgba(0, 0, 0, 0.12),
      0 6px 12px rgba(0, 0, 0, 0.08),
      0 0 0 1px rgba(255, 255, 255, 0.1);

    .tag {
      transform: translateY(-2px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);

      .tag-shine {
        opacity: 1;
        transform: translateX(100px);
      }
    }

    .title-underline {
      width: 100%;
    }

    .floating-actions {
      opacity: 1;
      transform: translateY(0);
    }
  }



  // 信息区域
  .info-wrapper {
    width: 100%;
    z-index: 2;
    flex: 1; // 让信息区域占据剩余空间

    .title-section {
      position: relative;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .title {
        font-size: 20px;
        font-weight: bold;
        line-height: 28px;
        color: $txtColor;
        margin-bottom: 8px;
        transition: color 0.3s ease;
        flex: 1;
        margin-right: 12px;
      }

      .tag {
        padding: 4px 10px;
        font-size: 11px;
        font-weight: 600;
        color: $txtColor-reverse;
        border-radius: 12px;
        text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
        flex-shrink: 0;

        .tag-text {
          position: relative;
          z-index: 2;
        }

        .tag-shine {
          position: absolute;
          top: 0;
          left: -100px;
          width: 100px;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.4),
            transparent
          );
          opacity: 0;
          transition: all 0.6s ease;
          transform: skewX(-20deg);
        }

        &.process {
          background: linear-gradient(135deg, #20c997 0%, #22b8cf 100%);
          box-shadow: 0 2px 8px rgba(32, 201, 151, 0.3);
        }

        &.affair {
          background: linear-gradient(135deg, #fcc419 0%, #ff922b 100%);
          box-shadow: 0 2px 8px rgba(252, 196, 25, 0.3);
        }
      }

      .title-underline {
        height: 2px;
        width: 40px;
        background: linear-gradient(90deg, $themeColor, $themeColor-light);
        border-radius: 2px;
        transition: width 0.4s ease;
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 8px;

      // 模型版本ID项 - 静态样式，无悬停效果
      .model-id-item {
        display: flex;
        flex-direction: column;
        background-color: rgba(110, 101, 198, 0.02);
        padding: 8px 10px;
        border-radius: 6px;

        .info-label {
          font-size: 11px;
          color: $txtColor-light;
          margin-bottom: 2px;
        }

        .info-value {
          font-size: 13px;
          font-weight: 500;
          color: $txtColor;
        }
      }

      // 其他信息项 - 有悬停效果
      .info-item {
        display: flex;
        flex-direction: column;
        background-color: rgba(110, 101, 198, 0.05);
        padding: 8px 10px;
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba(110, 101, 198, 0.1);
          transform: translateX(4px);
        }

        .info-label {
          font-size: 11px;
          color: $txtColor-light;
          margin-bottom: 2px;
        }

        .info-value {
          font-size: 13px;
          font-weight: 500;
          color: $txtColor;
        }

        &.desc-item {
          grid-column: 1 / -1;

          .desc-text {
            @include text-overflow(1);
          }
        }
      }
    }
  }

  // 浮动操作按钮
  .floating-actions {
    position: absolute;
    bottom: 16px;
    right: 16px;
    display: flex;
    gap: 10px;
    opacity: 0;
    transform: translateY(15px);
    transition: all 0.3s ease;
    z-index: 10;

    .action-btn {
      width: 34px;
      height: 34px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s ease;
      backdrop-filter: blur(5px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);

      i {
        font-size: 16px;
        color: white;
      }

      &:hover {
        transform: translateY(-4px);
      }

      &.edit-btn {
        background: linear-gradient(135deg, $themeColor, $themeColor-light);

        &:hover {
          box-shadow: 0 6px 16px rgba(110, 101, 198, 0.4);
        }
      }

      &.delete-btn {
        background: linear-gradient(135deg, #f03838, #ff5252);

        &:hover {
          box-shadow: 0 6px 16px rgba(240, 56, 56, 0.4);
        }
      }
    }
  }
}
</style>
