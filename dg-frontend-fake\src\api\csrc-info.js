import { get, post } from '@/http/request'
import { csrcInfo } from '@/api/PATH'

// 证监会组织信息

// 组织架构信息
export function getClassTree(id) {
  return post(csrcInfo.classTree, { orgId: id })
}

export function getClassList(id) {
  return post(csrcInfo.classList, { orgId: id })
}

export function addClass(formData) {
  return post(csrcInfo.addClass, { data: JSON.stringify(formData) })
}

export function updateClass(formData) {
  return post(csrcInfo.updateClass, { data: JSON.stringify(formData) })
}

export function deleteClass(formData) {
  return post(csrcInfo.deleteClass, { data: JSON.stringify(formData) })
}

// 人员信息
export function getStaffList(formData) {
  return post(csrcInfo.staffList, formData)
}

export function importStaff(formData) {
  return post(csrcInfo.importStaff, formData, null, { 'Content-Type': 'multipart/form-data' })
}

export function addStaff(formData) {
  return post(csrcInfo.addStaff, { data: JSON.stringify(formData) })
}

export function updateStaff(formData) {
  return post(csrcInfo.updateStaff, { data: JSON.stringify(formData) })
}

export function deleteStaff(formData) {
  return post(csrcInfo.deleteStaff, { data: JSON.stringify(formData) })
}
