<template>
  <base-card
    id="crontab"
    class="y-page">
    <div class="y-bar header">
      <h2 class="y-title">定时任务</h2>
      <el-button
        class="mini"
        type="primary"
        @click.native="openEdit(null)">
        <svg-icon icon="add"></svg-icon>
        创建任务
      </el-button>
    </div>
    <div class="y-bar search-bar">
      <span>关键字</span>
      <el-input
        v-model="formData.key"
        v-trim
        placeholder="请输入关键词进行搜索"
        size="small"
        clearable
        style="width: 220px"></el-input>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
    </div>
    <div class="y-container--tight">
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <el-table-column
          type="index"
          label="序号"
          width="50">
        </el-table-column>
        <el-table-column
          label="名称"
          prop="SCRIPT_NAME"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="命令/脚本"
          prop="EXE_COMMAND"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="状态"
          prop="STATE">
          <template slot-scope="scope">
            <el-tag
              size="small"
              :type="getStatus(scope.row.STATE, 'type')"
              >{{ getStatus(scope.row.STATE, 'text') }}</el-tag
            >
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="执行前命令"
          prop="PRE_COMMAND"
          width="100"
          show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.PRE_COMMAND.trim() ? scope.row.PRE_COMMAND : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="执行后命令"
          prop="SUB_COMMAND"
          width="100"
          show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.SUB_COMMAND.trim() ? scope.row.SUB_COMMAND : '-' }}</span>
          </template>
        </el-table-column> -->
        <el-table-column
          label="定时规则"
          prop="CRON_TAB"
          width="100">
        </el-table-column>
        <el-table-column
          label="最后运行时间"
          prop="LAST_RUNTIME"
          width="150">
        </el-table-column>
        <el-table-column
          label="下次运行时间"
          prop="NEXT_RUNTIME"
          width="150">
        </el-table-column>
        <el-table-column
          label="运行日志"
          prop="LOG"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="操作"
          width="150"
          fixed="right">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :underline="false"
              :disabled="scope.row.STATE != '2'"
              :key="scope.row.ID"
              v-debounce="{ evtHandler: () => executeTask(scope.row) }"
              >执行</el-link
            >
            <el-link
              type="primary"
              :underline="false"
              @click.native="openEdit(scope.row)"
              >编辑</el-link
            >
            <el-link
              type="danger"
              :underline="false"
              @click.native="deleteTask(scope.row)"
              >删除</el-link
            >
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
    <el-drawer
      title="添加文件"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getList, deleteTask, executeTask } from '@/api/crontab'
import EditForm from './components/EditForm'

export default {
  name: 'Crontab',
  components: {
    EditForm,
  },
  props: {},
  data() {
    return {
      tableData: [],
      tableLoading: false,
      total: 0,
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        key: '',
      },
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.tableLoading = true
      const [err, res] = await getList(this.formData)
      if (res) {
        this.tableData = res.data
        this.total = res.totalRow
      }
      this.tableLoading = false
    },
    async deleteTask(data) {
      try {
        await this.$confirm('此操作将永久删除选中任务, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const payload = {
        id: data.ID,
        contentBefore: data,
      }

      const [err, res] = await deleteTask(payload)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    async executeTask(data) {
      if (data.STATE != '2') {
        return false
      }
      const [err, res] = await executeTask(data.ID)
      if (!err) {
        this.$message({
          message: '执行成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    openEdit(data) {
      if (data) {
        this.editDrawerTitle = '编辑任务'
        this.editDrawerData = data
      } else {
        this.editDrawerTitle = '创建任务'
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    getStatus(status, flag) {
      const map = {
        type: {
          1: 'success',
          2: 'primary',
          3: 'danger',
        },
        text: {
          1: '运行中',
          2: '空闲',
          3: '禁用',
        },
      }
      return map[flag][status]
    },
  },
}
</script>

<style lang="scss">
#crontab {
  .header {
    border-bottom: 1px solid $borderColor;
  }

  > .search-bar {
    padding-top: 16px;
    justify-content: flex-start;
  }

  > .y-container--tight {
    padding-bottom: 0;
    border-bottom: 1px solid $borderColor;

    .el-table {
      &::before {
        content: none;
      }

      .el-link {
        + .el-dropdown {
          margin-left: 16px;
          color: $themeColor;
          cursor: pointer;
        }
      }
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }
}
</style>
