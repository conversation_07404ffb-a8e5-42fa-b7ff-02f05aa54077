<template>
  <div class="detail-table y-container no-padding">
    <div class="y-container--tight no-padding">
      <div class="header">
        <el-image
          :src="require('@/assets/images/training-card-icon.svg')"
          style="flex: 0 1 max-content; width: 60px; min-width: 60px; height: 60px"></el-image>
        <div class="wrapper">
          <h3>{{ data?.NAME }}</h3>
          <overflow-text
            :max="40"
            :content="data?.DESC"></overflow-text>
        </div>
        <el-button
          @click="dialogShow = true"
          class="mini"
          icon="el-icon-upload2"
          type="primary">
          导入
        </el-button>
      </div>
      <div class="y-bar">
        <el-input
          v-model="formData.keyword"
          v-trim
          placeholder="请输入关键词进行搜索"
          size="small"
          clearable
          style="width: 220px"></el-input>
        <el-button
          v-debounce="{ evtHandler: fetchData }"
          class="mini"
          type="primary">
          <i class="el-icon-search"></i>
          搜索
        </el-button>
      </div>
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <el-table-column
          type="index"
          label="序号"
          width="50">
        </el-table-column>
        <el-table-column
          label="文件名称"
          prop="FILE_NAME"
          min-width="120"
          show-overflow-tooltip>
          <template v-slot="scope">
            <el-link
              @click="openViewer(scope.row)"
              type="primary"
              :key="scope.row.ID"
              :underline="false"
              style="text-overflow: ellipsis"
              >{{ scope.row.FILE_NAME }}</el-link
            >
          </template>
        </el-table-column>
        <el-table-column
          label="文件后缀"
          prop="FILE_SUFFIX"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="文件类型"
          prop="FILE_TYPE"
          show-overflow-tooltip>
          <template v-slot="scope">
            {{ fileTypeMap[scope.row.FILE_TYPE] }}
          </template>
        </el-table-column>
        <el-table-column
          label="上传人"
          prop="UPLOADER"
          width="100"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="上传时间"
          prop="UPLOAD_TIME"
          width="180"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="操作"
          width="100">
          <template slot-scope="scope">
            <el-link
              type="danger"
              :underline="false"
              @click.native="deleteFile(scope.row)"
              >删除</el-link
            >
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
    <el-dialog
      :visible.sync="dialogShow"
      @close="$refs.uploader?.$refs?.upload?.clearFiles?.()"
      title="上传文件"
      width="500px"
      append-to-body>
      <UploadFile
        ref="uploader"
        @change="files = $event"></UploadFile>
      <div slot="footer">
        <el-button
          @click="importFile"
          :loading="loading"
          type="primary"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <ImageViewer
      v-if="showViewer"
      :z-index="3000"
      :initial-index="imageIndex"
      :on-close="() => (showViewer = false)"
      :url-list="previewSrcList"></ImageViewer>
  </div>
</template>

<script>
import { getFileList, getFile, deleteFile, importFile } from '@/api/file-data-set'
import { downloadFile } from '@/utils'
import UploadFile from './UploadFile.vue'
import ImageViewer from 'element-ui/packages/image/src/image-viewer.vue'

export default {
  components: {
    UploadFile,
    ImageViewer,
  },
  props: {
    data: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      total: 0,
      tableLoading: false,
      loading: false,
      tableData: [],
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        dataSetId: '',
        keyword: '',
      },
      files: [],
      fileTypeMap: {
        1: '文本',
        2: '图片',
        3: '音频',
        4: '视频',
        5: 'PDF',
        6: '办公文件',
      },
      dialogShow: false,
      imageIndex: 0,
      showViewer: false,
      previewSrcList: [],
    }
  },
  computed: {
    imageList() {
      return this.tableData.filter((i) => i.FILE_TYPE == 2)
    },
    // previewSrcList() {
    //   return this.imageList.map((i) => '/dg-etl-mgr/servlet/fileDataItem?action=downloadOrPreviewFile&fileId=' + i.ID)
    // },
  },
  watch: {
    data: {
      handler(data) {
        if (data) {
          this.initPage()
          this.fetchData()
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    initPage() {
      this.formData = {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        dataSetId: this.data.ID,
        keyword: '',
      }
    },
    async fetchData() {
      this.tableLoading = true
      const payload = { ...this.formData }
      const [err, res] = await getFileList(payload)
      if (res) {
        this.tableData = res.data
        this.total = res.totalRow
      }
      this.tableLoading = false
    },
    async openViewer(data) {
      let url = await this.getFile(data)
      if (!url) {
        return false
      }
      if (data.FILE_TYPE == 2) {
        this.showViewer = true
        // let idx = this.imageList.findIndex((i) => i.ID === data.ID)
        // this.imageIndex = idx !== -1 ? idx : 0
        this.previewSrcList = [url]
      } else {
        downloadFile(url)
      }
    },
    async getFile(data) {
      const [err, res] = await getFile(data.ID)
      return res?.data?.url
    },

    async deleteFile(data) {
      try {
        await this.$confirm('此操作将永久删除选中文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const [err, res] = await deleteFile(data.ID)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    async importFile() {
      if (this.files?.length < 1) {
        this.$message({
          message: '请选择文件进行上传',
        })
      }
      const payload = new FormData()
      payload.append('fileDataSetId', this.data.ID)
      this.files.forEach((file) => payload.append('files', file.raw))
      this.loading = true
      const [err, res] = await importFile(payload)
      this.loading = false
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.$refs.uploader?.$refs?.upload?.clearFiles?.()
            this.dialogShow = false
            this.fetchData()
            this.$emit('update-data')
          },
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.detail-table {
  > .y-container--tight {
    padding-bottom: 0;
    border-bottom: 1px solid $borderColor;

    .header {
      @include flex-row;
      padding: 24px 16px 16px;
      width: 100%;

      .wrapper {
        flex: 1;
        margin-left: 16px;

        h3 {
          @include text-overflow(1);
          margin-bottom: 4px;
          font-size: 16px;
          font-weight: bold;
          line-height: 24px;
          color: $txtColor;
        }

        span {
          font-size: 14px;
          font-weight: normal;
          line-height: 22px;
          color: $txtColor-light;
        }
      }
    }

    .y-bar {
      justify-content: flex-end;
    }

    .el-table::before {
      content: none;
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }
}
</style>
