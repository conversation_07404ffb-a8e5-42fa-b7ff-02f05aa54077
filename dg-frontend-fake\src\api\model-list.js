import { get, post } from '@/http/request'
export function getModelList(formData) {
  return post('/dg-etl-mgr/webcall?action=model.pageModel', { data: JSON.stringify(formData) })
}

export function addModel(formData) {
  return post('/dg-etl-mgr/servlet/model?action=addModel', { data: JSON.stringify(formData) })
}

export function updateModel(formData) {
  return post('/dg-etl-mgr/servlet/model?action=editModel', { data: JSON.stringify(formData) })
}

export function deleteModel(id) {
  return post('/dg-etl-mgr/servlet/model?action=delModel', { data: JSON.stringify({ id }) })
}

export function generateVersion(formData) {
  return post('/dg-etl-mgr/servlet/model?action=ver', { data: JSON.stringify(formData) })
}

export function downloadModel(SYS_URL) {
  return post('/dg-etl-mgr/servlet/model?action=download', { data: JSON.stringify({ SYS_URL }) }, null, null, { responseType: 'blob' })
}
