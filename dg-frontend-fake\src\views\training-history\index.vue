<template>
  <base-card
    id="training-history"
    class="y-page">
    <div class="y-bar header">
      <h2 class="y-title">{{ overrideTitle || '工单历史' }}</h2>
      <p class="tip"><svg-icon icon="exclam"></svg-icon>准确率规则：<span>a &lt; 70%</span><span>70% ≤ a &lt; 85%</span><span>a ≥ 85%</span></p>
      <span>关键字</span>
      <el-input
        v-model="formData.key"
        v-trim
        placeholder="请输入关键词进行搜索"
        size="small"
        clearable
        style="width: 220px"></el-input>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
    </div>
    <div class="y-container--tight">
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <el-table-column
          label="名称"
          prop="TRAIN_BATCH_NAME"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="训练时间"
          prop="CREATE_TIME"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="记录数"
          prop="RECORD_COUNT"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="标记记录数"
          prop="MARK_COUNT"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="准确记录数"
          prop="SUCC_COUNT"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="不准确记录数"
          prop="ERROR_COUNT"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="准确率"
          prop="SUCC_RATE">
          <template slot-scope="scope">
            <el-tag
              v-if="scope.row.SUCC_RATE"
              :type="getAccuracyType(Number(scope.row.SUCC_RATE))"
              >{{ getPercent(scope.row.SUCC_RATE) }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="150">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :underline="false"
              @click.native="
                $router.push({
                  name: 'TrainingMark',
                  params: { etlId: scope.row.ETL_ID, trainDatasetId: scope.row.TRAIN_DATASET_ID, overrideTitle: scope.row.ETL_NAME + '标注' },
                  query: { trainBatchId: scope.row.TRAIN_BATCH_ID },
                })
              "
              >查看数据</el-link
            >
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
  </base-card>
</template>

<script>
import { getHistoryList } from '@/api/data-training'

export default {
  name: 'TrainingHistory',
  components: {},
  props: {},
  data() {
    return {
      overrideTitle: '',
      tableData: [],
      tableLoading: false,
      total: 0,
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        key: '',
        trainDatasetId: '',
      },
    }
  },
  computed: {},
  watch: {},
  created() {
    this.handleRouteData()
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.tableLoading = true
      const [err, res] = await getHistoryList(this.formData)
      if (res) {
        this.tableData = res.data
        this.total = res.totalRow
      }
      this.tableLoading = false
    },
    handleRouteData() {
      let { trainDatasetId, overrideTitle } = this.$route.params
      Object.assign(this.formData, { trainDatasetId })
      this.overrideTitle = overrideTitle
    },
    getAccuracyType(accuracy) {
      if (accuracy >= 0.85) {
        return 'success'
      } else if (accuracy >= 0.7) {
        return 'warning'
      } else {
        return 'danger'
      }
    },
    getPercent(num) {
      return (Number(num) * 100).toFixed(1) + '%'
    },
  },
}
</script>

<style lang="scss">
#training-history {
  .header {
    border-bottom: 1px solid $borderColor;
    justify-content: flex-start;

    .y-title {
      flex: 0;
      margin-right: 40px;
    }

    .tip {
      @include flex-row;
      flex: 1;
      flex-wrap: wrap;
      justify-content: flex-start;
      font-size: 12px;
      font-weight: normal;
      line-height: 18px;
      color: $txtColor-light;

      .svg-icon {
        margin-right: 4px;
        font-size: 14px;
        color: $txtColor-slight;
      }

      span {
        @include flex-row;
        justify-content: flex-start;

        &:before {
          content: '';
          display: block;
          margin-left: 16px;
          margin-right: 4px;
          width: 10px;
          height: 10px;
          border-radius: 2px;
        }

        &:nth-child(2):before {
          background-color: $color-danger;
        }

        &:nth-child(3):before {
          background-color: $color-warning;
        }

        &:nth-child(4):before {
          background-color: $color-success;
        }
      }
    }
  }

  > .y-container--tight {
    padding-top: 16px;
    padding-bottom: 0;
    border-bottom: 1px solid $borderColor;

    .el-table::before {
      content: none;
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }
}
</style>
