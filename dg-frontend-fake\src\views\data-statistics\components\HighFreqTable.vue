<template>
  <div class="high-freq-table y-container no-padding">
    <div class="y-container--tight no-padding">
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <el-table-column
          type="index"
          label="序号"
          width="100">
        </el-table-column>
        <el-table-column
          label="问题名称"
          prop="ROW_COUNT">
        </el-table-column>
        <el-table-column
          label="问题类型"
          prop="ROW_COUNT">
        </el-table-column>
        <el-table-column
          label="关联数量"
          prop="ROW_COUNT">
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
      total: 0,
      formData: {
        pageIndex: 1,
        pageSize: 6,
        pageType: 3,
      },
    }
  },
  computed: {},
  methods: {},
}
</script>

<style lang="scss" scoped>
.high-freq-table {
  > .y-container--tight {
    padding: 16px 0px;

    .el-table {
      border-radius: 4px;
      border: 1px solid $borderColor;
      overflow: hidden;

      &::before {
        content: none;
      }
    }
  }

  .base-pagination {
    align-self: flex-end;
  }
}
</style>
