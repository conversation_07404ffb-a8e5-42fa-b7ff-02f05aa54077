import { get, post } from '@/http/request'
import { etl } from '@/api/PATH'

export function getEtlList(formData) {
  return post(etl.etlList, formData)
}

export function updateEtl(formData) {
  return post(etl.update, null, formData)
}

export function deleteEtl(etlId) {
  return post(etl.delete, null, { etlId })
}

export function getEtlVerList(formData) {
  return post(etl.etlVerList, formData)
}
