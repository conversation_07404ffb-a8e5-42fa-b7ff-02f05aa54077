<template>
  <div class="aside-bar">
    <div class="y-bar">
      <h2 class="y-title">服务组件分类</h2>
      <svg-icon
        icon="add"
        @click.native="openDialog({ P_TYPE_ID: '0' })"></svg-icon>
    </div>
    <div
      class="y-bar"
      style="padding-top: 0">
      <el-input
        v-model="keyword"
        v-trim
        placeholder="请输入关键字"
        size="small"
        clearable
        suffix-icon="el-icon-search"></el-input>
    </div>
    <class-tree
      ref="tree"
      :tree="menuList"
      :active-item="activeItem"
      :filter-node-method="filterNode"
      @set-current="setCurrent($event)">
      <template v-slot="{ node, data }">
        <svg-icon
          v-if="node.level === 1"
          icon="folder"></svg-icon>
        <span
          :class="['tree-item-name', node.level === 1 ? '' : 'secondary']"
          style="flex: 1"
          >{{ data.name }}</span
        >
        <!-- <svg-icon
          v-if="node.level === 1"
          icon="plus"
          @click="openDialog({ P_TYPE_ID: data.TYPE_ID })"></svg-icon> -->
        <el-dropdown
          trigger="click"
          style="margin-left: 18px">
          <div>
            <svg-icon
              icon="more"
              style="vertical-align: middle"></svg-icon>
          </div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              icon="el-icon-edit"
              @click.native="openDialog(data)"
              >编辑</el-dropdown-item
            >
            <el-dropdown-item
              icon="el-icon-delete"
              @click.native="deleteClass(data)"
              >删除</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
        <!-- <svg-icon
          v-if="data.children?.length > 0 || node.level === 1"
          icon="up"
          :style="{ 'margin-left': '18px', transform: node.expanded ? 'rotate(180deg)' : '' }"></svg-icon> -->
      </template>
    </class-tree>
    <el-dialog
      :title="dialogTitle"
      width="500px"
      :visible.sync="dialogShow">
      <el-form
        ref="editForm"
        class="clearfix"
        :model="editForm"
        :rules="rules"
        label-position="right"
        label-width="90px">
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="分类名称"
              prop="typeName">
              <el-input
                v-model="editForm.typeName"
                v-trim
                clearable
                placeholder="请输入分类名称"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button
          v-debounce="submitForm"
          type="primary"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getClassTree, addClass, updateClass, deleteClass } from '@/api/process-module'
import ClassTree from '@/components/ClassTree'

export default {
  components: {
    ClassTree,
  },
  data() {
    return {
      loading: false,
      activeItem: null,
      keyword: '',
      menuList: [],
      treeProps: {
        label: 'name',
        children: 'children',
      },
      dialogTitle: '',
      dialogShow: false,
      dialogType: '',
      editForm: { typeName: '' },
      rules: {
        typeName: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
      },
    }
  },
  computed: {
    filteredList() {
      if (this.keyword.trim()) {
        return this.menuList.filter(([key, value]) => value.includes(this.keyword.trim()))
      }
      return this.menuList
    },
  },
  watch: {
    keyword(val) {
      this.$refs.tree.$refs.elTree.filter(val)
    },
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      const [err, res] = await getClassTree()
      if (res && res.data) {
        this.menuList = res.data?.children
        this.setCurrent(this.menuList[0])
      }

      this.loading = false
    },
    setCurrent(item) {
      this.activeItem = item?.id
      this.$emit('set-current', item?.id)
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const payload = { ...this.editForm }

        let submitAction = addClass
        if (this.dialogType === 'edit') {
          submitAction = updateClass
        }

        const [err, res] = await submitAction(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.dialogShow = false
              this.fetchData()
            },
          })
        }
      })
    },
    async deleteClass(data) {
      try {
        await this.$confirm('此操作将永久删除选中分类, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const payload = {
        typeId: data.TYPE_ID,
      }

      const [err, res] = await deleteClass(payload)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    initDialog(data) {
      this.$refs?.editForm?.resetFields()
      const { TYPE_NAME, TYPE_ID, P_TYPE_ID } = data
      if (TYPE_ID) {
        this.dialogTitle = '编辑分类'
        this.dialogType = 'edit'
        this.editForm = { typeName: TYPE_NAME, typeId: TYPE_ID, pTypeId: P_TYPE_ID }
      } else {
        this.dialogTitle = '新增分类'
        this.dialogType = 'add'
        this.editForm = { typeName: '', pTypeId: P_TYPE_ID }
      }
    },
    openDialog(data) {
      this.initDialog(data)
      this.dialogShow = true
    },
    closeDialog() {
      this.dialogShow = false
    },
  },
}
</script>

<style lang="scss" scoped>
.aside-bar::v-deep {
  @include flex-col;
  flex-shrink: 0;
  width: 288px;
  height: 100%;

  .y-title {
    line-height: 24px;
  }

  > .y-bar {
    padding: 16px 24px;
    padding-bottom: 8px;

    .svg-icon {
      font-size: 16px;
      color: $txtColor-light;
      cursor: pointer;

      &:hover {
        color: $themeColor;
      }
    }
  }
}
</style>
