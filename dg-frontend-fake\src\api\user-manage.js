import { get, post } from '@/http/request'
import { userManage } from '@/api/PATH'
import md5 from 'blueimp-md5'

// 人员管理
export function getList(formData) {
  return post(userManage.list, formData)
}

export function getDetail(id) {
  return post(userManage.detail, { pk: id })
}

export function importUser(formData) {
  return post(userManage.import, formData, null, { 'Content-Type': 'multipart/form-data' })
}

export function addUser(formData) {
  // if (formData['userLogin.USER_PWD']) {
  //   formData['userLogin.USER_PWD'] = md5(formData['userLogin.USER_PWD'])
  // }
  return post(userManage.add, { data: JSON.stringify(formData) })
}

export function updateUser(formData) {
  // if (formData['userLogin.USER_PWD']) {
  //   formData['userLogin.USER_PWD'] = md5(formData['userLogin.USER_PWD'])
  // }
  return post(userManage.update, { data: JSON.stringify(formData) })
}

export function deleteUser(id) {
  return post(userManage.delete, { data: JSON.stringify({ userId: id }) })
}

export function updatePwd(formData) {
  // for (const key in formData) {
  //   formData[key] = md5(formData[key])
  // }
  return post(userManage.updatePwd, { data: JSON.stringify(formData) })
}

export function resetPwd(formData) {
  return post(userManage.resetPwd, { data: JSON.stringify(formData) })
}