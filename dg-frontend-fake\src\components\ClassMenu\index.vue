<template>
  <div class="class-menu y-container--tight">
    <div
      :class="['class-menu_item', isActive(item) ? 'is-active' : '']"
      v-for="(item, idx) in list"
      :key="generateId()"
      :style="{ marginTop: idx > 0 ? gap : null }"
      @click="$emit('set-current', item)">
      <slot :item="item"></slot>
    </div>
  </div>
</template>

<script setup>
import { generateId } from '@/utils'

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  activeMenu: {
    type: String,
    default: '',
  },
  idProp: {
    type: String,
    default: () => 'id',
  },
  gap: {
    type: String,
    default: '4px',
  },
})

const isActive = (item) => {
  if (!props.idProp) {
    return item === props.activeMenu
  }
  return item[props.idProp] === props.activeMenu
}
</script>

<style lang="scss" scoped>
.class-menu {
  > .class-menu_item {
    @include flex-row;
    padding: 12px 16px;
    width: 100%;
    border-radius: 4px;
    cursor: pointer;

    > span {
      @include text-overflow(2);
      flex: 1;
    }

    .svg-icon {
      flex: 0 0 16px;
      margin-left: 8px;
      font-size: 16px;

      &:hover {
        color: $themeColor;
      }
    }

    &.is-active,
    &:hover {
      color: $themeColor;
      background-color: transparentize($themeColor, 0.95);

      .svg-icon {
        color: $themeColor;
      }
    }
  }
}
</style>
