<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="90px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="选择Excel"
            prop="fileName">
            <div class="upload-wrapper">
              <el-input
                v-model="formData.fileName"
                v-trim
                readonly
                placeholder="请选择文件"></el-input>
              <el-upload
                ref="upload"
                action=""
                accept=".xlsx, .xls"
                :auto-upload="false"
                :show-file-list="false"
                :multiple="false"
                :on-change="importExcel">
                <el-button
                  class="mini"
                  type="primary"
                  plain>
                  <svg-icon icon="enter"></svg-icon>
                  导入Excel
                </el-button>
              </el-upload>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="table-wrapper y-container no-padding">
        <h4 class="table-title">数据预览</h4>
        <div class="y-container--tight no-padding">
          <el-table
            :data="tableData"
            v-loading="tableLoading"
            stripe
            height="100%"
            fit
            ref="table"
            style="width: 100%">
            <template v-if="tableHeader.length > 0">
              <el-table-column
                type="index"
                label="序号"
                width="50">
              </el-table-column>
              <el-table-column
                v-for="{ columnLabel, column } in tableHeader"
                :key="column"
                :label="columnLabel"
                :prop="column"
                show-overflow-tooltip>
              </el-table-column>
            </template>
            <el-empty
              slot="empty"
              :image="require('@/assets/images/no-info.png')"
              description="暂无信息"></el-empty>
          </el-table>
        </div>
      </div>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="cancelImport">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        type="primary"
        size="small"
        :loading="confirming"
        @click.native="confirmImport"
        >确定</el-button
      >
    </div>
  </div>
</template>

<script>
import { importExcel, confirmImport, cancelImport } from '@/api/data-set'

export default {
  components: {},
  props: {},
  data() {
    return {
      tableHeader: [],
      tableData: [],
      tableLoading: false,
      confirming: false,
      formData: {
        fileName: '',
        fileId: '',
      },
      rules: {
        fileName: [{ required: true, message: '请选择文件', trigger: 'change' }],
      },
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      this.formData = {
        fileName: '',
        fileId: '',
      }
      this.tableData = []
      this.tableHeader = []
    },
    async confirmImport() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        this.confirming = true
        const [err, res] = await confirmImport(this.formData.fileId)

        if (!err) {
          this.$message({
            message: '正在上传中，请稍后刷新列表以查看上传结果',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.confirming = false
              this.$emit('close-edit')
              this.initPage()
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
    async cancelImport() {
      if (this.formData.fileId) {
        const [err, res] = await cancelImport(this.formData.fileId)

        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              // this.$emit('close-edit')
              this.initPage()
              this.$refs.editForm.resetFields()
              // this.$emit('update-data')
            },
          })
        }
      }
    },
    async importExcel(file, fileList) {
      if (
        file.raw.type !== 'application/vnd.ms-excel' &&
        file.raw.type !== 'application/x-excel' &&
        file.raw.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ) {
        this.$message({
          message: '文件类型不正确，请重新上传',
          type: 'error',
          duration: 800,
        })
        return false
      }

      const payload = {
        file: file.raw,
      }

      this.tableLoading = true
      this.initPage()
      const [err, res] = await importExcel(payload)
      if (res) {
        this.tableHeader = res.data.headConfig
        this.tableData = res.data.dataList
        this.formData.fileName = file.name.slice(0, file.name.lastIndexOf('.'))
        this.formData.fileId = res.data.fileId
        this.$message({
          message: '上传成功！',
          type: 'success',
          duration: 800,
          onClose: () => {},
        })
      }
      this.tableLoading = false
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;

  .upload-wrapper {
    @include flex-row;

    .el-button {
      margin-left: 16px;
      padding: 12px 16px;
    }
  }

  .table-wrapper {
    margin-left: 90px;
    width: calc(100% - 90px);
    height: calc(100% - 65px);
    border-radius: 4px;
    border: 1px solid $borderColor;

    .table-title {
      padding: 16px 24px;
      width: 100%;
      font-size: 16px;
      font-weight: normal;
      line-height: 24px;
    }
  }

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
