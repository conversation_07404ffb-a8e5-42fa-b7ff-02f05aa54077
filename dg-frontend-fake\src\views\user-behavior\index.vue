<template>
  <base-card id="user-behavior" class="y-page">
    <div class="y-bar">
      <h2 class="y-title">客户行为列表</h2>
    </div>
    <div class="y-bar search-bar" :class="{ expanded: showExpanded }">
      <el-form :model="formData" ref="searchForm">
        <!-- 第一行搜索条件 -->
        <div class="search-row">
          <el-form-item label="按时还款" prop="ON_TIME_PAYMENT">
            <el-select
              v-model="formData.ON_TIME_PAYMENT"
              size="small"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in yesNoOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否本人" prop="IS_SELF">
            <el-select
              v-model="formData.IS_SELF"
              size="small"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in yesNoOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="提供联系方式" prop="PROVIDE_CONTACT">
            <el-select
              v-model="formData.PROVIDE_CONTACT"
              size="small"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in yesNoOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="否认情况" prop="DENIAL">
            <el-select
              v-model="formData.DENIAL"
              size="small"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in denialOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <div v-if="!showExpanded" class="action-buttons">
            <el-button
              class="mini"
              type="primary"
              size="small"
              :icon="showExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
              plain
              @click="toggleExpanded"
            >
              {{ showExpanded ? "收起" : "高级搜索" }}
            </el-button>
            <el-button v-debounce="fetchData" class="mini" type="primary">
              <i class="el-icon-search"></i>
              搜索
            </el-button>
            <el-button class="mini" @click="resetSearch">
              <i class="el-icon-refresh"></i>
              重置
            </el-button>
          </div>
        </div>

        <!-- 展开状态下的搜索条件 -->
        <transition name="expand">
          <div v-show="showExpanded" class="expanded-search">
            <!-- 第二行搜索条件 -->
            <div class="search-row">
              <el-form-item label="承诺还款" prop="PROMISE_PAYMENT">
                <el-select
                  v-model="formData.PROMISE_PAYMENT"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in yesNoOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="愿意还款" prop="WILLING_TO_PAY">
                <el-select
                  v-model="formData.WILLING_TO_PAY"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in yesNoOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="同意告知" prop="AGREE_TO_INFORM">
                <el-select
                  v-model="formData.AGREE_TO_INFORM"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in yesNoOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="想要分期" prop="WANT_INSTALLMENT">
                <el-select
                  v-model="formData.WANT_INSTALLMENT"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in yesNoOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>

            <!-- 第三行搜索条件 -->
            <div class="search-row">
              <el-form-item label="参加活动" prop="JOIN_ACTIVITY">
                <el-select
                  v-model="formData.JOIN_ACTIVITY"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in yesNoOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="贷款产品" prop="LOAN_PRODUCT">
                <el-select
                  v-model="formData.LOAN_PRODUCT"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in loanProductOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="不要再打" prop="NO_MORE_CALLS">
                <el-select
                  v-model="formData.NO_MORE_CALLS"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in noMoreCallsOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="潜在投诉" prop="POTENTIAL_COMPLAINT">
                <el-select
                  v-model="formData.POTENTIAL_COMPLAINT"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in potentialComplaintOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>

            <!-- 第四行搜索条件 -->
            <div class="search-row">
              <el-form-item label="还款渠道" prop="PAYMENT_CHANNEL">
                <el-select
                  v-model="formData.PAYMENT_CHANNEL"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in paymentChannelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="申请渠道" prop="APPLICATION_CHANNEL">
                <el-select
                  v-model="formData.APPLICATION_CHANNEL"
                  size="small"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in applicationChannelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>

            <!-- 展开状态下的操作按钮行 -->
            <div class="search-row action-row">
              <div class="action-buttons">
                <el-button
                  class="mini"
                  type="primary"
                  size="small"
                  :icon="
                    showExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
                  "
                  plain
                  @click="toggleExpanded"
                >
                  {{ showExpanded ? "收起" : "展开更多" }}
                </el-button>
                <el-button v-debounce="fetchData" class="mini" type="primary">
                  <i class="el-icon-search"></i>
                  搜索
                </el-button>
                <el-button class="mini" @click="resetSearch">
                  <i class="el-icon-refresh"></i>
                  重置
                </el-button>
              </div>
            </div>
          </div>
        </transition>
      </el-form>
    </div>
    <empty-wrapper
      class="y-container--tight"
      :toggle="!behaviorList || behaviorList?.length === 0"
      v-loading="loading"
    >
      <el-table
        :data="behaviorList"
        v-loading="loading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        @selection-change="handleSelectionChange"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%"
      >
        <el-table-column type="selection"> </el-table-column>
        <!-- <el-table-column type="index" label="序号" width="50"></el-table-column> -->
        <el-table-column
          label="消息时间"
          prop="MSG_TIME"
          show-overflow-tooltip
          width="150"
        ></el-table-column>

        <el-table-column
          label="客户是否按期还款"
          prop="ON_TIME_PAYMENT"
          show-overflow-tooltip
          width="120"
        ></el-table-column>
        <el-table-column
          label="是否本人操作"
          prop="IS_SELF"
          show-overflow-tooltip
          width="100"
        ></el-table-column>
        <el-table-column
          label="是否提供本人联系方式"
          prop="PROVIDE_CONTACT"
          show-overflow-tooltip
          width="140"
        ></el-table-column>
        <el-table-column
          label="客户否认内容"
          prop="DENIAL"
          show-overflow-tooltip
          width="100"
        ></el-table-column>
        <el-table-column
          label="是否承诺还款"
          prop="PROMISE_PAYMENT"
          show-overflow-tooltip
          width="100"
        ></el-table-column>
        <el-table-column
          label="是否愿意代还"
          prop="WILLING_TO_PAY"
          show-overflow-tooltip
          width="100"
        ></el-table-column>
        <el-table-column
          label="是否同意转告他人"
          prop="AGREE_TO_INFORM"
          show-overflow-tooltip
          width="120"
        ></el-table-column>
        <el-table-column
          label="是否希望分期付款"
          prop="WANT_INSTALLMENT"
          show-overflow-tooltip
          width="120"
        ></el-table-column>
        <el-table-column
          label="是否参加活动"
          prop="JOIN_ACTIVITY"
          show-overflow-tooltip
          width="100"
        ></el-table-column>
        <el-table-column
          label="贷款产品类型"
          prop="LOAN_PRODUCT"
          show-overflow-tooltip
          width="120"
        ></el-table-column>
        <el-table-column
          label="逾期金额"
          prop="OVERDUE_AMOUNT"
          show-overflow-tooltip
          width="100"
        ></el-table-column>
        <el-table-column
          label="逾期期数"
          prop="OVERDUE_PERIODS"
          show-overflow-tooltip
          width="100"
        ></el-table-column>
        <el-table-column
          label="营销活动名称"
          prop="MARKETING_ACTIVITY"
          show-overflow-tooltip
          width="120"
        ></el-table-column>
        <el-table-column
          label="是否要求不再来电"
          prop="NO_MORE_CALLS"
          show-overflow-tooltip
          width="120"
        ></el-table-column>
        <el-table-column
          label="潜在投诉风险"
          prop="POTENTIAL_COMPLAINT"
          show-overflow-tooltip
          width="120"
        ></el-table-column>
        <el-table-column
          label="客户还款渠道"
          prop="PAYMENT_CHANNEL"
          show-overflow-tooltip
          width="120"
        ></el-table-column>
        <el-table-column
          label="客户办理渠道"
          prop="APPLICATION_CHANNEL"
          show-overflow-tooltip
          width="120"
        ></el-table-column>
        <el-table-column
          label="记录创建时间"
          prop="CREATED_AT"
          show-overflow-tooltip
          width="150"
        ></el-table-column>
        <!-- <el-table-column label="操作" width="100" fixed="right">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :underline="false"
              @click.native="openEdit(scope.row)"
              >编辑</el-link
            >
            <el-link
              type="danger"
              :underline="false"
              @click.native="deleteHotword(scope.row)"
              >删除</el-link
            >
          </template>
        </el-table-column> -->
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"
        ></el-empty>
      </el-table>
    </empty-wrapper>
    <div class="footer">
      <pagination
        :current-page.sync="formData.pageIndex"
        :page-size.sync="formData.pageSize"
        :total="total"
        @page="fetchData"
      ></pagination>
    </div>
  </base-card>
</template>

<script>
import { getList } from "@/api/user-behavior";
// import ConfigCard from "@/components/ConfigCard";
// import EditForm from './components/EditForm'

export default {
  name: "UserBehavior",
  components: {
    // ConfigCard,
    // EditForm,
  },
  props: {},
  data() {
    return {
      total: 0,
      loading: false,
      formData: {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
        ON_TIME_PAYMENT: "",
        IS_SELF: "",
        PROVIDE_CONTACT: "",
        DENIAL: "",
        PROMISE_PAYMENT: "",
        WILLING_TO_PAY: "",
        AGREE_TO_INFORM: "",
        WANT_INSTALLMENT: "",
        JOIN_ACTIVITY: "",
        LOAN_PRODUCT: "",
        NO_MORE_CALLS: "",
        POTENTIAL_COMPLAINT: "",
        PAYMENT_CHANNEL: "",
        APPLICATION_CHANNEL: "",
      },
      behaviorList: [],
      showExpanded: false,

      tableSelection: [],

      // 搜索选项数组
      yesNoOptions: [
        { value: "是", label: "是" },
        { value: "否", label: "否" },
      ],
      denialOptions: [
        { value: "否认本人", label: "否认本人" },
        { value: "否认消费", label: "否认消费" },
      ],
      loanProductOptions: [
        { value: "个人非房消费贷款", label: "个人非房消费贷款" },
        { value: "个人经营性贷款", label: "个人经营性贷款" },
        { value: "个人住房贷款", label: "个人住房贷款" },
      ],
      noMoreCallsOptions: [
        { value: "不要再打", label: "不要再打" },
        { value: "无", label: "无" },
      ],
      potentialComplaintOptions: [
        { value: "再打投诉", label: "再打投诉" },
        { value: "无", label: "无" },
      ],
      paymentChannelOptions: [
        { value: "手机银行APP", label: "手机银行APP" },
        { value: "企业网银", label: "企业网银" },
      ],
      applicationChannelOptions: [
        { value: "手机银行APP", label: "手机银行APP" },
        { value: "手机上的软件", label: "手机上的软件" },
      ],
      // 字段标签映射
      fieldLabelMap: {
        ON_TIME_PAYMENT: "按时还款",
        IS_SELF: "是否本人",
        PROVIDE_CONTACT: "提供联系方式",
        DENIAL: "否认情况",
        PROMISE_PAYMENT: "承诺还款",
        WILLING_TO_PAY: "愿意还款",
        AGREE_TO_INFORM: "同意告知",
        WANT_INSTALLMENT: "想要分期",
        JOIN_ACTIVITY: "参加活动",
        LOAN_PRODUCT: "贷款产品",
        NO_MORE_CALLS: "不要再打",
        POTENTIAL_COMPLAINT: "潜在投诉",
        PAYMENT_CHANNEL: "还款渠道",
        APPLICATION_CHANNEL: "申请渠道",
        OVERDUE_AMOUNT: "逾期金额",
        OVERDUE_PERIODS: "逾期期数",
      },
    };
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData();
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true;
      const [err, res] = await getList(this.formData);
      if (res) {
        this.behaviorList = res.data;
        this.total = res.totalRow;
      }

      this.loading = false;
    },
    // 解析消息内容
    parsedMsgContent(msgContent) {
      try {
        const parsed = JSON.parse(msgContent);
        return {
          robot: parsed.robot || "",
          user: parsed.user || "",
        };
      } catch (e) {
        return {
          robot: msgContent || "",
          user: "",
        };
      }
    },
    // 截断文本
    truncateText(text, maxLength) {
      if (!text) return "";
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + "...";
    },
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return "";
      return timeStr.replace(/\//g, "-");
    },
    // 生成行为摘要
    getBehaviorSummary(item) {
      const summaryParts = [];
      if (item.MARKETING_ACTIVITY && item.MARKETING_ACTIVITY !== "无") {
        summaryParts.push(`营销活动：${item.MARKETING_ACTIVITY}`);
      }

      // 添加关键行为信息
      const keyBehaviors = [];
      if (item.ON_TIME_PAYMENT === "是") keyBehaviors.push("按时还款");
      if (item.IS_SELF === "是") keyBehaviors.push("本人接听");
      if (item.WILLING_TO_PAY === "是") keyBehaviors.push("愿意还款");
      if (item.PROVIDE_CONTACT === "是") keyBehaviors.push("提供联系方式");

      if (keyBehaviors.length > 0) {
        summaryParts.push(`关键行为：${keyBehaviors.join("、")}`);
      }

      return summaryParts.join(" | ") || "客户行为数据记录";
    },
    // 获取行为标识分类
    getBehaviorTags(item) {
      const categories = {
        payment: {
          label: "还款相关",
          fields: [
            "ON_TIME_PAYMENT",
            "PROMISE_PAYMENT",
            "WILLING_TO_PAY",
            "PAYMENT_CHANNEL",
            "OVERDUE_AMOUNT",
            "OVERDUE_PERIODS",
          ],
          tags: [],
        },
        communication: {
          label: "沟通相关",
          fields: [
            "IS_SELF",
            "PROVIDE_CONTACT",
            "AGREE_TO_INFORM",
            "NO_MORE_CALLS",
            "POTENTIAL_COMPLAINT",
            "DENIAL",
          ],
          tags: [],
        },
        product: {
          label: "产品相关",
          fields: [
            "LOAN_PRODUCT",
            "WANT_INSTALLMENT",
            "JOIN_ACTIVITY",
            "APPLICATION_CHANNEL",
          ],
          tags: [],
        },
      };

      // 过滤有意义的字段
      Object.keys(categories).forEach((catKey) => {
        categories[catKey].fields.forEach((field) => {
          const value = item[field];
          if (value && value !== "无" && value !== "否") {
            categories[catKey].tags.push({
              label: this.fieldLabelMap[field] || field,
              value: value,
            });
          }
        });
      });

      return categories;
    },
    // 获取字段标签
    getFieldLabel(field) {
      return this.fieldLabelMap[field] || field;
    },
    // 格式化消息内容用于表格显示
    formatMsgContent(msgContent) {
      if (!msgContent) return '';
      try {
        const parsed = JSON.parse(msgContent);
        const robot = parsed.robot || '';
        const user = parsed.user || '';

        // 截断过长的内容并组合显示
        const robotText = robot.length > 50 ? robot.substring(0, 50) + '...' : robot;
        const userText = user.length > 30 ? user.substring(0, 30) + '...' : user;

        if (user) {
          return `机器人: ${robotText} | 客户: ${userText}`;
        } else {
          return `机器人: ${robotText}`;
        }
      } catch (e) {
        // 如果不是JSON格式，直接截断显示
        return msgContent.length > 80 ? msgContent.substring(0, 80) + '...' : msgContent;
      }
    },
    async handleSwitch(data) {
      // TODO: 实现状态切换逻辑
    },
    toggleExpanded() {
      this.showExpanded = !this.showExpanded;
    },
    getTag(status, flag, type) {
      const map = {
        STATUS: {
          class: {
            2: "primary",
            1: "success",
            0: "danger",
          },
          label: {
            2: "待审核",
            1: "已启用",
            0: "已禁用",
          },
        },
        PRIORITY: {
          class: {
            1: "warning",
            2: "danger",
            3: "info",
          },
          label: {
            1: "高优先级",
            2: "中优先级",
            3: "低优先级",
          },
        },
      };

      return map[type][flag][status];
    },
    resetSearch() {
      this.formData = {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
        ON_TIME_PAYMENT: "",
        IS_SELF: "",
        PROVIDE_CONTACT: "",
        DENIAL: "",
        PROMISE_PAYMENT: "",
        WILLING_TO_PAY: "",
        AGREE_TO_INFORM: "",
        WANT_INSTALLMENT: "",
        JOIN_ACTIVITY: "",
        LOAN_PRODUCT: "",
        NO_MORE_CALLS: "",
        POTENTIAL_COMPLAINT: "",
        PAYMENT_CHANNEL: "",
        APPLICATION_CHANNEL: "",
      };
      this.fetchData();
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },
  },
};
</script>

<style lang="scss">
#user-behavior {
  @import "@/assets/style/modules/config-card-page-with-footer.scss";
  background-color: transparent;

  .search-bar {
    padding-top: 16px;
    // justify-content: flex-start;
    width: 100%;

    .el-form {
      width: 100%;
    }

    .search-row {
      display: flex;
      align-items: center;
      gap: 24px;
      margin-bottom: 16px;

      .el-form-item {
        flex: 1;
        margin-right: 0;
        margin-bottom: 0;
        display: flex;
        align-items: center;

        .el-form-item__label {
          white-space: nowrap;
          padding-right: 8px;
          font-size: 14px;
          color: #606266;
          margin-bottom: 0;
        }

        .el-form-item__content {
          flex: 1;

          .el-select,
          .el-date-picker {
            width: 100%;
          }
        }
      }

      .action-buttons {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        gap: 8px;

        .el-button {
          margin-left: 0;
        }
      }
    }

    .expanded-search {
      width: 100%;
      margin: 0;

      .search-row:not(.action-row) {
        display: flex;
        align-items: center;
        gap: 24px;
        margin-bottom: 16px;

        .el-form-item {
          flex: 1;
          margin-right: 0;
          margin-bottom: 0;
          display: flex;
          align-items: center;

          .el-form-item__label {
            white-space: nowrap;
            padding-right: 8px;
            font-size: 14px;
            color: #606266;
            margin-bottom: 0;
          }

          .el-form-item__content {
            flex: 1;

            .el-select,
            .el-date-picker {
              width: 100%;
            }
          }
        }
      }

      .action-row {
        display: flex !important;
        justify-content: flex-end !important;
        flex-wrap: nowrap !important;
        margin-top: 24px;
        margin-bottom: 0;

        .action-buttons {
          margin-left: 0;
          margin-right: 0;
        }
      }
    }
  }

  // 行为标识样式
  .behavior-tags {
    margin: 12px 0;

    .tag-category {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;

      .category-label {
        font-size: 12px;
        font-weight: 500;
        color: #606266;
        white-space: nowrap;
      }

      .behavior-tag {
        padding: 2px 8px;
        font-size: 12px;
        background-color: #f0f2f5;
        color: #606266;
        border-radius: 4px;
        white-space: nowrap;
      }
    }
  }

  // 对话内容样式
  .conversation-content {
    margin: 12px 0;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #409eff;

    .conversation-item {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .speaker {
        font-size: 12px;
        font-weight: 500;
        color: #409eff;
        margin-right: 8px;
      }

      .content {
        font-size: 12px;
        color: #606266;
        line-height: 1.4;
      }
    }
  }

  // 展开/收起动画
  .expand-enter-active,
  .expand-leave-active {
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .expand-enter,
  .expand-leave-to {
    max-height: 0;
    opacity: 0;
  }

  .expand-enter-to,
  .expand-leave {
    max-height: 500px;
    opacity: 1;
  }
}

.y-bar div + div {
  margin-left: 0px;
}
</style>
