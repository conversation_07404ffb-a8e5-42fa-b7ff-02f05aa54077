<template>
  <base-card class="object-card">
    <div class="body">
      <el-image
        :src="require('@/assets/images/object-card-icon.svg')"
        style="width: 88px; height: 88px"></el-image>
      <div class="info-wrapper">
        <h5 class="title">{{ data.APPEAL_NAME }}</h5>
        <div class="tag-list">
          <el-tag>{{ activeMenu[1] }}</el-tag>
        </div>
        <p class="alias">
          <span class="prefix">别名：</span><span>{{ data.ANOTHER_NAME }}</span>
        </p>
      </div>
    </div>
    <div class="btn-set">
      <el-button
        class="mini"
        type="danger"
        plain
        size="small"
        @click="$emit('delete-object')"
        >删除</el-button
      >
      <el-button
        class="mini"
        type="primary"
        size="small"
        @click="$emit('edit-object')"
        >编辑</el-button
      >
    </div>
    <!-- <div class="tag"><svg-icon icon="robot"></svg-icon></div> -->
  </base-card>
</template>

<script>
export default {
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    activeMenu: {
      type: Array,
      default: () => [], // [key, value]
    },
  },
  data() {
    return {}
  },
  computed: {},
  methods: {},
}
</script>

<style lang="scss" scoped>
.object-card {
  @include full;
  @include flex-col;
  align-items: flex-start;
  position: relative;
  padding: 24px;
  background: url('@/assets/images/object-card-bg.svg') no-repeat right/contain, linear-gradient(113deg, #f1f7ff 0%, #ffffff 100%);

  .body {
    @include flex-row;

    .el-image {
      flex: 0 0 88px;
    }
  }

  .info-wrapper {
    @include flex-col;
    align-items: flex-start;
    margin-left: 24px;
    height: 100%;

    .title {
      font-size: 20px;
      font-weight: bold;
      line-height: 30px;
      color: $txtColor;
    }

    .tag-list {
      .el-tag {
        padding: 0 8px;
        height: 20px;
        line-height: 20px;

        + .el-tag {
          margin-left: 8px;
        }
      }
    }

    .prefix {
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      color: $txtColor-light;
    }

    .alias {
      @include text-overflow(1);
    }
  }

  .btn-set {
    @include flex-row;
    justify-content: flex-end;
    width: 100%;
  }

  // .tag {
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   padding: 4px 8px;
  //   width: 32px;
  //   height: 24px;
  //   font-size: 16px;
  //   color: $txtColor-reverse;
  //   border-radius: 4px 0px 4px 0px;
  //   background: #22b8cf;
  // }
}
</style>
