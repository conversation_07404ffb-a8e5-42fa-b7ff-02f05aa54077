import verifyMethods from './verify.js'
import { etlFlowInfo, getEtlFlowByVer, excueteFlow, addFlow, updateFlow } from '@/api/etl-flow'
import { recoverLocal, saveLocal, removeLocal, initIdb } from './localStorage.js'

// 节点配置信息操作
export default {
  data() {
    return {
      nodeData: {}, // 所有节点的配置
      configuration: null, //调度配置
    }
  },
  computed: {
    // 截至到当前流程的column
    currentCols() {
      // 不存在选中节点
      if (!this.currentNode) {
        return []
      }

      const curIdx = this.getCurIdx(this.currentNode.id)
      let list = this.gatherColumns(curIdx)
      return list
    },
    // 当前节点配置信息
    currentData() {
      if (this.currentNode) {
        return this.nodeData[this.currentNode.id]
      } else {
        return null
      }
    },
    // 数据预览列
    previewCols() {
      if (this.targetNode) {
        return this.nodeData[this.targetNode.id]?.columns.filter((item) => item.show) || []
      } else {
        return []
      }
    },
  },
  watch: {},
  async created() {
    await initIdb()
    this.init()
  },
  methods: {
    // 初始化
    async init() {
      const { etlId, ver } = this.$route.query
      // 是否获取数据的标识
      let flag = true
      if (!ver) {
        let res = await recoverLocal(etlId)
        if (res) {
          try {
            await this.$confirm('检测到本地有未保存的编辑，是否恢复？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
            flag = false
            this.handleRes(res)
          } catch (e) { }
          // 无论恢复与否都清除数据
          removeLocal(etlId)
        }
      }

      if (flag) {
        await this.fetchData(etlId, ver)
      }

      // 初始化流程
      this.$nextTick(() => {
        this.initJsPlumb()
      })
    },
    // 获取流程数据
    async fetchData(etlId, ver) {
      if (!etlId) {
        return false
      }

      // 可编辑版本
      let api = etlFlowInfo
      const payload = { etlId }

      if (ver) {
        // 不可编辑版本
        api = getEtlFlowByVer
        payload.ver = ver
        this.editable = false
      }

      const [err, res] = await api(payload)
      if (res) {
        this.handleRes(res.data)
      }
    },
    handleRes(res) {
      if (!res) {
        return false
      }
      this.decomposeNodeData(res)
      this.$nextTick(() => {
        this.$refs.tab?.timedTask()
      })
    },
    // 提交（新增、编辑、执行）
    async submitData(flag) {
      if (!this.editable) {
        return false
      }
      this.$store.dispatch('resetVerify')
      this.verifyIntegrity()
      this.verifyNodeData()
      if (!this.$store.getters.verifyStatus) {
        this.$store.dispatch('notify')
        return false
      }

      if (this.data.nodeList.length > this.sortedNodes.length) {
        try {
          await this.$confirm('检测到存在未连接到流程的节点，继续执行将不会保存这些节点及配置信息，是否继续？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
        } catch (e) {
          return
        }
      }

      const payload = this.composePayload()
      if (flag === 'execute') {
        payload.dgEtlFlow.etlId = this.etlId
        this.executeFlow(payload)
      } else {
        if (this.etlId) {
          payload.dgEtlFlow.etlId = this.etlId
          this.updateFlow(payload)
        } else {
          this.addFlow(payload)
        }
      }
      this.removeExtra()
    },
    // 执行
    executeFlow(data) {
      excueteFlow(data).then(([err, res]) => {
        if (!err && res.state == 1 && res.data) {
          this.$message.success(res.msg || '执行成功')
          if (!this.etlId) {
            this.tempId = res.data
          }
          this.$refs.tab?.timedTask()
        } else {
          this.$message.error(res?.msg || '未知错误')
        }
      })
    },
    // 新增
    async addFlow(data) {
      console.log('addFlow', data)
      const [err, res] = await addFlow(data)
      if (res && res.state == 1 && res.data) {
        this.$message.success(res.msg || '执行成功')
        try {
          await this.$confirm('是否继续编辑?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          this.etlId = res.data
          this.$router.push({
            name: 'EtlFlow', query: {
              etlId: res.data,
            }
          })
        } catch (err) {
          //关闭标签页
          top.close()
        }
      }

      this.handleUnsave(err, data)
    },
    // 编辑
    async updateFlow(data) {
      const [err, res] = await updateFlow(data)
      if (res && res.state == 1) {
        this.$message.success(res.msg || '编辑成功')
        this.fetchData()
      }

      this.handleUnsave(err, data)
    },
    // 接口成功则清除缓存，否则缓存并提示
    handleUnsave(err, data) {
      console.log('handleUnsave', data)
      if (err) {
        saveLocal(this.etlId, data)
        this.$notify({
          message: '检测到保存失败，已将配置数据保存至本地，可在下次打开页面时恢复',
          type: 'success',
          duration: 800,
        })
      } else {
        removeLocal(this.etlId)
      }
    },
    // 初始化node数据
    initNodes(flowRenderJson) {
      try {
        flowRenderJson = JSON.parse(flowRenderJson)
      } catch (err) {
        flowRenderJson = {
          edges: [],
          nodes: [],
        }
      }
      this.data.lineList = flowRenderJson.edges
      this.data.nodeList = flowRenderJson.nodes.map(i => {
        // 每次获取数据数据治理组件（nodeType:6）按照当前配置更新
        if (i.dataComId && this.moduleDict[i.dataComId]) {
          // 目前是配置数据覆盖节点数据
          i = Object.assign({}, i, this.moduleDict[i.dataComId])
        }
        i.nodeType = String(i.nodeType)
        return i
      })
      this.fixNodesPosition()
    },
    // 更新配置信息
    updateNodeData(node, data) {
      console.log('updateNodeData', node, data)
      // 调度配置
      if (!node) {
        this.configuration = { ...data }
      } else {
        let find = this.sortedNodes.find(item => item.id === node.id)
        if (find) {
          this.verifyNodeData()
        }
        this.$set(this.nodeData, node.id, data)
        // 防止两次打开同一个节点的配置，由于currentNode没变，currentData也不变
        this.currentNode = null
      }
    },
    // 删除节点后清空后续节点的增量字段
    removeColumns(nodeList, columns) {
      console.log('removeColumns', nodeList, columns)
      for (let i = 0; i < nodeList.length; i++) {
        const node = nodeList[i]
        // 数据源、派生列、数据处理组件跳过
        if (!node.nodeType || node.nodeType === '1' || node.nodeType === '5' || (node.nodeType === '6' && node.modelType !== '1')) {
          continue
        }

        let data
        let propName = 'column'

        if (node.nodeType === '6' && node.modelType === '1') {
          // 事务组件
          data = [this.nodeData?.[node.id]]
          propName = node.dgDataComConfigKeys.filter(item => item.KEY_TYPE === '3').map(item => item.KEY_NAME)
        } else if (node.nodeType === '7') {
          // 目标源
          data = this.nodeData?.[node.id]?.columns
          propName = 'mapColumn'
        } else {
          // 数据预处理组件（除派生列）
          data = this.nodeData?.[node.id]
        }

        console.log('removeColumns-data', i, data, columns)

        if (!data || data.length === 0) {
          continue
        }

        this.checkMapColumn(node, data, columns, propName, 'negative')
      }
    },
    // 解构请求到的node数据
    decomposeNodeData(res) {
      console.log('decomposeNodeData')
      if (!res) {
        return false
      }
      if (res.dgEtlFlow) {
        let dgEtlFlow = res.dgEtlFlow
        this.etlId = dgEtlFlow.etlId
        this.ver = dgEtlFlow.ver
        this.formData = {
          etlName: dgEtlFlow.etlName,
          maxThreadCount: dgEtlFlow.maxThreadCount,
        }
        this.initNodes(dgEtlFlow.flowRenderJson)
        this.configuration = {
          dispatchForm: dgEtlFlow.scheduleJson,
          callbackForm: dgEtlFlow.callBackJson,
        }
        // 解析数据源
        let srcNode = this.data.nodeList.find(item => item.nodeType === '1')
        if (srcNode && dgEtlFlow.srcDatasetJson) {
          this.nodeData[srcNode.id] = dgEtlFlow.srcDatasetJson
        }
        // 解析目标源
        let tagNode = this.data.nodeList.find(item => item.nodeType === '7')
        if (srcNode && dgEtlFlow.tagDatasetJson) {
          this.nodeData[tagNode.id] = dgEtlFlow.tagDatasetJson
        }
      }
      if (res.dgEtlFlowNodes) {
        let dgEtlFlowNodes = res.dgEtlFlowNodes
        if (dgEtlFlowNodes.length > 0) {
          // 解析数据预处理组件
          dgEtlFlowNodes.forEach(item => {
            // 跳过无nodeType和数据源、目标源
            if (!item.nodeType || item.nodeType === '1' || item.nodeType === '7') {
              return
            }
            // 跳过数据处理组件
            if (item.nodeType === '6' && item.modelType !== '1') {
              return
            }
            this.nodeData[item.nodeId] = item.nodeConfigJson.filterDatas
          })
        }
      }
      return res
    },
    // 组合提交入参
    composePayload() {
      console.log('composePayload', this.nodeData, this.sortedNodes)
      // 获取dgEtlFlowNodes
      const dgEtlFlowNodes = []
      let srcConfig
      let tagConfig
      for (let i = 0; i < this.sortedNodes.length; i++) {
        const node = this.sortedNodes[i]
        const item = this.nodeData[node.id]

        // 数据源
        if (i === 0) {
          srcConfig = item
        }

        // 目标源
        if (i === this.sortedNodes.length - 1) {
          tagConfig = item
        }

        if (!node.nodeType) {
          continue
        }

        const formatted = {
          nodeName: node.name,
          nodeOrder: i + 1,
          nodeType: node.nodeType,
          nodeCode: i,
          nodeId: node.id,
          pNodeId: i === 0 ? 0 : this.sortedNodes[i - 1].id,
          cNodeId: i === this.sortedNodes.length - 1 ? '' : this.sortedNodes[i + 1].id,
          nodeConfigJson: {},
          etlId: this.etlId,
        }

        // 数据预处理组件
        if (node.nodeType === '6') {
          formatted.dataComId = node.dataComId
          formatted.modelType = node.modelType
        }

        if (node.nodeType !== '1' && node.nodeType !== '7') {
          item && (formatted.nodeConfigJson = { filterDatas: item })
        }
        dgEtlFlowNodes.push(formatted)
      }

      // 获取dgEtlFlow
      const dgEtlFlow = {
        ...this.formData,
        scheduleJson: this.configuration?.dispatchForm,
        callBackJson: this.configuration?.callbackForm,
        srcDsId: srcConfig.dsId,
        srcDatasetJson: srcConfig,
        tagDsId: tagConfig.dsId,
        tagDatasetJson: tagConfig,
        flowRenderJson: JSON.stringify({ nodes: this.sortedNodes, edges: this.sortedLines }),
        ver: this.ver || '1',
      }
      const payload = {
        dgEtlFlowNodes,
        dgEtlFlow,
      }
      return payload
    },
    // 获取到某节点之前的映射字段
    gatherColumns(end, start = 0, nodeList = this.sortedNodes) {
      let list = []
      for (let i = start; i < end; i++) {
        let node = nodeList[i]
        if (!node) {
          break
        }

        let data = this.nodeData[node.id]
        if (!data && node.nodeType !== '6') {
          continue
        }

        let columns = this.getColumnsByNode(node)

        for (let i = 0; i < columns.length; i++) {
          let col = columns[i]
          let index = list.findIndex(find => find.column === col.column)
          // 防重
          if (index === -1) {
            const item = {
              column: col.column,
              // 派生列备注字段是remarks
              columnLabel: node.nodeType === '5' ? col.remarks : col.columnLabel,
              columnType: col.columnType,
              orderColumn: col.orderColumn || false,
              syncColumn: col.syncColumn || false,
            }
            list.push(item)
          }
        }
      }
      return list
    },
    // 获取
    getColumnsByNode(node) {
      let columns = []

      if (node.nodeType === '1') {
        // 数据源
        let data = this.nodeData[node.id]
        if (data?.columns && Array.isArray(data.columns)) {
          data.columns.forEach(col => {
            if (!col.show) {
              return false
            }
            const item = {
              // excel数据源后续column取映射字段mapColumn
              column: this.sourceType === 'ds' ? col.column : col.mapColumn,
              columnLabel: col.columnLabel,
              columnType: col.columnType,
            }
            columns.push(item)
          })
        }
      } else if (node.nodeType === '6' && node.dgDataComKeys) {
        // 数据处理组件
        columns = node.dgDataComKeys
      } else if (node.nodeType === '5' && this.nodeData[node.id]) {
        // 派生列
        columns = this.nodeData[node.id]
      }
      else if (node.nodeType === '8' && this.nodeData[node.id]) {
        // 格式转换
        columns = this.nodeData[node.id].map(col => {
          return {
            column: col.mapColumn,
            columnLabel: col.column + ' 格式转换',
            columnType: '5',
          }
        })
      } else if (node.nodeType === '9' && this.nodeData[node.id]) {
        // 数据分割
        columns = this.nodeData[node.id].map(col => {
          return col.targets.map(tag => {
            return {
              column: tag.mapColumn,
              columnLabel: col.column + ' 数据分割-' + tag.index,
              columnType: '1',
            }
          })
        }).flat()
      }

      return columns
    },
    // 根据id获取node在sortedNodes的索引
    getCurIdx(id) {
      return this.sortedNodes.findIndex(node => node.id === id)
    },
    addNodeCallback(node) {
      this.$set(this.nodeData, node.id, null)
    },
    deleteNodeCallback(node) {
      delete this.nodeData[node.id]
    },
    removeExtra() {
      this.data = {
        nodeList: JSON.parse(JSON.stringify(this.sortedNodes)),
        lineList: JSON.parse(JSON.stringify(this.sortedLines)),
      }

      for (const key in this.nodeData) {
        let find = this.sortedNodes.find(node => node.id === key)
        if (!find) {
          delete this.nodeData[key]
        }
      }
    },
    ...verifyMethods,
  },
}
