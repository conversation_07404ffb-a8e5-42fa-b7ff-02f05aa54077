<template>
  <base-card
    id="high-incidence-manage"
    class="y-page">
    <aside-bar
      ref="asideBar"
      @set-current="setCurrent($event)"></aside-bar>
    <div class="y-container no-padding">
      <div class="y-bar">
        <h2 class="y-title">热词高发事件预警</h2>
      </div>
      <div
        class="y-container--tight"
        v-loading="loading">
        <config-card
          v-for="(item, idx) in new Array(3)"
          :key="generateId()">
          <template #left>
            <div class="title-wrapper">
              <el-image
                :src="require(`@/assets/images/alarm-icon-${idx}.svg`)"
                style="width: 40px; height: 40px; margin-right: 8px"></el-image>
              <h5 class="title">
                {{ alarmMap[idx] + '风险热词事件预警' }}
              </h5>
            </div>
            <template v-if="alarmList[idx]">
              <div class="process-mode">
                <span class="prefix">处理方式：</span>
                <p class="content">{{ alarmList[idx].HANDLE_METHOD }}</p>
              </div>
              <div class="create-data">
                <span class="prefix">创建时间：</span>
                <span>{{ alarmList[idx].UPDATE_TIME }}</span>
                <span
                  class="prefix"
                  style="margin-left: 24px"
                  >创建人：</span
                >
                <span>{{ alarmList[idx].UPDATE_NAME }}</span>
              </div>
            </template>
          </template>
          <template #right>
            <el-checkbox
              :value="alarmList[idx] && alarmList[idx].STATUS"
              true-label="1"
              false-label="0"
              @change="updateAlarm($event, idx)"
              >启用设置</el-checkbox
            >
            <el-button
              class="mini"
              type="primary"
              size="small"
              @click.native="openEdit(idx)"
              >配置</el-button
            >
          </template>
        </config-card>
      </div>
    </div>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        :active-menu="activeMenu"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getList, updateAlarm } from '@/api/high-incidence-manage'
import AsideBar from './components/AsideBar.vue'
import EditForm from './components/EditForm.vue'
import ConfigCard from '@/components/ConfigCard'
import { generateId } from '@/utils'

export default {
  name: 'HighIncidenceManage',
  components: {
    AsideBar,
    EditForm,
    ConfigCard,
  },
  props: {},
  data() {
    return {
      loading: false,
      activeMenu: null,
      alarmList: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
      alarmMap: {
        0: '低',
        1: '中',
        2: '高',
      },
    }
  },
  computed: {},
  watch: {
    activeMenu: {
      handler(menu) {
        if (menu) {
          this.fetchData()
        }
      },
      deep: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true
      const payload = {
        typeId: this.activeMenu.id,
      }
      const [err, res] = await getList(payload)
      if (res) {
        this.alarmList = res.data || []
      }

      this.loading = false
    },
    async updateAlarm(v, idx) {
      const data = this.alarmList[idx]
      const payload = {
        status: v,
        id: data.ID,
      }
      const [err, res] = await updateAlarm(payload)
      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
        })
        data.STATUS = v
      } else {
        this.fetchData()
      }
    },
    openEdit(idx) {
      const data = this.alarmList[idx]
      this.editDrawerTitle = this.alarmMap[idx] + '风险事件预警配置'
      this.editDrawerData = data
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    setCurrent(menu) {
      this.activeMenu = menu
    },
    generateId,
  },
}
</script>

<style lang="scss">
#high-incidence-manage {
  @include flex-row;
  min-width: 1097px;

  > .aside-bar {
    border-right: 1px solid $borderColor;
  }

  > .y-container {
    width: calc(100% - 240px);

    .y-bar {
      justify-content: flex-start;
      padding: 16px 24px;
      padding-bottom: 8px;
      border-bottom: 1px solid $borderColor;
    }

    > .y-container--tight {
      padding: 16px 24px;

      .config-card {
        flex: 0 0 max-content;
        min-height: 220px;

        .title-wrapper {
          @include flex-row;
        }

        .process-mode,
        .create-data {
          padding: 8px 16px;
          border-radius: 4px;
          background-color: transparentize($themeColor, 0.9);
        }

        .process-mode {
          @include flex-row;
          .content {
            @include text-overflow(3);
          }
        }

        .prefix {
          color: $txtColor-light;
        }

        + .config-card {
          margin-top: 16px;
        }
      }
    }
  }
}
</style>
