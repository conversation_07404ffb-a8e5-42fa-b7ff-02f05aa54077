<template>
  <base-card
    id="user-manage"
    class="y-page">
    <div class="y-bar">
      <h2 class="y-title">人员管理</h2>
      <span>关键字</span>
      <el-input
        v-model="formData.keyword"
        v-trim
        size="small"
        style="width: 220px"
        clearable
        placeholder="请输入关键字进行搜索"></el-input>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
      <el-button
        class="mini"
        type="primary"
        @click="downloadTemplate">
        <i class="el-icon-download"></i>
        下载模板
      </el-button>
      <el-button
        class="mini"
        type="primary"
        @click.native="openEdit(null)">
        <svg-icon icon="add"></svg-icon>
        新增人员
      </el-button>
      <el-upload
        ref="upload"
        action=""
        accept=".xlsx, .xls"
        :auto-upload="false"
        :show-file-list="false"
        :multiple="false"
        :on-change="importUser">
        <el-button
          class="mini"
          type="primary"
          plain
          style="margin-left: 8px">
          <svg-icon icon="enter"></svg-icon>
          批量导入
        </el-button>
      </el-upload>
    </div>
    <empty-wrapper
      class="y-container--tight"
      :toggle="!userList || userList?.length === 0"
      v-loading="loading">
      <div
        class="user-wrapper y-card-wrapper y-container--tight no-padding"
        v-reset-scroll>
        <user-card
          v-for="item in userList"
          :key="item.USER_ID"
          :data="item"
          @edit-user="openEdit(item)"
          @delete-user="deleteUser(item)">
        </user-card>
      </div>
    </empty-wrapper>
    <div class="footer">
      <pagination
        :current-page.sync="formData.pageIndex"
        :page-size.sync="formData.pageSize"
        :total="total"
        @page="fetchData"></pagination>
    </div>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getList, deleteUser, importUser } from '@/api/user-manage'
import { getRoleDict } from '@/api/role'
import { downloadFile } from '@/utils'
import EditForm from './components/EditForm.vue'
import UserCard from './components/UserCard.vue'

export default {
  name: 'UserManage',
  components: {
    UserCard,
    EditForm,
  },
  props: {},
  data() {
    return {
      loading: false,
      total: 0,
      formData: {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
        keyword: '',
      },
      userList: [],
      roleDict: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true

      const [err, res] = await getList(this.formData)
      if (res) {
        this.userList = res.data
        this.total = res.totalRow
      }
      this.getRoleDict()

      this.loading = false
    },
    async getRoleDict() {
      const [err, res] = await getRoleDict()
      if (res) {
        this.roleDict = res.data
      }
    },
    async deleteUser(data) {
      try {
        await this.$confirm('此操作将永久删除选中用户, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const [err, res] = await deleteUser(data.USER_ID)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    openEdit(data) {
      if (data) {
        this.editDrawerTitle = '编辑人员'
        this.editDrawerData = data
      } else {
        this.editDrawerTitle = '新增人员'
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    downloadTemplate() {
      // downloadFile('/dg-portal/servlet/user?action=DownUserImportTemplate')
      downloadFile('/dg-portal/servlet/user?action=ExportTemplate')
    },
    async importUser(file, fileList) {
      if (
        file.raw.type !== 'application/vnd.ms-excel' &&
        file.raw.type !== 'application/x-excel' &&
        file.raw.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ) {
        this.$message({
          message: '文件类型不正确，请重新上传',
          type: 'error',
          duration: 800,
        })
        return false
      }

      const payload = {
        file: file.raw,
      }

      const [err, res] = await importUser(payload)
      if (!err && res?.msg.includes('成功')) {
        this.$message({
          message: res.msg,
          type: 'success',
          duration: 3000,
          onClose: () => {
            this.fetchData()
          },
        })
      } else if (!err) {
        this.$message({
          message: res.msg,
          type: 'error',
          duration: 3000,
          onClose: () => {},
        })
      }
    },
  },
}
</script>

<style lang="scss">
#user-manage {
  background-color: transparent;

  > .y-bar {
    justify-content: flex-start;
    background-color: $bgColor;
    border-radius: 4px;
  }

  .user-wrapper {
    margin-top: 16px;
    grid-auto-rows: 208px;
    grid-template-columns: repeat(auto-fill, minmax(408px, 1fr));
  }

  .footer {
    width: 100%;
    background-color: $bgColor;
    border-radius: 4px;

    .base-pagination {
      float: right;
      padding: 16px 8px;
    }
  }
}
</style>
