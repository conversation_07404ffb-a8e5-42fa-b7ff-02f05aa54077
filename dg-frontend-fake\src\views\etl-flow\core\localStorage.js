// TODO: idb不支持的回退
import store from '@/store'

const user = store.getters.userInfo || {}
const BASE_KEY = 'ETL_FLOW'
let idb

// 获取本地存储
export async function recoverLocal(etlId) {
  const res = await idb.query({ etlId: etlId || 'temporary', user: user.USER_ACCT || 'unknown' })
  return res && res[0]?.config
}

// 新增/修改本地存储
export function saveLocal(etlId, data) {
  const item = {
    user: user.USER_ACCT,
    etlId: etlId || 'temporary',
    config: data,
  }
  idb.add(item, 'put')
}

// 删除本地存储
export async function removeLocal(etlId) {
  const res = await idb.query({ etlId: etlId || 'temporary', user: user.USER_ACCT })
  if (res && res[0]) {
    let id = res[0].id
    idb.delete(id)
  }
  return res
}

export async function initIdb() {
  idb = await new IndexedDB({
    name: BASE_KEY,
    version: 1,
    objectStore: [{
      name: 'configuration',
      options: {
        keyPath: 'id',
        autoIncrement: true,
      },
      indexes: [
        { name: 'id', unique: true },
        { name: 'user', unique: false },
        { name: 'etlId', unique: false },
      ]
    }],
  })

  idb.setTransaction('configuration', 'configuration', 'readwrite')
}

export default class IndexedDB {
  constructor(options) {
    return this.initIndexedDB(options)
  }

  initIndexedDB({ name, version, objectStore }) {
    this.name = name || 'unamed'
    this.version = version || 1
    this.objectStoreList = objectStore || []

    return new Promise((resolve, reject) => {
      if (!indexedDB) {
        reject('IndexedDB not supported')
      }

      const request = indexedDB.open(name, version)

      request.onsuccess = (evt) => {
        console.log('IndexedDB 打开成功', evt.target.result)
        this.db = evt.target.result
        this.db.onerror = (evt) => {
          console.error('IndexedDB 操作错误', evt)
        }
        resolve(this)
      }

      request.onerror = (evt) => {
        reject(evt)
        console.error('IndexedDB 打开失败', evt)
      }

      request.onupgradeneeded = (evt) => {
        console.log('IndexedDB 升级成功', evt)
        const objStoreList = evt.target.result.objectStoreNames
        this.objectStoreList.forEach(({ name, options, indexes }) => {
          if (!name || Array.prototype.includes.call(objStoreList, name)) {
            return false
          }
          const objStore = evt.target.result.createObjectStore(name, options || {})
          if (indexes) {
            for (const index in indexes) {
              const { options, name } = indexes[index] || {}
              objStore.createIndex(name, name, options || {})
            }
          }
        })
      }
    })
  }

  setTransaction(transaction, objectStore, mode) {
    this.transactionSetting = { transaction, objectStore, mode }
  }

  #openTransaction() {
    if (!this.transactionSetting) {
      console.warn('请先调用setTransaction设置事务')
      return false
    }
    const { transaction, objectStore, mode } = this.transactionSetting
    return this.db.transaction(transaction, mode).objectStore(objectStore)
  }

  get(id, idx) {
    const currentStore = this.#openTransaction()
    if (!currentStore) {
      return false
    }

    let request

    if (idx) {
      request = currentStore.index(idx).get(id)
    } else {
      request = currentStore.get(id)
    }

    return new Promise((resolve, reject) => {
      request.onsuccess = (evt) => {
        const result = evt.target.result
        resolve(result)
      }
    }).catch((err) => err)
  }

  query(idxs, mode = 'either') {
    console.log('query', idxs)

    const currentStore = this.#openTransaction()
    if (!currentStore) {
      return false
    }

    if (typeof idxs !== 'object') {
      console.warn('getByIdxs idxs传参错误')
      return false
    }

    if (['either', 'or'].indexOf(mode) === -1) {
      console.warn('getByIdxs mode传参错误')
    }

    idxs = Object.entries(idxs)
    let result = []

    return new Promise((resolve, reject) => {
      const request = currentStore
        .openCursor()
        .onsuccess = (evt) => {
          const cursor = evt.target.result
          if (cursor) {
            let matchMethod
            if (mode === 'either') {
              matchMethod = 'every'
            } else {
              matchMethod = 'some'
            }
            if (idxs[matchMethod](([key, value]) => cursor.value[key] === value)) {
              result.push(cursor.value)
            }
            cursor.continue()
          } else {
            resolve(result)
          }
        }

    }).catch((err) => err)
  }

  add(data, mode = 'add') {
    const currentStore = this.#openTransaction()
    if (!currentStore) {
      return false
    }

    if (typeof data === 'object') {
      if (Array.isArray(data)) {
        data.forEach(item => {
          currentStore[mode](item)
        })
      } else {
        currentStore[mode](data)
      }
    }
  }

  delete(id) {
    const currentStore = this.#openTransaction()
    if (!currentStore) {
      return false
    }

    currentStore.delete(id)
  }
}