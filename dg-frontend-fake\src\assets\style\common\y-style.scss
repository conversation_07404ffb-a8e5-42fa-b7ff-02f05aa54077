// y系列公共样式

// 页面
.y-page {
  @include full;
}

// 通用bar
.y-bar {
  @include flex-row;
  flex-wrap: wrap;
  padding: 12px 24px;
  padding-bottom: 4px;
  width: 100%;

  &.no-padding {
    padding: 0;
  }

  >* {
    margin-bottom: 8px;
  }

  .y-title {
    flex: 1;
    white-space: nowrap;
  }

  div {

    +div,
    +span,
    +button {
      margin-left: 16px;
    }
  }

  span {
    +div {
      margin-left: 8px;
    }
  }

  &+.y-bar,
  &+.y-container,
  &+.y-container--tight {
    padding-top: 0;
  }
}

// 普通容器，可以配合no-padding类名
.y-container {
  @include container;

  &.no-padding {
    padding: 0;
  }
}

// 限制大小overflow:auto的容器，可以配合no-padding类名
.y-container--tight {
  @include container;
  overflow: auto;

  &.no-padding {
    padding: 0;
  }
}

// 标题
.y-title {
  @include title;
}

// 前面有竖线的标题
.y-title--secondary {
  @include title;
  position: relative;
  margin-left: 12px;
  font-weight: 400;
  line-height: 16px;
  color: $txtColor;

  &:before {
    content: '';
    position: absolute;
    left: -12px;
    width: 4px;
    height: 100%;
    background-color: $themeColor;
  }
}

// table标签专用，模仿el-table样式
.y-table {
  overflow: hidden;
  flex: unset;
  border: 1px solid $borderColor;
  border-radius: 4px;

  &.el-table {
    &::before {
      content: none;
    }

    table {
      width: 100%;
      border-collapse: collapse;

      th,
      td {
        border-right: 1px solid $borderColor;
        border-bottom: 1px solid $borderColor;
      }

      td {
        &.el-table__cell {
          padding: 5px 16px;
        }
      }

      .el-table__cell .cell {
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: 0em;
        color: $txtColor;
      }

      .no-bb {
        border-bottom: none;
      }

      .no-br {
        border-right: none;
      }
    }
  }
}

// 卡片布局
.y-card-wrapper {
  display: grid;
  justify-content: space-around;
  gap: 16px;
  flex-wrap: wrap;
  overflow: unset;
  overflow-y: auto;
}

// 无数据
.y-no-data {
  display: block;
  width: 100%;
  text-align: center;
  color: $txtColor-light
}

// 复选框
.y-checkbox {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 20px;
  height: 20px;
  border-radius: 50px;
  background: $bgColor-dark;
  border: 1px solid #c5c5c5;
  cursor: pointer;
  z-index: 99;

  .svg-icon {
    display: none;
    position: absolute;
    top: 2px;
    left: 3px;
    font-size: 12px;
    color: $txtColor-reverse;
  }

  &.checked {
    background-color: $themeColor;
    border-color: $themeColor;

    .svg-icon {
      display: block;
    }
  }
}

// 可增删的input组
.y-multi-input {
  @include flex-col;
  width: 100%;

  .y-multi-input_item {
    @include flex-row;
    width: 100%;

    >div {
      flex: 1;

      +div {
        margin-left: 8px;
      }
    }

    .el-button {
      margin-left: 8px;
      padding: 12px;
    }

    +.y-multi-input_item {
      margin-top: 16px;
    }
  }
}