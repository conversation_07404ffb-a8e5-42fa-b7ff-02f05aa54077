<template>
  <div class="detail-table y-container no-padding">
    <div class="y-container--tight no-padding">
      <div class="header">
        <el-image
          :src="require('@/assets/images/training-card-icon.svg')"
          style="flex: 0 1 max-content; width: 60px; min-width: 60px; height: 60px"></el-image>
        <div class="wrapper">
          <overflow-text
            v-if="data?.FILE_NAME"
            :max="60"
            :content="data?.FILE_NAME"></overflow-text>
          <span>文件编号：{{ data?.FILE_ID }}</span>
        </div>
      </div>
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <template v-if="flag === 'detail' && tableHeader.length > 0">
          <el-table-column
            type="index"
            label="序号"
            width="50">
          </el-table-column>
          <el-table-column
            v-for="{ columnLabel, column } in tableHeader"
            :key="column"
            :prop="column"
            align="center"
            min-width="132"
            show-overflow-tooltip>
            <template
              slot="header"
              slot-scope="scope">
              <overflow-text
                :max="8"
                :content="columnLabel || column"></overflow-text>
            </template>
          </el-table-column>
        </template>
        <template v-else-if="flag === 'log'">
          <el-table-column
            type="index"
            label="序号"
            width="50">
          </el-table-column>
          <el-table-column
            label="失败ID"
            prop="FAIL_ID"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            label="失败原因"
            prop="FAIL_MESSAGE"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            label="行数据"
            prop="ROW_JSON"
            show-overflow-tooltip>
          </el-table-column>
        </template>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
  </div>
</template>

<script>
import { getDetail, getLog } from '@/api/data-set'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default: () => null,
    },
    flag: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      total: 0,
      tableLoading: false,
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        fileId: '',
      },
      tableHeader: [],
      tableData: [],
    }
  },
  computed: {},
  watch: {
    data: {
      handler(fileId) {
        if (fileId) {
          this.initPage()
          this.fetchData()
        }
      },
      immediate: true,
    },
  },
  methods: {
    initPage() {
      this.formData = {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        fileId: this.data?.FILE_ID,
      }
    },
    async fetchData() {
      this.tableLoading = true
      const payload = { ...this.formData }

      let fetchData = this.flag === 'log' ? getLog : getDetail

      const [err, res] = await fetchData(payload)
      if (!err) {
        if (this.flag === 'log') {
          this.tableData = res.data
        } else {
          try {
            this.tableHeader = JSON.parse(res.title.ROW_JSON).headConfig
            this.tableData = res.data.map(({ ROW_JSON }) => JSON.parse(ROW_JSON))
          } catch (e) {
            this.tableHeader = []
            this.tableData = []
          }
        }

        this.total = res.totalRow
      }
      this.$nextTick(() => {
        this.$refs.table.doLayout()
      })
      this.tableLoading = false
    },
  },
}
</script>

<style lang="scss" scoped>
.detail-table {
  > .y-container--tight {
    padding-bottom: 0;
    border-bottom: 1px solid $borderColor;

    .header {
      @include flex-row;
      margin: 24px 16px 16px;

      .wrapper {
        margin-left: 16px;

        .overflow-text > span,
        h3 {
          // @include text-overflow(1);
          margin-bottom: 4px;
          font-size: 16px;
          font-weight: bold;
          line-height: 24px;
          color: $txtColor;
        }

        span {
          font-size: 14px;
          font-weight: normal;
          line-height: 22px;
          color: $txtColor-light;
        }
      }
    }

    .el-table::before {
      content: none;
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }
}
</style>
