@import './config-card-page.scss';

.y-card-wrapper {
  @import '@/assets/style/modules/taggy.scss';
  @import './text-color.scss';

  margin: 0;
  padding: 16px 0;

  .config-card {
    .title-wrapper {
      @include flex-row;
    }
  }

  .config-wrapper {
    padding-top: 8px;
    // justify-content: center;
  }

  .sep {
    margin: 4px 24px;
    width: 1px;
    height: 16px;
    border: 1px solid $borderColor;
  }
}

.footer {
  width: 100%;
  background-color: $bgColor;
  border-radius: 4px;

  .base-pagination {
    float: right;
    padding: 16px 8px;
  }
}