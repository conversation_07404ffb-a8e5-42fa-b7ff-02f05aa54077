export default {
  // 校验流程完整性
  verifyIntegrity() {
    const verify = {
      type: 'integrity',
      msg: '',
    }
    if (!this.data.nodeList.find(node => node.nodeType === '1')) {
      verify.msg = '数据源节点缺少'
    } else if (!this.sortedNodes.find(node => node.nodeType === '1')) {
      verify.msg = '数据源节点未连接'
    } else if (this.sortedNodes[0]?.nodeType !== '1') {
      verify.msg = '数据源节点连接位置错误'
    } else if (!this.data.nodeList.find(node => node.nodeType === '7')) {
      verify.msg = '目标源节点缺少'
    } else if (!this.sortedNodes.find(node => node.nodeType === '7')) {
      verify.msg = '目标源节点未连接'
    } else if (this.sortedNodes[this.sortedNodes.length - 1]?.nodeType !== '7') {
      verify.msg = '目标源节点连接位置错误'
    }

    if (!verify.msg) {
      console.log('verifyIntegrity', true)
      return true
    }

    this.$store.dispatch('createLog', verify)
    console.log('verifyIntegrity', false)
    return false
  },
  // 校验配置信息
  verifyNodeData(idx = 0, list = this.sortedNodes) {
    console.log('verifyNodeData', idx, list)

    for (let i = idx; i < list.length; i++) {

      const node = list[i]

      // 没nodeType或者数据处理组件跳过
      if (!node.nodeType || (node.nodeType === '6' && node.modelType !== '1')) {
        continue
      }

      const columns = this.gatherColumns(i, idx, list)
      console.log('verifyNodeData-data', i, this.nodeData?.[node.id], columns)

      let data, propName

      if (node.nodeType === '1') {
        // 数据源
        data = this.nodeData?.[node.id]?.columns?.filter(item => item.show)
        if (!this.verifyDataEmpty(data, node)) break
      } else if (node.nodeType === '5') {
        // 派生列
        data = this.nodeData?.[node.id]
        if (!this.verifyDataEmpty(data, node)) break
      } else if (node.nodeType === '6' && node.modelType === '1') {
        // 事务处理组件
        data = this.nodeData?.[node.id] && [this.nodeData?.[node.id]]
        propName = node.dgDataComConfigKeys.filter(item => item.KEY_TYPE === '3').map(item => item.KEY_NAME)
        if (!this.verifyDataEmpty(data, node)) break
        if (!this.verifyColumnMissing(data, node, propName)) break
        this.checkMapColumn(node, data, columns, propName)
      } else if (node.nodeType === '7') {
        // 目标源
        data = this.nodeData?.[node.id]?.columns?.filter(item => item.show)
        propName = 'mapColumn'
        if (!this.verifyDataEmpty(data, node)) break
        if (!this.verifyColumnMissing(data, node, propName)) break
        this.checkMapColumn(node, data, columns, propName)
      } else {
        // 数据预处理组件（除派生列）
        data = this.nodeData?.[node.id]
        if (!this.verifyDataEmpty(data, node)) break
        if (!this.verifyColumnMissing(data, node, propName)) break
        this.checkMapColumn(node, data, columns, propName)
      }
    }
  },
  // 校对映射column是否存在，不存在就置空，
  // mode: 移除对比方式
  //  -position为移除columns里不存在的字段
  //  -negative为移除columns里存在的字段
  checkMapColumn(node, data, columns, propName = 'column', mode = 'positive') {
    console.log('checkMapColumn', node, data, columns, propName, mode)
    const verify = {
      node,
      type: 'column-removed',
      columns: [],
    }

    // TODO: 优化
    if (Array.isArray(propName)) {
      propName.forEach(propName => {
        for (let i = 0; i < data.length; i++) {
          let item = data[i]
          let find = columns.find(col => col.column === item[propName])
          if ((mode === 'positive' && !find) || (mode === 'negative' && find)) {
            verify.columns.push(item[propName])
            item[propName] = ''
          }
        }
      })
    } else {
      for (let i = 0; i < data.length; i++) {
        let item = data[i]
        let find = columns.find(col => col.column === item[propName])
        if ((mode === 'positive' && !find) || (mode === 'negative' && find)) {
          verify.columns.push(item[propName])
          item[propName] = ''
        }
      }
    }

    if (verify.columns.length > 0) {
      console.log('checkMapColumn', false)
      this.$store.dispatch('createLog', verify)
      return false
    } else {
      console.log('checkMapColumn', true)
      return true
    }
  },
  // 校验是否配置完整
  verifyDataEmpty(data, node) {
    if (!data || data?.length === 0) {
      const verify = {
        node,
        type: 'empty-config',
      }
      this.$store.dispatch('createLog', verify)
      console.log('verifyDataEmpty', false)
      return false
    }
    console.log('verifyDataEmpty', true)
    return true
  },
  // 校验column是否已缺失
  verifyColumnMissing(data, node, propName = 'column') {
    if (
      (typeof propName === 'string' &&
        data.some(item => { !item[propName] })) ||
      (Array.isArray(propName) &&
        data.some(item => { return propName.some(prop => !item[prop]) }))
    ) {
      const verify = {
        node,
        type: 'column-missing',
      }
      this.$store.dispatch('createLog', verify)
      console.log('verifyColumnMissing', false)
      return false
    }
    console.log('verifyColumnMissing', true)
    return true
  },
  // 校验必填字段是否存在
  // verifyRequires(data, node, requires) {
  //   if (Array.isArray(data)) {
  //     return data.every((item) => this.verifyRequires(item, node, requires))
  //   } else if (typeof data === 'object') {
  //     for (let i = 0, len = requires.length; i < len; i++) {
  //       let prop = requires[i]
  //       if (!data[prop]) {
  //         const verify = {
  //           node,
  //           type: 'empty-config',
  //         }
  //         this.$store.dispatch('createLog', verify)
  //         return false
  //       }
  //     }
  //     return true
  //   }
  //   return false
  // },
}

