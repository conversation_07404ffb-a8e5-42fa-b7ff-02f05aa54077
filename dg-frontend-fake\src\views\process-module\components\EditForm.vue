<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="150px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="组件类型"
            prop="modelType">
            <multi-switch
              v-model="formData.modelType"
              :options="moduleTypeList"></multi-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider><h3>通用配置</h3></el-divider>
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="数据治理组件名"
            prop="dataComName">
            <el-input
              v-model="formData.dataComName"
              v-trim
              clearable
              placeholder="请输入数据治理组件名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="服务名"
            prop="serviceId">
            <el-input
              v-model="formData.serviceId"
              v-trim
              clearable
              placeholder="请输入服务名"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="回调服务名"
            prop="callbackService">
            <el-select
              v-model="formData.callbackService"
              placeholder="组件处理结束后回调的SOA服务，用于处理个性化业务使用"
              clearable>
              <el-option
                v-for="(value, key) in serviceDict"
                :label="value"
                :value="key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="模型版本ID"
            prop="modelId">
            <el-input
              v-model="formData.modelId"
              v-trim
              clearable
              placeholder="请输入模型版本ID"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="formData.modelType !== '2'">
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="最大线程数"
              prop="serviceMaxLine">
              <el-input-number
                v-model="formData.serviceMaxLine"
                controls-position="right"
                :min="1"
                :max="50"
                placeholder="请输入最大线程数"
                style="width: 100%"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="超时重试次数"
              prop="serviceWaitCount">
              <el-input-number
                v-model="formData.serviceWaitCount"
                controls-position="right"
                :min="0"
                :max="10"
                placeholder="请输入超时重试次数"
                style="width: 100%"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="服务等待时长（秒）"
              prop="serviceMaxTime">
              <el-input-number
                v-model="formData.serviceMaxTime"
                controls-position="right"
                :min="0"
                :max="1800"
                placeholder="请输入服务等待时长（秒）"
                style="width: 100%"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="配置字段"
            prop="dataComKeys">
            <div class="y-multi-input">
              <div
                class="y-multi-input_item"
                v-for="(item, idx) in formData.dataComKeys">
                <el-input
                  v-model="item.keyName"
                  v-trim
                  clearable
                  placeholder="请输入字段名称"></el-input>
                <el-input
                  v-model="item.gwKeyName"
                  v-trim
                  clearable
                  placeholder="请输入原字段名称"></el-input>
                <el-input
                  v-model="item.keyComment"
                  v-trim
                  clearable
                  placeholder="请输入字段备注"></el-input>
                <el-button
                  type="primary"
                  plain
                  icon="el-icon-plus"
                  @click.native="addInput('dataComKeys')"
                  v-if="idx === 0"></el-button>
                <el-button
                  type="danger"
                  plain
                  icon="el-icon-delete"
                  @click.native="removeInput('dataComKeys', idx)"
                  v-else></el-button>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="说明"
            prop="serviceDesc">
            <el-input
              v-model="formData.serviceDesc"
              v-trim
              type="textarea"
              placeholder="请输入说明"
              maxlength="100"
              show-word-limit
              clearable
              :autosize="{ minRows: 3, maxRows: 10 }"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="formData.modelType === '1'">
        <el-divider><h3>事务组件配置</h3></el-divider>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="字段配置"
              prop="dataComConfigKeys">
              <div class="y-multi-input">
                <div
                  class="y-multi-input_item"
                  v-for="(item, idx) in formData.dataComConfigKeys">
                  <el-input
                    v-model="item.KEY_NAME"
                    v-trim
                    clearable
                    placeholder="请输入字段名"></el-input>
                  <el-input
                    v-model="item.KEY_LABEL"
                    v-trim
                    clearable
                    placeholder="请输入标签"></el-input>
                  <el-select
                    v-model="item.KEY_TYPE"
                    placeholder="请选择类型">
                    <el-option
                      v-for="(value, key) in keyTypes"
                      :label="value"
                      :value="key"></el-option>
                  </el-select>
                  <el-select
                    v-if="item.KEY_TYPE === '4'"
                    v-model="item.DICT_NAME"
                    placeholder="请选择字典"
                    filterable>
                    <el-option
                      v-for="item in dictList"
                      :label="item.NAME"
                      :value="item.CODE"></el-option>
                  </el-select>
                  <el-input
                    v-model="item.DEFAULT_VALUE"
                    v-trim
                    clearable
                    placeholder="请输入默认值"></el-input>
                  <el-button
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    @click.native="addInput('dataComConfigKeys')"
                    v-if="idx === 0"></el-button>
                  <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    @click.native="removeInput('dataComConfigKeys', idx)"
                    v-else></el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="字段说明"
              prop="dataComConfigDesc">
              <el-input
                v-model="formData.dataComConfigDesc"
                v-trim
                @keydown.native="handleKeydown"
                type="textarea"
                placeholder="请输入字段说明，以${}包裹的字段名在流程节点配置中将会替换为该字段对应的值显示"
                maxlength="500"
                show-word-limit
                clearable
                :autosize="{ minRows: 5, maxRows: 10 }"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import MultiSwitch from '@/components/MultiSwitch'
import { getDetail, addModule, updateModule, getServiceDict } from '@/api/process-module'
import { getDictList } from '@/api/dict-config'
import { extractParamsFromStr } from '@/utils'
import { MODULE_TYPE_LIST } from '@/utils/constant'

export default {
  components: { MultiSwitch },
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
    activeMenu: {
      type: String,
      default: '',
    },
  },
  data() {
    const validateKeys = (rule, value, callback) => {
      const notEmpty = value.filter((item) => {
        if (!item.keyName.trim() && !item.gwKeyName.trim() && !item.keyComment.trim()) {
          return false
        }
        if (!item.keyName.trim() || !item.gwKeyName.trim() || !item.keyComment.trim()) {
          callback(new Error('请完善字段配置'))
          return false
        }
        return true
      })

      // if (notEmpty.length === 0) {
      //   callback(new Error('请配置至少一个字段'))
      // }

      callback()
    }

    const validateConfigKeys = (rule, value, callback) => {
      const notEmpty = value.filter((item) => {
        if (!item.KEY_NAME.trim() && !item.KEY_LABEL.trim() && !item.KEY_TYPE.trim()) {
          return false
        }
        if (!item.KEY_NAME.trim() || !item.KEY_LABEL.trim() || !item.KEY_TYPE.trim()) {
          callback(new Error('请完善配置'))
          return false
        }
        return true
      })

      if (notEmpty.length === 0) {
        callback(new Error('请添加至少一个配置'))
      }

      callback()
    }

    const validateConfigDesc = (rule, value, callback) => {
      let paramList = extractParamsFromStr(value).map((item) => item[0])
      for (let i = 0, len = paramList.length; i < len; i++) {
        const key = paramList[i].slice(2, -1)
        let find = this.formData.dataComConfigKeys.find((item) => item.KEY_NAME === key)
        if (!find) {
          callback('说明中存在未配置的参数：' + key)
          return false
        }
      }
      callback()
    }

    const validateServiceOrModelId = (rule, value, callback) => {
      if (!this.formData.serviceId && !this.formData.modelId) {
        callback(new Error('请至少填写服务名或模型版本ID'));
      } else {
        callback();
      }
    }

    return {
      contentBefore: null,
      formData: {
        modelType: '0',
        dataComName: null,
        modelId: null,
        serviceId: null,
        callbackService: null,
        serviceMaxLine: null,
        serviceMaxTime: null,
        serviceWaitCount: null,
        dataComKeys: [{ keyName: '', gwKeyName: '', keyComment: '' }],
        serviceDesc: null,
        dataComConfigKeys: [{ KEY_NAME: '', KEY_LABEL: '', KEY_TYPE: '', DEFAULT_VALUE: '' }],
        dataComConfigDesc: '',
      },
      rules: {
        modelType: [{ required: true, message: '请选择组件类型', trigger: 'change' }],
        busiType: [{ required: true, message: '请选择事务组件类型', trigger: 'blur' }],
        dataComName: [{ required: true, message: '请输入数据治理组件名', trigger: 'blur' }],
        serviceId: [{ validator: validateServiceOrModelId, trigger: 'blur' }],
        dataComKeys: [{ validator: validateKeys, trigger: 'blur' }],
        // serviceDesc: [{ required: true, message: '请输入说明', trigger: 'blur' }],
        serviceMaxLine: [{ required: true, message: '请输入最大线程数', trigger: 'blur' }],
        serviceMaxTime: [{ required: true, message: '请输入服务等待时长（秒）', trigger: 'blur' }],
        serviceWaitCount: [{ required: true, message: '请输入超时重试次数', trigger: 'blur' }],
        dataComConfigKeys: [{ required: true, validator: validateConfigKeys, trigger: 'blur' }],
        dataComConfigDesc: [{ validator: validateConfigDesc, trigger: 'blur' }],
      },
      serviceDict: {},
      moduleTypeList: MODULE_TYPE_LIST,
      keyTypes: {
        1: '字符串',
        2: '数字',
        3: '流程字段',
        4: '字典值',
      },
      dictList: [],
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
    this.getServiceDict()
    this.getDictList()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data?.DATA_COM_ID) {
        this.getDetail(this.data.DATA_COM_ID)
      } else {
        this.formData = {
          modelType: '0',
          dataComName: null,
          modelId: null,
          serviceId: null,
          callbackService: null,
          serviceMaxLine: null,
          serviceMaxTime: null,
          serviceWaitCount: null,
          dataComKeys: [{ keyName: '', gwKeyName: '', keyComment: '' }],
          serviceDesc: null,
          dataComConfigKeys: [{ KEY_NAME: '', KEY_LABEL: '', KEY_TYPE: '', DEFAULT_VALUE: '' }],
          dataComConfigDesc: '',
        }
      }
    },
    async getDetail(id) {
      const [err, res] = await getDetail(id)
      if (res) {
        const data = res.data
        this.contentBefore = data
        this.$set(this.formData, 'modelType', data?.MODEL_TYPE)
        this.$set(this.formData, 'dataComName', data?.DATA_COM_NAME)
        this.$set(this.formData, 'modelId', data?.MODEL_ID)
        this.$set(this.formData, 'serviceId', data?.SERVICE_ID)
        this.$set(this.formData, 'callbackService', data?.CALLBACK_SERVICE)
        this.$set(this.formData, 'serviceMaxLine', data?.SERVICE_MAXLINE)
        this.$set(this.formData, 'serviceMaxTime', data?.SERVICE_MAXTIME)
        this.$set(this.formData, 'serviceWaitCount', data?.SERVICE_WAIT_COUNT)
        const keys = data.dataComKeys.map(({ KEY_NAME, KEY_COMMENT, GW_KEY_NAME }, idx) => {
          return { keyName: KEY_NAME, gwKeyName: GW_KEY_NAME, keyComment: KEY_COMMENT }
        })
        this.$set(this.formData, 'dataComKeys', keys)
        this.$set(this.formData, 'serviceDesc', data?.SERVICE_DESC)

        // 事务组件配置
        let configKeys
        if (data?.dataComConfigKeys && data?.dataComConfigKeys.length > 0) {
          configKeys = data?.dataComConfigKeys
        } else {
          configKeys = [{ KEY_NAME: '', KEY_LABEL: '', KEY_TYPE: '', DEFAULT_VALUE: '' }]
        }
        this.$set(this.formData, 'dataComConfigKeys', configKeys)

        this.$set(this.formData, 'dataComConfigDesc', data?.CONFIG_DESC)
      }
    },
    async getServiceDict() {
      const [err, res] = await getServiceDict()
      if (res) {
        this.serviceDict = res.data
      }
    },
    async getDictList() {
      const payload = {
        pageSize: 999,
        pageIndex: 1,
        pageType: 3,
      }
      const [err, res] = await getDictList(payload)
      if (res) {
        this.dictList = res.data || []
      }
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const payload = {
          ...this.formData,
        }

        let submitAction = addModule
        // 有id为更新，无id为新增
        if (this.data?.DATA_COM_ID) {
          submitAction = updateModule
          payload.dataComId = this.data?.DATA_COM_ID
          payload.contentBefore = { ...this.contentBefore }
        } else {
          // 当前分类
          payload.typeId = this.activeMenu
        }

        // 去掉空值
        payload.dataComKeys = payload.dataComKeys.filter((item) => {
          return item.keyName.trim() && item.gwKeyName.trim() && item.keyComment.trim()
        })

        if (payload.modelType !== '1') {
          delete payload.dataComConfigDesc
          delete payload.dataComConfigKeys
        } else {
          payload.dataComConfigKeys = payload.dataComConfigKeys.filter((item) => {
            return item.KEY_NAME.trim() && item.KEY_LABEL.trim() && item.KEY_TYPE.trim()
          })
        }

        const [err, res] = await submitAction(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
    addInput(prop) {
      if (prop === 'dataComKeys') {
        this.formData[prop].push({ keyName: '', gwKeyName: '', keyComment: '' })
      } else if (prop === 'dataComConfigKeys') {
        this.formData[prop].push({ KEY_NAME: '', KEY_LABEL: '', KEY_TYPE: '', DEFAULT_VALUE: '' })
      }
    },
    removeInput(prop, idx) {
      this.formData[prop].splice(idx, 1)
    },
    handleKeydown(e) {
      let focusEl

      if (e.target === document.activeElement) {
        focusEl = e.target
      } else {
        return false
      }

      const { selectionStart, selectionEnd } = focusEl
      if (selectionStart !== selectionEnd) {
        return false
      }

      const keyList = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown']
      if (!keyList.includes(e.key)) {
        return false
      }

      const paramIndexList = this.findParamIndex(e.target.value)

      if (e.key === 'Backspace' || e.key === 'Delete') {
        this.handleDelete(e, selectionStart, paramIndexList)
      }
    },
    findParamIndex(str) {
      return extractParamsFromStr(str).map((item) => {
        return {
          start: item.index,
          end: item.index + item[0].length,
        }
      })
    },
    handleDelete(e, sel, paramIndexList) {
      let prop = e.key === 'Backspace' ? 'end' : 'start'
      let find = paramIndexList.find((item) => item[prop] === sel)
      if (find) {
        const { selectionStart, selectionEnd } = e.target
        let str = this.formData.dataComConfigDesc
        this.formData.dataComConfigDesc = str.slice(0, find.start) + str.slice(find.end)
        // 光标回归
        let dif = e.key === 'Backspace' ? find.end - find.start : 0
        const selection = {
          selectionStart: selectionStart - dif,
          selectionEnd: selectionStart - dif,
        }
        setTimeout(() => Object.assign(e.target, selection), 0)
        e.preventDefault()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;

  .el-divider {
    flex-shrink: 0;
    margin: 36px 0;
  }

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
