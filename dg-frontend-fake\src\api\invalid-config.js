import { get, post } from '@/http/request'
import { invalidConig } from '@/api/PATH'

// 无效工单配置 

// 分类
export function getClassList(formData) {
  return post(invalidConig.classList, formData)
}
export function addClass(formData) {
  return post(invalidConig.addClass, { data: JSON.stringify(formData) })
}

export function updateClass(formData) {
  return post(invalidConig.updateClass, { data: JSON.stringify(formData) })
}

export function deleteClass(id) {
  return post(invalidConig.deleteClass, { data: JSON.stringify({ typeId: id }) })
}

// 答复样例
export function getAnswerList(formData) {
  return post(invalidConig.answerList, formData)
}

export function addAnswer(formData) {
  return post(invalidConig.addAnswer, { data: JSON.stringify(formData) })
}

export function updateAnswer(formData) {
  return post(invalidConig.updateAnswer, { data: JSON.stringify(formData) })
}

export function deleteAnswer(id) {
  return post(invalidConig.deleteAnswer, { data: JSON.stringify({ answerId: id }) })
}