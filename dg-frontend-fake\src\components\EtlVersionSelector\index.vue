<template>
  <div class="etl-version-selector">
    <span>历史版本：</span>
    <el-select
      value=""
      @visible-change="handleVisibleChange"
      filterable
      size="mini"
      style="display: inline-flex; width: 150px"
      placeholder="打开历史版本">
      <el-option
        v-for="ver in etlVerList"
        :key="ver.VER"
        :label="`version. ${ver.VER}`"
        :value="ver.VER"
        @click.native="openVersion(ver.VER)"></el-option>
    </el-select>
  </div>
</template>

<script>
import { getEtlVerList } from '@/api/etl'
import { getQueryString } from '@/utils'

export default {
  name: 'EtlVersionSelector',
  components: {},
  props: {
    etlId: {
      type: String,
    },
  },
  data() {
    return {
      etlVerList: null,
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    handleVisibleChange(visible) {
      if (visible && !this.etlVerList) {
        this.getEtlVerList(this.etlId)
      }
    },
    async getEtlVerList(etlId) {
      const payload = {
        etlId,
        pageIndex: 1,
        pageSize: 999,
      }
      const [err, res] = await getEtlVerList(payload)
      if (res) {
        this.etlVerList = res.data
      }
    },
    openVersion(ver) {
      window.open(
        '/#/etl-flow?' +
          getQueryString({
            etlId: this.etlId,
            ver,
          })
      )
    },
  },
}
</script>

<style lang="scss">
.etl-version-selector {
}
</style>
