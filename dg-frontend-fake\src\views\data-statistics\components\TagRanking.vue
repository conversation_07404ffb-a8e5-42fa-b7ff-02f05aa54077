<template>
  <div
    v-if="!data || data?.length === 0"
    :key="0"
    class="y-container--tight no-padding">
    <el-empty
      :image="require('@/assets/images/no-data.png')"
      description="暂无数据"></el-empty>
  </div>
  <div
    v-else
    :key="1"
    class="tag-ranking y-container no-padding">
    <div class="top">
      <div class="second">
        <div class="wrapper">
          <span class="name">{{ rankedList?.[1]?.name }}</span>
          <span
            class="prefix"
            v-show="rankedList?.[1]"
            >数量</span
          >
          <span class="count">{{ rankedList?.[1]?.value }}</span>
        </div>
      </div>
      <div class="first">
        <div class="wrapper">
          <span class="name">{{ rankedList?.[0]?.name }}</span>
          <span
            class="prefix"
            v-show="rankedList?.[0]"
            >数量</span
          >
          <span class="count">{{ rankedList?.[0]?.value }}</span>
        </div>
      </div>
      <div class="third">
        <div class="wrapper">
          <span class="name">{{ rankedList?.[2]?.name }}</span>
          <span
            class="prefix"
            v-show="rankedList?.[2]"
            >数量</span
          >
          <span class="count">{{ rankedList?.[2]?.value }}</span>
        </div>
      </div>
    </div>
    <div class="others">
      <div
        class="wrapper"
        v-for="({ name, value }, idx) in showList">
        <span class="index">{{ idx + 4 }}</span>
        <span class="name">{{ name }}</span>
        <div class="bar">
          <span
            class="bar-fill"
            :style="getWidth(value)"></span>
        </div>
        <span class="count">{{ value }}</span>
      </div>
      <el-popover
        placement="top"
        trigger="hover"
        popper-class="tag-ranking">
        <div
          class="overflow-item"
          v-for="(item, idx) in overflowList">
          <p class="label">{{ item.name }}</p>
          <p class="value">{{ item.value }}</p>
        </div>
        <p
          v-show="overflowList?.length > 0"
          class="tag-ranking_overflow"
          slot="reference">
          ...
        </p>
      </el-popover>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {}
  },
  computed: {
    rankedList() {
      return [...this.dataList].sort((a, b) => b.value - a.value)
    },
    dataList() {
      return this.data.map((item) => {
        return {
          name: item.DG_APPEAL_MOTIF,
          value: item.APPEAL_MOTIF_TOTAL,
        }
      })
    },
    showList() {
      return this.rankedList.slice(3, 10)
    },
    overflowList() {
      return this.rankedList.slice(10)
    },
  },
  methods: {
    getWidth(value) {
      return `width: ${(value / this.rankedList[0].value) * 100}%`
    },
  },
}
</script>

<style lang="scss" scoped>
.tag-ranking {
  @include flex-col;

  > .top {
    @include flex-row;
    flex: 1;
    margin-bottom: 24px;
    width: 100%;

    > div {
      flex: 1;
      position: relative;
      height: 100%;

      .wrapper {
        min-height: 136px;
      }

      &.second {
        background: url('@/assets/images/ranking-second-bg.svg') no-repeat bottom right/contain;

        .wrapper {
          bottom: 20px;
        }

        .name {
          background-color: #cdddf5;

          &::before {
            background: url('@/assets/images/ranking-second-icon.svg') no-repeat center/contain;
          }
        }
      }

      &.first {
        background: url('@/assets/images/ranking-first-bg.svg') no-repeat bottom right/contain;

        .wrapper {
          bottom: 40px;
        }

        .name {
          background-color: #feebcd;

          &::before {
            background: url('@/assets/images/ranking-first-icon.svg') no-repeat center/contain;
          }
        }
      }

      &.third {
        background: url('@/assets/images/ranking-third-bg.svg') no-repeat bottom right/contain;

        .wrapper {
          bottom: 10px;
        }

        .name {
          background-color: #f9e4d0;

          &::before {
            background: url('@/assets/images/ranking-third-icon.svg') no-repeat center/contain;
          }
        }
      }

      .wrapper {
        @include flex-col;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
      }

      .name {
        @include flex-center;
        position: relative;
        width: 80px;
        height: 80px;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        text-align: center;
        color: $txtColor;
        border-radius: 50%;

        &::before {
          content: '';
          position: absolute;
          top: -29px;
          width: 32px;
          height: 32px;
        }
      }

      .prefix {
        margin-top: 8px;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        color: $txtColor-light;
      }

      .count {
        font-size: 24px;
        font-weight: 700;
        line-height: 26px;
        color: $themeColor;
      }
    }
  }

  > .others {
    @include flex-col;
    align-items: flex-start;
    flex: 0 0 264px;
    width: 100%;

    .wrapper {
      @include flex-row;
      justify-content: flex-start;
      margin-bottom: 10px;
      width: 100%;
      font-size: 14px;
      font-weight: normal;
      line-height: 14px;
      color: $txtColor;
    }

    .index {
      flex: 0 0 24px;
      margin-right: 20px;
      width: 24px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      border-radius: 50%;
      background-color: $bgColor-dark;
    }

    .name {
      @include text-overflow(1);
      margin-right: 16px;
      flex: 0 0 100px;
    }

    .bar {
      flex: 1 0 200px;
      margin-right: 16px;
      height: 8px;
      border-radius: 4px;
      background: $bgColor-dark;
      overflow: hidden;

      .bar-fill {
        display: block;
        width: 0px;
        height: 100%;
        border-radius: 4px;
        background-color: $themeColor;
        transition: all 0.3s;
      }
    }

    .count {
      flex: 0 0 50px;
      font-size: 18px;
      font-weight: bold;
      line-height: 18px;
    }
  }

  .tag-ranking_overflow {
    margin-left: 6px;
    font-size: 14px;
    font-weight: 700;
    color: $txtColor;
    cursor: pointer;
  }

  .overflow-item {
    @include flex-row;
    min-width: 100px;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;

    .label {
      @include text-overflow(1);
      flex: 1;
    }

    .value {
      font-weight: 700;
    }
  }
}
</style>
