<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="100px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="角色名称"
            prop="ROLE_NAME">
            <el-input
              v-model="formData.ROLE_NAME"
              v-trim
              clearable
              placeholder="请输入角色名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="角色编码"
            prop="ROLE_ID">
            <el-input
              v-model="formData.ROLE_ID"
              v-trim
              :readonly="isEdit"
              clearable
              placeholder="请输入角色编码，未填则自动生成"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="24">
          <el-form-item
            label="启用状态"
            prop="ROLE_STATUS">
            <el-switch
              v-model="formData.ROLE_STATUS"
              active-color="#52C41A"
              active-value="1"
              inactive-value="0">
            </el-switch>
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { addRole, updateRole } from '@/api/role'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      formData: {
        ROLE_ID: null,
        ROLE_NAME: null,
        ROLE_TYPE: 2,
        // ROLE_STATUS: '0',
      },
      rules: {
        ROLE_NAME: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
      },
    }
  },
  computed: {
    isEdit() {
      return this.data?.ROLE_TYPE || this.data?.ROLE_TYPE === 0
    },
  },
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data?.ROLE_TYPE || this.data?.ROLE_TYPE === 0) {
        this.$set(this.formData, 'ROLE_ID', this.data?.ROLE_ID)
        this.$set(this.formData, 'ROLE_NAME', this.data?.ROLE_NAME)
        this.$set(this.formData, 'ROLE_TYPE', this.data?.ROLE_TYPE)
        // this.$set(this.formData, 'ROLE_STATUS', this.data?.ROLE_STATUS)
      } else {
        this.formData = {
          ROLE_ID: null,
          ROLE_NAME: null,
          ROLE_TYPE: 2,
          ROLE_STATUS: '0',
        }
      }
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const payload = {
          'role.ROLE_ID': this.formData.ROLE_ID,
          'role.ROLE_NAME': this.formData.ROLE_NAME,
          'role.ROLE_TYPE': this.formData.ROLE_TYPE,
          // 'role.ROLE_STATUS': this.formData.ROLE_STATUS,
        }

        let submitAction = addRole
        // 有id为更新，无id为新增
        if (this.isEdit) {
          submitAction = updateRole
        }

        const [err, res] = await submitAction(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
