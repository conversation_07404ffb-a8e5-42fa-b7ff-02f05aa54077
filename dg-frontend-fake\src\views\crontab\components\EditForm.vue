<template>
  <div class="edit-form y-container--tight no-padding">
    <el-form
      ref="editForm"
      class="y-container--tight"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="100px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="名称"
            prop="scriptName">
            <el-input
              v-model="formData.scriptName"
              v-trim
              clearable
              placeholder="请输入任务名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="命令/脚本"
            prop="exeCommand">
            <el-input
              v-model="formData.exeCommand"
              v-trim
              clearable
              placeholder="请输入命令/脚本"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="定时规则"
            prop="cronTab">
            <el-input
              v-model="formData.cronTab"
              v-trim
              clearable
              placeholder="请输入定时规则（秒 分 时 天 月 周）"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="24">
          <el-form-item
            label="执行前"
            prop="preCommand">
            <el-input
              v-model="formData.preCommand"
              v-trim
              clearable
              placeholder="请输入执行前命令"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="执行后"
            prop="subCommand">
            <el-input
              v-model="formData.subCommand"
              v-trim
              clearable
              placeholder="请输入执行后命令"></el-input>
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <div class="footer y-bar">
      <el-button
        type="text"
        class="btn-reset"
        @click.native="initPage">
        <svg-icon icon="reset"></svg-icon>
        重置
      </el-button>
      <el-button
        type="primary"
        plain
        size="small"
        @click.native="$emit('close-edit')"
        >取消</el-button
      >
      <el-button
        v-debounce="submitForm"
        type="primary"
        size="small"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { getDetail, addTask, updateTask } from '@/api/crontab'

export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return null
      },
    },
  },
  data() {
    return {
      contentBefore: null,
      formData: {
        scriptName: null,
        exeCommand: null,
        cronTab: null,
        // preCommand: null,
        // subCommand: null,
        state: '2',
      },
      rules: {
        scriptName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        exeCommand: [{ required: true, message: '请输入命令/脚本', trigger: 'blur' }],
        cronTab: [{ required: true, message: '请输入定时规则', trigger: 'blur' }],
      },
    }
  },
  computed: {},
  watch: {
    data: {
      handler() {
        this.initPage()
      },
      deep: true,
    },
  },
  created() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.$refs?.editForm?.resetFields()
      if (this.data?.ID) {
        this.getDetail(this.data.ID)
      } else {
        this.formData = {
          scriptName: null,
          exeCommand: null,
          cronTab: null,
          // preCommand: null,
          // subCommand: null,
          state: '2',
        }
      }
      this.$refs?.editForm?.resetFields()
    },
    async getDetail(id) {
      const [err, res] = await getDetail(id)
      if (res) {
        const data = res.data[0]
        this.contentBefore = data
        this.$set(this.formData, 'scriptName', data?.SCRIPT_NAME)
        this.$set(this.formData, 'exeCommand', data?.EXE_COMMAND)
        this.$set(this.formData, 'cronTab', data?.CRON_TAB)
        // this.$set(this.formData, 'preCommand', data?.PRE_COMMAND)
        // this.$set(this.formData, 'subCommand', data?.SUB_COMMAND)
        this.$set(this.formData, 'state', '2')
      }
    },
    submitForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        const payload = {
          ...this.formData,
        }

        let submitAction = addTask
        // 有id为更新，无id为新增
        if (this.data?.ID) {
          submitAction = updateTask
          payload.id = this.data?.ID
          payload.contentBefore = { ...this.contentBefore }
        }

        const [err, res] = await submitAction(payload)
        if (!err) {
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.$emit('close-edit')
              this.$refs.editForm.resetFields()
              this.$emit('update-data')
            },
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.edit-form {
  position: relative;

  .footer {
    justify-content: flex-end;
    border-top: 1px solid $borderColor;
  }

  .btn-reset {
    position: absolute;
    left: 0;
  }
}
</style>
