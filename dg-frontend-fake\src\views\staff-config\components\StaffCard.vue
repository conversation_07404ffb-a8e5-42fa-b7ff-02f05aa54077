<template>
  <base-card :class="['staff-card', gender]">
    <div class="header">
      <el-image
        :src="require(`@/assets/images/staff-card-icon_${gender}.svg`)"
        style="width: 56px; height: 56px"></el-image>
      <div class="info-wrapper">
        <h5 class="name">{{ data.INFO_NAME }}</h5>
        <p>
          <!-- <span class="gender">{{ data?.gender == 0 ? '男' : '女' }}</span> -->
          <span class="phone">{{ data.INFO_TEL }}</span>
        </p>
      </div>
    </div>
    <div class="body">
      <p style="grid-area: a"><span class="prefix">所属机构：</span>{{ data.ORG_NAME }}</p>
      <p style="grid-area: b"><span class="prefix">职位：</span>{{ data.INFO_JOB }}</p>
      <p style="grid-area: c"><span class="prefix">身份证号码：</span>{{ data.INFO_IDCARD }}</p>
    </div>
    <div class="btn-set">
      <el-button
        class="mini"
        type="danger"
        plain
        size="small"
        @click="$emit('delete-staff')"
        >删除</el-button
      >
      <el-button
        class="mini"
        type="primary"
        size="small"
        @click="$emit('edit-staff')"
        >编辑</el-button
      >
    </div>
  </base-card>
</template>

<script>
export default {
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {}
  },
  computed: {
    gender() {
      return 'male'
      // return this.data?.gender == 1 ? 'female' : 'male'
    },
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
.staff-card {
  @include full;
  @include flex-col;
  padding: 24px;
  padding-bottom: 16px;
  border: 1px solid $borderColor;

  &.male {
    background: url('@/assets/images/staff-card-bg_male.svg') no-repeat right top/120px 120px;

    .gender {
      color: $themeColor;
      background-color: transparentize($themeColor, 0.9);
    }
  }

  &.female {
    background: url('@/assets/images/staff-card-bg_female.svg') no-repeat right top/120px 120px;

    .gender {
      color: $color-danger;
      background-color: transparentize($color-danger, 0.9);
    }
  }

  .header {
    @include flex-row;
    align-self: flex-start;

    .info-wrapper {
      margin-left: 16px;
    }

    .name {
      margin-bottom: 8px;
      font-size: 18px;
      font-weight: 700;
      line-height: 22px;
      color: $txtColor;
    }

    .gender {
      margin-right: 8px;
      padding: 1px 8px;
      font-size: 12px;
      font-weight: normal;
      line-height: 18px;
      border-radius: 2px;
    }

    .phone {
      font-size: 14px;
      font-weight: normal;
      line-height: 16px;
      color: $txtColor-light;
    }
  }

  .body {
    display: grid;
    gap: 4px;
    grid-auto-rows: auto;
    grid-template-columns: 1fr 1fr;
    grid-template-areas:
      'a b'
      'c c';
    width: 100%;

    p {
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      color: $txtColor;
    }

    .prefix {
      color: $txtColor-light;
    }
  }

  .btn-set {
    align-self: flex-end;
  }
}
</style>
