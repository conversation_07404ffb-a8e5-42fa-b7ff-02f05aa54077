<template>
  <div
    v-if="!data || data?.length === 0"
    :key="0"
    class="y-container--tight no-padding">
    <el-empty
      :image="require('@/assets/images/no-data.png')"
      description="暂无数据"></el-empty>
  </div>
  <div
    v-else
    :key="1"
    class="urgency-chart y-container no-padding"
    ref="chart-container"></div>
</template>

<script>
import chartMixins from '@/mixins/chartMixins.js'
import style from '../style.js'
import { formatNumberWithComma } from '@/utils/formatter.js'

export default {
  components: {},
  mixins: [chartMixins, style],
  props: {
    data: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {}
  },
  computed: {
    labelList() {
      if (!this.data) {
        return []
      }
      return Object.entries(this.data).map(([key, value]) => '无效工单' + key)
    },
    dataList() {
      if (!this.data) {
        return []
      }
      return Object.entries(this.data).map(([key, value]) => {
        return {
          name: '无效工单' + key,
          value,
        }
      })
    },
    option() {
      return {
        color: this.color,
        tooltip: this.tooltipStyle,
        legend: this.sideLegend,
        grid: {
          left: 0,
        },
        series: [
          {
            name: '无效工单',
            type: 'pie',
            radius: '40%',
            center: ['35%', '55%'],
            data: this.dataList,
            itemStyle: {
              emphasis: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
            label: {
              formatter: '{text|{b}}\n{number|{d}%}',
              rich: this.rich,
            },
            ...this.labelLineStyle,
          },
        ],
      }
    },
  },
  methods: {},
}
</script>

<style lang="scss">
.urgency-chart {
}
</style>
