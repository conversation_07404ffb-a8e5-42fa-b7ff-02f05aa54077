import { get, post } from '@/http/request'
import { dataQuery } from '@/api/PATH'

// 数据标签统计
export function getTaskList(formData) {
  return post(dataQuery.taskList, formData)
}

export function getTargetList(formData) {
  return post(dataQuery.targetList, formData)
}

export function getTagQueryList(formData) {
  return post(dataQuery.tagQueryList, formData)
}

export function getEtlQueryList(formData) {
  return post(dataQuery.etlQueryList, formData)
}

export function getTagHeadList(formData) {
  return post(dataQuery.tagHeadList, formData)
}

export function getEtlHeadList(formData) {
  return post(dataQuery.etlHeadList, formData)
}

export function getTagDataList(formData) {
  return post(dataQuery.tagDataList, formData)
}

export function getEtlDataList(formData) {
  return post(dataQuery.etlDataList, formData)
}

export function getTagDict(formData) {
  return post(dataQuery.tagDict, formData)
}

export function getEtlDict(formData) {
  return post(dataQuery.etlDict, formData)
}

export function getTagDetail(formData) {
  return post(dataQuery.tagDetail, formData)
}

export function getEtlDetail(formData) {
  return post(dataQuery.etlDetail, formData)
}

// 旧导出接口
export function exportList(formData) {
  return get(dataQuery.export, formData, null, null, { responseType: 'blob' })
}

export function exportByTarget(formData) {
  return post(dataQuery.exportByTarget, formData)
}

export function exportByEtl(formData) {
  return post(dataQuery.exportByEtl, formData)
}
