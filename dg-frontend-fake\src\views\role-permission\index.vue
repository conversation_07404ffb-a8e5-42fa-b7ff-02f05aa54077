<template>
  <base-card
    id="role-permission"
    class="y-page">
    <div class="y-bar header">
      <h2 class="y-title">角色权限</h2>
      <span>关键字</span>
      <el-input
        v-model="formData.keyword"
        v-trim
        placeholder="请输入关键词进行搜索"
        size="small"
        clearable
        style="width: 220px"></el-input>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
      <el-button
        class="mini"
        type="primary"
        @click.native="openEdit(null)">
        <svg-icon icon="add"></svg-icon>
        新增角色
      </el-button>
    </div>
    <div class="y-container--tight">
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        v-reset-scroll="'div.el-table__body-wrapper'"
        stripe
        height="100%"
        fit
        ref="table"
        style="width: 100%">
        <el-table-column
          type="index"
          label="序号"
          width="100">
        </el-table-column>
        <el-table-column
          label="角色编码"
          prop="ROLE_ID">
        </el-table-column>
        <el-table-column
          label="角色名称"
          prop="ROLE_NAME">
        </el-table-column>
        <!-- <el-table-column
          label="状态"
          prop="ROLE_STATUS">
        </el-table-column> -->
        <el-table-column
          label="操作"
          width="250">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :underline="false"
              @click.native="openConfig(scope.row)"
              >权限配置</el-link
            >
            <el-link
              type="primary"
              :underline="false"
              @click.native="$router.push({ name: 'RoleUsers', query: { roleId: scope.row.ROLE_ID } })"
              >授权用户</el-link
            >
            <el-link
              v-if="scope.row.ROLE_TYPE != 0"
              type="primary"
              :underline="false"
              @click.native="openEdit(scope.row)"
              >编辑</el-link
            >
            <el-link
              v-if="scope.row.ROLE_TYPE != 0"
              type="danger"
              :underline="false"
              @click.native="deleteRole(scope.row)"
              >删除</el-link
            >
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/no-info.png')"
          description="暂无信息"></el-empty>
      </el-table>
    </div>
    <pagination
      :current-page.sync="formData.pageIndex"
      :page-size.sync="formData.pageSize"
      :total="total"
      @page="fetchData"></pagination>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
    <el-drawer
      title="权限配置"
      :visible.sync="configDrawerShow"
      direction="rtl"
      :size="420">
      <config-form
        ref="configForm"
        :data="configDrawerData"
        @close-edit="closeConfig"
        @update-data="fetchData"></config-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getList, deleteRole } from '@/api/role'
import EditForm from './components/EditForm.vue'
import ConfigForm from './components/ConfigForm'

export default {
  name: 'RolePermission',
  components: {
    EditForm,
    ConfigForm,
  },
  props: {},
  data() {
    return {
      tableData: [],
      tableLoading: false,
      total: 0,
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
        keyword: '',
      },
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
      configDrawerShow: false,
      configDrawerData: null,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.tableLoading = true
      const [err, res] = await getList(this.formData)
      if (res) {
        this.tableData = res.data
        this.total = res.totalRow
      }
      this.tableLoading = false
    },
    async deleteRole(data) {
      try {
        await this.$confirm('此操作将永久删除选中角色, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }

      const [err, res] = await deleteRole(data.ROLE_ID)
      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    openEdit(data) {
      if (data) {
        this.editDrawerTitle = '编辑角色'
        this.editDrawerData = data
      } else {
        this.editDrawerTitle = '新增角色'
        this.editDrawerData = null
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    openConfig(data) {
      this.configDrawerData = data
      this.configDrawerShow = true
    },
    closeConfig() {
      this.configDrawerShow = false
    },
  },
}
</script>

<style lang="scss">
#role-permission {
  > .header {
    border-bottom: 1px solid $borderColor;
  }

  > .y-container--tight {
    padding-top: 16px;
    padding-bottom: 0;
    border-bottom: 1px solid $borderColor;

    .el-table::before {
      content: none;
    }
  }

  .base-pagination {
    align-self: flex-end;
    padding: 24px 8px;
  }
}
</style>
