<template>
  <div id="y-login">
    <h1 class="y-login_logo">AI中台</h1>
    <el-form
      class="y-login_form"
      ref="loginForm"
      label-width="0"
      :model="formData"
      :rules="rules">
      <el-row>
        <el-col :span="24">
          <h2 class="y-login_title">用户登录</h2>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item prop="j_username">
            <el-input
              class="has-prefix"
              v-model="formData.j_username"
              name="j_username"
              autofocus
              autocomplete="on"
              placeholder="请输入用户名">
              <svg-icon
                slot="prefix"
                icon="user"></svg-icon>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item prop="j_password">
            <el-input
              class="has-prefix"
              v-model="formData.j_password"
              name="j_password"
              :type="pwdType"
              autocomplete="on"
              placeholder="请输入密码">
              <svg-icon
                slot="prefix"
                icon="lock"></svg-icon>
              <svg-icon
                class="btn-show-pwd"
                slot="suffix"
                :icon="pwdType === 'password' ? 'no-view' : 'view'"
                @click.native="showPwd"></svg-icon>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="17">
          <el-form-item prop="j_imagecode">
            <el-input
              v-model="formData.j_imagecode"
              name="j_imagecode"
              placeholder="请输入验证码">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-image
            class="y-login_code"
            :src="authCodeSrc"
            @click="getAuthCode"></el-image>
        </el-col>
      </el-row>
      <el-button
        class="y-login_btn"
        type="primary"
        @click.native="debouncedLogin"
        >登录</el-button
      >
    </el-form>
  </div>
</template>

<script>
import md5 from 'blueimp-md5'
import CryptoJS from 'crypto-js'
const ax = 'jzdk',
  ex = 'FY6f',
  zx = 'mLHg',
  jx = 'hkxA'

export default {
  name: 'Login',
  components: {},
  data() {
    return {
      lock: false,
      authCodeSrc: '',
      pwdType: 'password',
      formData: {
        j_username: '',
        j_password: '',
        j_imagecode: '',
        authKey: '',
      },
      rules: {
        j_username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        j_password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        // j_imagecode: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
      },
    }
  },
  computed: {},
  created() {
    this.logout()
    this.getAuthCode()
    this.debouncedLogin = this.debounce(this.login, 1000, true)
    window.addEventListener('keyup', this.enterAgent)
  },
  beforeDestroy() {
    window.removeEventListener('keyup', this.enterAgent)
  },
  methods: {
    handleClick() {},
    getMsgCode() {},
    getAuthCode() {
      const src = '/dg-portal/captcha?_t=' + new Date().getTime()
      this.authCodeSrc = src
    },
    login() {
      if (this.lock) {
        return false
      }

      this.$refs.loginForm.validate((valid) => {
        if (!valid) {
          return false
        }

        Promise.all([this.verifyCaptcha(), this.submitLogin()])
          .then((res) => {
            // console.log(res)
            if (res.every((item) => item?.data?.state == 1)) {
              this.lock = true
              this.$message({
                message: '登录成功！',
                type: 'success',
                duration: 800,
                onClose: () => {
                  window.location.href = process.env.VUE_APP_INDEX_PATH
                },
              })
            } else {
              return Promise.reject(res.find((item) => item?.data?.state != 1)?.data?.msg)
            }
          })
          .catch((err) => {
            // console.log(err)
            this.$message({
              message: err?.message || err,
              type: 'error',
              duration: 800,
              onClose: () => {},
            })
            this.getAuthCode()
            this.formData.j_imagecode = ''
          })
      })
    },
    submitLogin() {
      const payload = {
        data: JSON.stringify({
          j_username: this.aesEncrypt(this.formData.j_username),
          j_password: this.aesEncrypt(md5(this.formData.j_password)),
          j_imagecode: this.aesEncrypt(this.formData.j_imagecode),
          authKey: this.formData.authKey,
        }),
      }

      return this.$axios({
        url: '/dg-portal/sso?action=Login',
        method: 'post',
        data: payload,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' },
      })
    },
    verifyCaptcha() {
      const payload = {
        data: JSON.stringify({
          j_username: this.aesEncrypt(this.formData.j_username),
          j_imagecode: this.aesEncrypt(this.formData.j_imagecode),
        }),
      }

      return this.$axios({
        url: '/dg-portal/sso?action=SmsVerifyCheck',
        method: 'post',
        data: payload,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' },
      })
    },
    logout() {
      return this.$axios({
        url: '/dg-portal/sso?action=Logout',
        method: 'post',
      })
    },
    showPwd() {
      if (this.pwdType === '') {
        this.pwdType = 'password'
      } else {
        this.pwdType = ''
      }
    },
    aesEncrypt(word) {
      if (word) {
        let key = this.getKey()
        const aesKey = CryptoJS.enc.Utf8.parse(key)
        let srcs = CryptoJS.enc.Utf8.parse(word)
        let encrypted = CryptoJS.AES.encrypt(srcs, aesKey, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 })
        return encrypted.toString()
      } else {
        return ''
      }
    },
    getKey() {
      return zx + ax + jx + ex
    },
    enterAgent(e) {
      if (e.code === 'Enter') {
        this.debouncedLogin()
      }
    },
    debounce(func, wait, immediate) {
      let timeout, args, context, timestamp, result

      const later = function () {
        // 据上一次触发时间间隔
        const last = +new Date() - timestamp

        // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
        if (last < wait && last > 0) {
          timeout = setTimeout(later, wait - last)
        } else {
          timeout = null
          // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
          if (!immediate) {
            result = func.apply(context, args)
            if (!timeout) context = args = null
          }
        }
      }

      return function (...args) {
        context = this
        timestamp = +new Date()
        const callNow = immediate && !timeout
        // 如果延时不存在，重新设定延时
        if (!timeout) timeout = setTimeout(later, wait)
        if (callNow) {
          result = func.apply(context, args)
          context = args = null
        }

        return result
      }
    },
  },
}
</script>

<style lang="scss">
#y-login {
  position: relative;
  width: 100%;
  min-width: 650px;
  height: 100%;
  overflow: auto;
  background: url('@/assets/images/login-bg.png') no-repeat center/cover;

  .y-login_logo {
    position: absolute;
    top: 4%;
    left: 2.6%;
    color: #003483;
    font-size: 48px;
  }

  .y-login_form {
    overflow: auto;
    position: absolute;
    top: 50%;
    right: 8.2%;
    padding: 30px 62px 72px;
    width: 540px;
    max-height: 100vh;
    border-radius: 12px;
    background: $bgColor;
    box-shadow: 0px 8px 32px 0px rgba(5, 85, 206, 0.15);
    transform: translate(0, -50%);

    .el-row {
      &:nth-child(2) {
        margin-bottom: 8px;
      }

      &:nth-child(3) {
        margin-bottom: 28px;
      }

      &:nth-child(4) {
        margin-bottom: 48px;
      }
    }

    .el-form-item {
      height: 58px;

      .el-form-item__error {
        font-size: 16px;
      }
    }

    .el-input {
      input {
        font-size: 16px;
      }

      .el-input__inner {
        padding: 15px 24px;
        height: 58px;

        &:focus {
          background-color: $bgColor;
        }
      }

      &.has-prefix {
        .el-input__inner {
          padding-left: 100px;
        }
      }

      .el-input__suffix {
        padding: 15px 24px;
      }

      .el-input__prefix {
        padding: 15px 18px;

        &:after {
          content: '';
          position: absolute;
          top: 50%;
          right: 0;
          width: 1px;
          height: 21px;
          background-color: #d8d8d8;
          transform: translateY(-50%);
        }
      }
    }

    .svg-icon {
      font-size: 28px;
      font-weight: 500;
    }

    .btn-show-pwd {
      cursor: pointer;

      &:hover {
        color: $themeColor;
      }
    }
  }

  .y-login_title {
    margin-bottom: 48px;
    font-size: 28px;
    font-weight: 500;
    line-height: 42px;
    letter-spacing: 0em;
    color: $txtColor;
  }

  .y-login_btn {
    padding: 18px 0;
    width: 100%;
    text-align: center;
    font-size: 20px;
    font-weight: normal;
    line-height: normal;
    cursor: pointer;
  }

  .y-login_code {
    float: right;
    margin-left: 15px;
    width: auto;
    height: 58px;
    vertical-align: middle;
    cursor: pointer;
  }
}
</style>
