<template>
  <el-drawer
    :size="800"
    v-bind="$attrs"
    v-on="$listeners"
    title="数据分割节点配置">
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight no-padding"
        :disabled="!editable">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%"
          default-expand-all>
          <el-table-column
            label="序号"
            type="index"
            width="50">
          </el-table-column>
          <el-table-column type="expand">
            <template slot-scope="props">
              <template v-for="item in props.row.targets">
                <el-row>
                  <el-col :span="4">
                    <span
                      class="row-label"
                      style="padding-left: 48px"
                      >{{ item.index }}</span
                    >
                  </el-col>
                  <el-col :span="20">
                    <span class="row-label">目标字段</span>
                    <el-input
                      v-model="item.mapColumn"
                      v-trim
                      clearable
                      placeholder="请输入"
                      style="padding-left: 16px; width: calc(100% - 152px)"></el-input>
                  </el-col>
                </el-row>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="原字段名称">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.column"
                placeholder="请选择">
                <el-option
                  v-for="(item, key) in currentCols"
                  :key="key"
                  :value="item.column">
                  {{ item.column }} ({{ item.columnLabel }})
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="分割符">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.separator"
                clearable
                placeholder="请输入"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="分割数">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.limit"
                @change="handleLimitChange(scope.row)"
                :min="1"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column
            width="80"
            align="center">
            <template slot="header">
              <el-button
                @click="addRow"
                type="primary"
                icon="el-icon-plus"
                style="padding: 8px 12px"></el-button>
            </template>
            <template slot-scope="scope">
              <el-link
                type="danger"
                :underline="false"
                :disabled="!editable"
                @click.native="handleDelete(scope.$index, scope.row)"
                >删除</el-link
              >
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="footer y-bar">
        <el-button
          type="primary"
          plain
          size="small"
          @click.native="close"
          >取消</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="handelConfirm"
          >保存</el-button
        >
      </div>
    </div>
  </el-drawer>
</template>
<script>
export default {
  inheritAttrs: false,
  components: {},
  props: ['node', 'data', 'currentCols', 'editable'],
  data() {
    return {
      tableData: [],
    }
  },
  computed: {},
  watch: {},
  created() {
    if (this.data) {
      this.tableData = JSON.parse(JSON.stringify(this.data))
    }
  },
  methods: {
    addRow() {
      this.tableData.push({
        column: '',
        separator: '',
        limit: 1,
        targets: [
          {
            index: 0,
            mapColumn: '',
          },
        ],
      })
    },
    close() {
      this.$emit('update:visible', false)
    },
    handleLimitChange(row) {
      const { limit, targets } = row
      const diff = limit - targets?.length
      if (typeof diff !== 'number') {
        return false
      }
      if (diff > 0) {
        for (let i = 0, len = Math.abs(diff); i < len; i++) {
          targets.push({
            index: targets.length,
            mapColumn: '',
          })
        }
      } else {
        for (let i = 0, len = Math.abs(diff); i < len; i++) {
          targets.pop()
        }
      }
    },
    handelConfirm() {
      // 校验
      if (this.tableData.length <= 0) {
        this.$message.warning('请添加至少一个字段进行保存')
        return false
      }
      if (this.tableData.some((item) => !item.column)) {
        this.$message.warning('请选择原字段')
        return false
      }
      if (this.tableData.some((item) => !item.separator)) {
        this.$message.warning('请填入数据分割符')
        return false
      }
      if (this.tableData.some((item) => item.targets.some((target) => !target.mapColumn))) {
        this.$message.warning('请为分割子数据添加目标字段')
        return false
      }

      this.$emit('update', this.node, this.tableData)
      this.close()
    },
    handleDelete(index, row) {
      if (!this.editable) {
        return false
      }
      this.tableData.splice(index, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
.el-table::v-deep {
  .el-table__cell {
    &.el-table__expanded-cell {
      padding: 0;

      .el-row {
        padding: 12px 0;
        border-bottom: 1px solid $borderColor;

        &:hover {
          background-color: transparentize($themeColor, 0.97);
        }

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
  .el-table__body-wrapper {
    overflow: auto;
  }

  .row-label {
    display: inline-block;
    line-height: 40px;
    height: 40px;
  }
}
</style>
