<template>
  <el-drawer
    :size="800"
    direction="rtl"
    v-bind="$attrs"
    v-on="$listeners"
    @open="onOpen"
    @close="onClose"
    title="扩展配置">
    <div class="y-container no-padding">
      <div class="y-container--tight">
        <h3 class="y-title">调度配置</h3>
        <el-form
          class="y-container"
          ref="dispatchForm"
          :disabled="!editable"
          :model="dispatchForm"
          :rules="dispatchRules"
          size="medium"
          label-width="110px">
          <el-row>
            <el-col :span="24">
              <el-form-item
                label="执行设置"
                prop="runType">
                <el-radio-group
                  v-model="dispatchForm.runType"
                  size="medium">
                  <el-radio-button
                    v-for="(item, index) in runTypeOptions"
                    :key="index"
                    :label="item.value"
                    :disabled="item.disabled"
                    >{{ item.label }}</el-radio-button
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item
                label="执行时间"
                prop="beginDate">
                <el-date-picker
                  v-model="dispatchForm.beginDate"
                  @change="handleExecuteDateChange"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  :style="{ width: '50%' }"
                  placeholder="请选择执行时间"
                  :picker-options="beginDatePickerOptions"
                  clearable></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item
                label="结束时间"
                prop="endDate">
                <el-radio-group
                  v-model="dispatchForm.endDateType"
                  size="medium">
                  <el-radio :label="1">无结束时间</el-radio>
                  <el-radio :label="2">
                    <el-date-picker
                      v-model="dispatchForm.endDate"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      :style="{ width: '50%', 'min-width': '200px' }"
                      placeholder="请选择时间选择"
                      clearable
                      :picker-options="endDatePickerOptions"></el-date-picker>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <template v-if="dispatchForm.runType == 1">
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="执行范围"
                  prop="runScop">
                  <el-radio-group
                    v-model="dispatchForm.runScop"
                    size="medium">
                    <el-radio-button
                      v-for="(item, index) in runScopOptions"
                      :key="index"
                      :label="item.value"
                      :disabled="item.disabled"
                      >{{ item.label }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="dispatchForm.runScop == 2">
              <el-col :span="24">
                <el-form-item prop="runRange">
                  <template v-for="(item, index) in dispatchForm.runRange">
                    <div
                      class="range-item"
                      :key="index">
                      <el-time-select
                        placeholder="起始时间"
                        v-model="dispatchForm.runRange[index].beginTime"
                        :picker-options="{
                          start: '00:00',
                          end: '23:00',
                          step: '1:00',
                        }">
                        >
                      </el-time-select>
                      <el-time-select
                        placeholder="结束时间"
                        v-model="dispatchForm.runRange[index].endTime"
                        :picker-options="{
                          start: '00:00',
                          end: '23:00',
                          step: '1:00',
                          minTime: dispatchForm.runRange[index].beginTime,
                        }">
                      </el-time-select>
                      <el-button
                        v-if="index == 0"
                        @click="addRange"
                        type="primary"
                        plain
                        icon="el-icon-plus"></el-button>
                      <el-button
                        v-else
                        @click="removeRange(index)"
                        type="danger"
                        plain
                        icon="el-icon-minus"></el-button>
                    </div>
                  </template>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="执行频率"
                  prop="runInterval">
                  <el-input-number
                    v-model="dispatchForm.runInterval"
                    :min="1"
                    :max="60"
                    label="间隔"></el-input-number>
                  分钟
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="批次处理数据量"
                  prop="maxDealRowSize">
                  <el-input-number
                    v-model="dispatchForm.maxDealRowSize"
                    :min="1"></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template v-else>
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="执行范围"
                  prop="runTimes">
                  <template v-for="(item, index) in dispatchForm.runTimes">
                    <div
                      class="range-item"
                      :key="index">
                      <el-time-picker
                        placeholder="执行时间"
                        v-model="dispatchForm.runTimes[index]"
                        value-format="HH:mm"
                        :picker-options="{
                          format: 'HH:mm',
                        }">
                      </el-time-picker>
                      <el-button
                        v-if="index == 0"
                        @click="addTime"
                        type="primary"
                        plain
                        icon="el-icon-plus"></el-button>
                      <el-button
                        v-else
                        @click="removeTime(index)"
                        type="danger"
                        plain
                        icon="el-icon-minus"></el-button>
                    </div>
                  </template>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="批次处理数据量"
                  prop="maxDealRowSize">
                  <el-input-number
                    v-model="dispatchForm.maxDealRowSize"
                    :min="1"></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <el-row>
            <el-col :span="24">
              <el-form-item
                label="是否启用"
                prop="status"
                required>
                <el-switch
                  v-model="dispatchForm.status"
                  :active-value="1"
                  :inactive-value="0"
                  active-color="#52C41A"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <h3 class="y-title">回调配置</h3>
        <el-form
          class="y-container"
          ref="callbackForm"
          :disabled="!editable"
          :model="callbackForm"
          :rules="callbackRules"
          size="medium"
          label-width="110px">
          <el-row>
            <el-col :span="24">
              <el-form-item
                label="是否开启回调"
                prop="callbackActive">
                <el-switch
                  v-model="callbackForm.callbackActive"
                  active-color="#52C41A"
                  :active-value="1"
                  :inactive-value="0">
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col
              :span="12"
              v-if="callbackForm.callbackActive === 1">
              <el-form-item
                label="回调类型"
                prop="callbackType">
                <el-select
                  v-model="callbackForm.callbackType"
                  placeholder="请选择回调类型">
                  <el-option
                    label="内部"
                    :value="1"></el-option>
                  <el-option
                    label="外部"
                    :value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="callbackForm.callbackType === 2">
            <el-col :span="12">
              <el-form-item
                label="回调地址"
                prop="callbackUrl">
                <el-input
                  v-model="callbackForm.callbackUrl"
                  clearable
                  placeholder="请输入回调地址"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="footer y-bar">
        <el-button
          type="primary"
          plain
          size="small"
          @click.native="close"
          >取消</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="handelConfirm"
          >保存</el-button
        >
      </div>
    </div>
  </el-drawer>
</template>
<script>
import { getExcelDsPreviewDatas } from '@/api/etl-flow'

export default {
  inheritAttrs: false,
  components: {},
  props: ['editable', 'configuration'],
  data() {
    const validateEndDate = (rule, value, callback) => {
      if (this.dispatchForm.endDateType === 2 && !value) {
        callback('请选择结束时间')
      } else {
        callback()
      }
    }

    const validateRunRange = (rule, value, callback) => {
      if (Array.isArray(value) && value.filter((item) => item.beginTime || item.endTime).length > 0) {
        if (value.some((item) => !item.beginTime || !item.endTime)) {
          callback(new Error('执行范围请填写完整'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请填写至少一个执行范围'))
      }
    }

    const validateRunTimes = (rule, value, callback) => {
      if (Array.isArray(value) && value.filter((item) => item).length > 0) {
        if (value.some((item) => !item)) {
          callback(new Error('执行范围请填写完整'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请填写至少一个执行范围'))
      }
    }

    return {
      // 调度配置
      dispatchForm: {
        runType: 1,
        beginDate: null,
        endDateType: 1,
        endDate: '',
        runScop: 2,
        runRange: [{ beginTime: '', endTime: '' }],
        runTimes: [null],
        runInterval: 1,
        status: 1,
        maxDealRowSize: 1,
      },
      dispatchRules: {
        runType: [
          {
            required: true,
            message: '执行设置不能为空',
            trigger: 'change',
          },
        ],
        beginDate: [
          {
            required: true,
            message: '请选择执行时间',
            trigger: 'change',
          },
        ],
        endDate: [
          {
            validator: validateEndDate,
            message: '请选择结束时间',
          },
        ],
        runScop: [
          {
            required: true,
            message: '执行范围不能为空',
            trigger: 'change',
          },
        ],
        runRange: [
          {
            required: true,
            validator: validateRunRange,
            trigger: 'change',
          },
        ],
        runTimes: [
          {
            required: true,
            validator: validateRunTimes,
            trigger: 'change',
          },
        ],
        runInterval: [
          {
            required: true,
            message: '请输入执行频率',
            trigger: 'blur',
          },
        ],
      },
      runTypeOptions: [
        {
          label: '固定周期',
          value: 1,
        },
        {
          label: '按天执行',
          value: 2,
        },
      ],
      Options: [
        {
          label: '无结束时间',
          value: 1,
        },
        {
          label: '选项二',
          value: 2,
        },
      ],
      runScopOptions: [
        {
          label: '全天',
          value: 1,
        },
        {
          label: '自定义范围',
          value: 2,
        },
      ],
      beginDatePickerOptions: {
        disabledDate: (time) => {
          let now = new Date()
          let today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          return time.getTime() < today.getTime()
        },
      },
      endDatePickerOptions: {
        disabledDate: (time) => {
          if (this.dispatchForm.beginDate) {
            let executeDate = new Date(this.dispatchForm.beginDate)
            executeDate.setDate(executeDate.getDate() - 1)
            return time.getTime() <= executeDate.getTime()
          }
          return false
        },
      },

      // 回调配置
      callbackForm: {
        callbackActive: 0,
        callbackType: 1,
        callbackUrl: '',
      },
      callbackRules: {
        callbackActive: [{ required: true, message: '请选择是否开启回调', trigger: 'change' }],
        callbackType: [{ required: true, message: '请选择回调类型', trigger: 'change' }],
        callbackUrl: [{ required: true, message: '请输入回调地址', trigger: 'blur' }],
      },
    }
  },
  computed: {},
  watch: {},
  created() {
    this.init()
  },
  methods: {
    init() {
      if (this.configuration) {
        const { dispatchForm, callbackForm } = JSON.parse(JSON.stringify(this.configuration))
        this.dispatchForm = dispatchForm || {
          runType: 1,
          beginDate: null,
          endDateType: 1,
          endDate: '',
          runScop: 2,
          runRange: [{ beginTime: '', endTime: '' }],
          runTimes: [null],
          runInterval: 1,
          status: 1,
          maxDealRowSize: 1,
        }
        this.callbackForm = callbackForm || {
          callbackActive: 0,
          callbackType: 1,
          callbackUrl: '',
        }
      }

      if (!this.dispatchForm.runRange || this.dispatchForm.runRange.length == 0) {
        this.dispatchForm.runRange = [{ beginTime: '', endTime: '' }]
      }

      if (!this.dispatchForm.runTimes || this.dispatchForm.runTimes.length == 0) {
        this.dispatchForm.runTimes = [null]
      }
    },
    onOpen() {},
    addRange() {
      this.dispatchForm.runRange.push({
        beginTime: '',
        endTime: '',
      })
    },
    addTime() {
      this.dispatchForm.runTimes.push(null)
    },
    removeRange(idx) {
      this.dispatchForm.runRange.splice(idx, 1)
    },
    removeTime(idx) {
      this.dispatchForm.runTimes.splice(idx, 1)
    },
    onClose() {
      this.$refs['dispatchForm'].resetFields()
    },
    close() {
      this.$emit('update:visible', false)
    },
    save() {
      getExcelDsPreviewDatas().then(([err, res]) => {
        if (res.data.length > 0) {
          this.tableData = res.data
        }
      })
    },
    handelConfirm() {
      let flag = true
      this.$refs['dispatchForm'].validate((valid) => {
        if (!valid) {
          flag = false
        }
      })
      this.$refs['callbackForm'].validate((valid) => {
        if (!valid) {
          flag = false
        }
      })

      if (!flag) {
        return false
      }

      this.$emit('update', null, { dispatchForm: this.dispatchForm, callbackForm: this.callbackForm })
      this.close()
    },
    handleExecuteDateChange(value) {
      if (this.dispatchForm.endDate) {
        let executeDate = new Date(this.dispatchForm.beginDate)
        let endDate = new Date(this.dispatchForm.endDate)
        executeDate.setDate(executeDate.getDate() - 1)
        if (endDate.getTime() <= executeDate.getTime()) {
          this.$set(this.dispatchForm, 'endDate', '')
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.el-form {
  height: max-content;

  &:not(:last-child) {
    margin-bottom: 24px;
    border-bottom: 1px solid $borderColor;
  }
}

.range-item {
  > div {
    + div,
    + button {
      margin-left: 8px;
    }
  }

  + .range-item {
    margin-top: 12px;
  }
}
</style>
